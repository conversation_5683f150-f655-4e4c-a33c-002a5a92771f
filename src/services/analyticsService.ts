// src/services/analyticsService.ts
import { Op, QueryTypes } from 'sequelize';
import { sequelize } from '../config/db';
import { 
  User, 
  UserWallet, 
  GameHistory, 
  WalletHistory, 
  UserTaskComplete, 
  TaskConfig,
  UserTaskStatus,
  Chest,
  UserDailyClaim,
  FarmPlot,
  DeliveryLine,
  Booster,
  ActiveBooster,
  IapPurchase,
  PhrsDeposit
} from '../models';
import { logger } from '../utils/logger';
import { CacheService } from './cacheService';

export interface DateRangeQuery {
  startDate?: string;
  endDate?: string;
  days?: number;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

export interface AnalyticsResponse<T> {
  success: boolean;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  metadata: {
    generatedAt: string;
    queryTime: number;
    cached: boolean;
    dataRange?: {
      startDate: string;
      endDate: string;
    };
  };
}

export class AnalyticsService {
  private static cacheService = new CacheService();

  /**
   * 解析日期范围参数
   */
  private static parseDateRange(query: DateRangeQuery): { startDate: Date; endDate: Date } {
    const endDate = query.endDate ? new Date(query.endDate) : new Date();
    let startDate: Date;

    if (query.startDate) {
      startDate = new Date(query.startDate);
    } else if (query.days) {
      startDate = new Date();
      startDate.setDate(startDate.getDate() - query.days);
    } else {
      // 默认查询最近7天
      startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
    }

    // 设置时间为当天的开始和结束
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);

    return { startDate, endDate };
  }

  /**
   * 生成缓存键
   */
  private static generateCacheKey(prefix: string, params: any): string {
    const paramString = JSON.stringify(params);
    return `analytics:${prefix}:${Buffer.from(paramString).toString('base64')}`;
  }

  /**
   * 执行带缓存的查询
   */
  private static async executeWithCache<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    ttl: number = 300 // 默认5分钟缓存
  ): Promise<{ data: T; cached: boolean; queryTime: number }> {
    const startTime = Date.now();
    
    // 尝试从缓存获取
    const cachedResult = await this.cacheService.get<T>(cacheKey);
    if (cachedResult) {
      return {
        data: cachedResult,
        cached: true,
        queryTime: Date.now() - startTime
      };
    }

    // 执行查询
    const result = await queryFn();
    const queryTime = Date.now() - startTime;

    // 存入缓存
    await this.cacheService.set(cacheKey, result, ttl);

    return {
      data: result,
      cached: false,
      queryTime
    };
  }

  /**
   * 用户活跃度指标 - 每日游戏启动总次数
   */
  static async getDailyGameStarts(query: DateRangeQuery): Promise<AnalyticsResponse<any[]>> {
    const { startDate, endDate } = this.parseDateRange(query);
    const cacheKey = this.generateCacheKey('daily_game_starts', { startDate, endDate });

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const result = await sequelize.query(`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as game_starts
          FROM game_histories 
          WHERE created_at BETWEEN :startDate AND :endDate
          GROUP BY DATE(created_at)
          ORDER BY date ASC
        `, {
          replacements: { startDate, endDate },
          type: QueryTypes.SELECT
        });
        return result;
      }
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached,
        dataRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    };
  }

  /**
   * 用户活跃度指标 - 每日APP打开总次数
   */
  static async getDailyAppOpens(query: DateRangeQuery): Promise<AnalyticsResponse<any[]>> {
    const { startDate, endDate } = this.parseDateRange(query);
    const cacheKey = this.generateCacheKey('daily_app_opens', { startDate, endDate });

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        // 基于用户最后活跃时间统计APP打开次数
        const result = await sequelize.query(`
          SELECT 
            DATE(last_active_time) as date,
            COUNT(DISTINCT user_id) as app_opens
          FROM user_wallets 
          WHERE last_active_time BETWEEN :startDate AND :endDate
          GROUP BY DATE(last_active_time)
          ORDER BY date ASC
        `, {
          replacements: { startDate, endDate },
          type: QueryTypes.SELECT
        });
        return result;
      }
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached,
        dataRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    };
  }

  /**
   * 用户活跃度指标 - 玩家数量每日新增量
   */
  static async getDailyNewPlayers(query: DateRangeQuery): Promise<AnalyticsResponse<any[]>> {
    const { startDate, endDate } = this.parseDateRange(query);
    const cacheKey = this.generateCacheKey('daily_new_players', { startDate, endDate });

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const result = await sequelize.query(`
          SELECT 
            DATE(created_at) as date,
            COUNT(*) as new_players
          FROM users 
          WHERE created_at BETWEEN :startDate AND :endDate
          GROUP BY DATE(created_at)
          ORDER BY date ASC
        `, {
          replacements: { startDate, endDate },
          type: QueryTypes.SELECT
        });
        return result;
      }
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached,
        dataRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    };
  }

  /**
   * 用户活跃度指标 - 7日活跃玩家总量
   */
  static async getWeeklyActivePlayers(query: DateRangeQuery = {}): Promise<AnalyticsResponse<any>> {
    const endDate = query.endDate ? new Date(query.endDate) : new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 7);
    
    const cacheKey = this.generateCacheKey('weekly_active_players', { startDate, endDate });

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const result = await sequelize.query(`
          SELECT COUNT(DISTINCT user_id) as active_players_7d
          FROM user_wallets 
          WHERE last_active_time BETWEEN :startDate AND :endDate
        `, {
          replacements: { startDate, endDate },
          type: QueryTypes.SELECT
        });
        return result[0];
      }
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached,
        dataRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    };
  }

  /**
   * 用户活跃度指标 - 每日玩家平均游玩时长
   */
  static async getDailyAveragePlaytime(query: DateRangeQuery): Promise<AnalyticsResponse<any[]>> {
    const { startDate, endDate } = this.parseDateRange(query);
    const cacheKey = this.generateCacheKey('daily_average_playtime', { startDate, endDate });

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        // 基于游戏会话计算平均游玩时长
        const result = await sequelize.query(`
          SELECT
            DATE(s.created_at) as date,
            AVG(TIMESTAMPDIFF(MINUTE, s.created_at, s.updated_at)) as avg_playtime_minutes,
            COUNT(DISTINCT s.user_id) as active_players
          FROM sessions s
          WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.updated_at IS NOT NULL
          GROUP BY DATE(s.created_at)
          ORDER BY date ASC
        `, {
          replacements: { startDate, endDate },
          type: QueryTypes.SELECT
        });
        return result;
      }
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached,
        dataRange: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString()
        }
      }
    };
  }

  /**
   * 游戏进度指标 - 区域解锁人数分布
   */
  static async getUnlockedAreasDistribution(): Promise<AnalyticsResponse<any[]>> {
    const cacheKey = this.generateCacheKey('unlocked_areas_distribution', {});

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const result = await sequelize.query(`
          SELECT
            area_id,
            COUNT(DISTINCT user_id) as unlocked_players
          FROM farm_plots
          WHERE area_id BETWEEN 2 AND 20
          GROUP BY area_id
          ORDER BY area_id ASC
        `, {
          type: QueryTypes.SELECT
        });
        return result;
      },
      600 // 10分钟缓存，因为区域解锁变化较慢
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached
      }
    };
  }

  /**
   * 游戏进度指标 - 流水线升级等级分布
   */
  static async getDeliveryLineLevelsDistribution(): Promise<AnalyticsResponse<any[]>> {
    const cacheKey = this.generateCacheKey('delivery_line_levels_distribution', {});

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const targetLevels = [10, 20, 25, 30, 35, 40, 45, 50];
        const results = [];

        for (const level of targetLevels) {
          const result = await sequelize.query(`
            SELECT
              :level as target_level,
              COUNT(DISTINCT user_id) as players_count
            FROM delivery_lines
            WHERE level >= :level
          `, {
            replacements: { level },
            type: QueryTypes.SELECT
          });
          results.push(result[0]);
        }
        return results;
      },
      600 // 10分钟缓存
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached
      }
    };
  }

  /**
   * 游戏进度指标 - 牧场区域升级分布
   */
  static async getFarmAreasLevelsDistribution(): Promise<AnalyticsResponse<any[]>> {
    const cacheKey = this.generateCacheKey('farm_areas_levels_distribution', {});

    const { data, cached, queryTime } = await this.executeWithCache(
      cacheKey,
      async () => {
        const targetLevels = [10, 20, 25, 30, 35, 40, 45, 50];
        const results = [];

        for (let areaId = 1; areaId <= 20; areaId++) {
          const areaStats = { area_id: areaId, level_distribution: [] };

          for (const level of targetLevels) {
            const result = await sequelize.query(`
              SELECT
                COUNT(DISTINCT user_id) as players_count
              FROM farm_plots
              WHERE area_id = :areaId AND level >= :level
            `, {
              replacements: { areaId, level },
              type: QueryTypes.SELECT
            });

            areaStats.level_distribution.push({
              target_level: level,
              players_count: result[0].players_count
            });
          }
          results.push(areaStats);
        }
        return results;
      },
      600 // 10分钟缓存
    );

    return {
      success: true,
      data,
      metadata: {
        generatedAt: new Date().toISOString(),
        queryTime,
        cached
      }
    };
  }
}

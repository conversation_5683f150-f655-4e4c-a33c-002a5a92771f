// src/routes/adminAnalyticsRoutes.ts
import { Router } from 'express';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { adminAuthMiddleware, rateLimitMiddleware } from '../middlewares/adminAuth';
import * as analyticsController from '../controllers/adminAnalyticsController';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 应用管理员认证中间件到所有路由
router.use(adminAuthMiddleware);

// 应用频率限制中间件（每分钟最多60次请求）
router.use(rateLimitMiddleware(60, 60000));

/**
 * 用户活跃度指标 API
 */
// 每日游戏启动总次数
router.get('/user-activity/daily-game-starts', analyticsController.getDailyGameStarts);

// 每日APP打开总次数  
router.get('/user-activity/daily-app-opens', analyticsController.getDailyAppOpens);

// 玩家数量每日新增量
router.get('/user-activity/daily-new-players', analyticsController.getDailyNewPlayers);

// 7日活跃玩家总量
router.get('/user-activity/weekly-active-players', analyticsController.getWeeklyActivePlayers);

// 每日玩家平均游玩时长
router.get('/user-activity/daily-average-playtime', analyticsController.getDailyAveragePlaytime);

/**
 * 游戏进度指标 API
 */
// 区域解锁人数分布
router.get('/game-progress/unlocked-areas-distribution', analyticsController.getUnlockedAreasDistribution);

// 流水线升级等级分布
router.get('/game-progress/delivery-line-levels-distribution', analyticsController.getDeliveryLineLevelsDistribution);

// 牧场区域升级分布
router.get('/game-progress/farm-areas-levels-distribution', analyticsController.getFarmAreasLevelsDistribution);

/**
 * 道具使用指标 API
 */
// 每种道具的使用玩家总数
router.get('/item-usage/unique-users-per-item', analyticsController.getUniqueUsersPerItem);

// 每种道具的总使用次数
router.get('/item-usage/total-usage-per-item', analyticsController.getTotalUsagePerItem);

/**
 * 任务完成指标 API
 */
// 玩家完成任务总次数
router.get('/task-completion/total-completions', analyticsController.getTotalTaskCompletions);

// 每个任务ID的完成人数
router.get('/task-completion/completions-per-task', analyticsController.getCompletionsPerTask);

/**
 * 社交功能指标 API
 */
// 玩家邀请好友总数
router.get('/social-features/total-invitations', analyticsController.getTotalInvitations);

// 邀请好友数量分布
router.get('/social-features/invitation-distribution', analyticsController.getInvitationDistribution);

/**
 * 奖励系统指标 API
 */
// 玩家获取箱子总数
router.get('/reward-system/total-chests-obtained', analyticsController.getTotalChestsObtained);

// 每日宝箱领取人数
router.get('/reward-system/daily-chest-claimers', analyticsController.getDailyChestClaimers);

// 玩家获取钻石总数
router.get('/reward-system/total-diamonds-obtained', analyticsController.getTotalDiamondsObtained);

// 玩家获取GEM总数
router.get('/reward-system/total-gems-obtained', analyticsController.getTotalGemsObtained);

/**
 * 付费指标 API
 */
// 玩家总充值金额
router.get('/payment/total-revenue', analyticsController.getTotalRevenue);

/**
 * 留存率指标 API
 */
// 首日留存率
router.get('/retention/day-one-retention', analyticsController.getDayOneRetention);

// 七日留存率
router.get('/retention/day-seven-retention', analyticsController.getDaySevenRetention);

/**
 * 综合面板 API
 */
// 获取综合统计面板数据
router.get('/dashboard/overview', analyticsController.getDashboardOverview);

// 自定义查询接口（支持多指标组合查询）
router.post('/dashboard/custom-query', analyticsController.getCustomQuery);

/**
 * 数据导出 API
 */
// 导出指定指标的数据为CSV
router.post('/export/csv', analyticsController.exportToCSV);

// 导出指定指标的数据为Excel
router.post('/export/excel', analyticsController.exportToExcel);

/**
 * 实时数据 API
 */
// 获取实时在线用户数
router.get('/realtime/online-users', analyticsController.getRealtimeOnlineUsers);

// 获取实时游戏活动统计
router.get('/realtime/game-activity', analyticsController.getRealtimeGameActivity);

export default router;

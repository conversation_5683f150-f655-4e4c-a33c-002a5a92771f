# Docker Compose 生产环境配置
# 使用方法: docker compose -f docker-compose.pharos-test.yml -f docker-compose.pharos-test.prod.yml up

services:
  app:
    # 生产环境配置
    environment:
      NODE_ENV: production
      DEBUG: "false"
      LOG_LEVEL: info
    
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    
    # 生产环境重启策略
    restart: unless-stopped
    
    # 生产环境健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3456/api/health/ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

  mysql:
    # 生产环境数据库配置
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-00321zixun}
      MYSQL_DATABASE: pharos_test_db
      MYSQL_USER: pharos_test
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-00321zixunadmin}
      TZ: Asia/Shanghai
    
    # 生产环境卷挂载
    volumes:
      - mysql_data_pharos_test_prod:/var/lib/mysql
      - ./mysqld.cnf:/etc/mysql/conf.d/mysqld.cnf
      - ./logs/mysql:/var/log/mysql
    
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 1.5G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # 生产环境重启策略
    restart: unless-stopped
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

  redis:
    # 生产环境 Redis 配置
    command: redis-server --requirepass ${REDIS_PASSWORD:-joetest1123} --appendonly yes --save 900 1 --save 300 10 --save 60 10000
    
    # 生产环境卷挂载
    volumes:
      - redis_data_pharos_test_prod:/data
      - ./logs/redis:/var/log/redis
    
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    
    # 生产环境重启策略
    restart: unless-stopped
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

  phpmyadmin:
    # 生产环境 phpMyAdmin 配置
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql
      UPLOAD_LIMIT: 100M
      MEMORY_LIMIT: 512M
    
    # 生产环境资源限制
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    
    # 生产环境重启策略
    restart: unless-stopped
    
    # 生产环境日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "2"

  # 生产环境监控服务
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower-pharos-test
    environment:
      WATCHTOWER_CLEANUP: "true"
      WATCHTOWER_POLL_INTERVAL: 3600
      WATCHTOWER_INCLUDE_STOPPED: "true"
      WATCHTOWER_REVIVE_STOPPED: "false"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped
    networks:
      - pharos_test

# 生产环境数据卷
volumes:
  mysql_data_pharos_test_prod:
    driver: local
  redis_data_pharos_test_prod:
    driver: local

const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config();

async function runSafeMigration(migrationFileName) {
  console.log(`🔧 运行安全迁移: ${migrationFileName}`);
  
  try {
    const env = process.env.NODE_ENV || 'development';
    const config = require('./config/config.js')[env];
    
    let sequelize;
    if (config.use_env_variable) {
      sequelize = new Sequelize(process.env[config.use_env_variable], config);
    } else {
      sequelize = new Sequelize(config.database, config.username, config.password, config);
    }
    
    // 测试数据库连接
    console.log('🔗 测试数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 获取 QueryInterface
    const queryInterface = sequelize.getQueryInterface();
    
    // 导入迁移文件
    const migrationPath = path.join(__dirname, 'migrations', migrationFileName);
    const migration = require(migrationPath);
    
    console.log(`📝 执行迁移: ${migrationFileName}`);
    await migration.up(queryInterface, Sequelize);
    console.log('✅ 迁移执行成功');
    
    await sequelize.close();
    console.log('✅ 迁移完成，数据库连接已关闭');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error.message);
    console.error('错误详情:', error.stack);
    process.exit(1);
  }
}

// 从命令行参数获取迁移文件名
const migrationFileName = process.argv[2];

if (!migrationFileName) {
  console.error('❌ 请提供迁移文件名');
  console.log('用法: node run_safe_migration.js <migration-file-name>');
  console.log('例如: node run_safe_migration.js 20250115000000-add-status-checked-to-iap-purchases.js');
  process.exit(1);
}

// 如果直接运行此文件，则执行迁移
if (require.main === module) {
  runSafeMigration(migrationFileName);
}

module.exports = { runSafeMigration };

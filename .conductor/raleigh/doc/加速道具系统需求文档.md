# 加速道具系统需求文档

## 📋 概述

本文档基于加速道具规则文档，结合当前系统实现情况，制定详细的开发需求和改进计划。

## 🎯 核心需求

### 1. 道具分类体系

#### 1.1 道具类型
- **时间加速道具**：提升生产和出货速度
  - 2倍加速道具
  - 4倍加速道具
- **收益加倍道具**：提升最终收益（牛奶价格）
  - 2倍收益道具  
  - 4倍收益道具

#### 1.2 道具属性
- **倍数**：2倍或4倍
- **持续时间**：以小时为单位
- **叠加时间**：同类型同倍数道具可叠加，最大999天
- **互斥规则**：同类型道具同时间只能激活一个

### 2. 加速算法规则

#### 2.1 基础算法
```
最终效果值 = 基础值 × 时间加速倍数 × 收益加倍倍数
```

#### 2.2 时间加速影响范围
- 牧场区域生产速度
- 配送线出货速度  
- 界面动画速度

#### 2.3 收益加倍影响范围
- 配送线牛奶价格
- 最终宝石产出

#### 2.4 离线收益计算
```
离线收益 = 基础离线产出 × 时间加速倍数 × 收益加倍倍数
```

**重要规则：**
- 只有道具作用时间内的收益会增加
- 超出时间正常计算
- 离线收益上限24小时
- 如果道具时间超过24小时，也只能扣除24小时的道具时间

### 3. UI界面需求

#### 3.1 主界面显示
- **加速按钮**：进入道具管理界面
- **加速状态显示区域**：
  - 显示当前激活道具的图标
  - 显示剩余时间（时:分:秒格式）
  - 超过99小时显示"加速中...."
  - 排序规则：时间加速 > 收益加倍，从左到右排列
  - 只有激活状态的道具才显示

#### 3.2 道具管理界面
- **道具列表**：
  - 时间加速道具（2倍、4倍两个条目）
  - 收益加倍道具（2倍、4倍两个条目）
  - 显示道具图标和拥有时间
- **操作按钮**：
  - 激活按钮：激活对应道具
  - 暂停按钮：暂停当前道具
  - 一键激活：激活当前拥有的最高倍数道具
  - 购买按钮：跳转商城
- **状态显示**：
  - 无道具时显示："暂无道具请前往商城购买"
  - 只有一个条目时居中显示

## 🔍 当前实现状态分析

### ✅ 已实现功能
1. **数据模型**：`Booster`和`ActiveBooster`表
2. **互斥检查**：`BoosterMutexService`服务
3. **效果应用**：在农场和配送线中应用速度加成
4. **API接口**：完整的道具管理API

### ❌ 存在问题

#### 1. 道具类型不匹配
- **当前**：`speed_boost` + `time_warp`
- **需求**：`speed_boost` + `income_boost`
- **问题**：`time_warp`是立即时间跳跃，不是收益加倍

#### 2. 收益加倍功能缺失
- 当前只有速度加成，缺少价格加倍逻辑
- 需要在配送线价格计算中实现收益倍数

#### 3. UI功能完全缺失
- 缺少主界面加速状态显示
- 缺少道具管理界面
- 缺少一键激活等交互功能

#### 4. 时间显示格式不规范
- 需要实现"时:分:秒"标准格式
- 需要处理超长时间显示

#### 5. 离线奖励计算需完善
- 验证道具剩余时间计算逻辑
- 确认24小时上限实现

## 🚀 开发计划

### 阶段一：核心数据模型调整

#### 1.1 道具类型重构
- [ ] 将`time_warp`类型改为`income_boost`
- [ ] 更新相关枚举和验证逻辑
- [ ] 数据库迁移脚本

#### 1.2 收益倍数逻辑实现
- [ ] 在`iapController.getBoosterEffects()`中添加收益倍数
- [ ] 在配送线服务中实现价格倍数计算
- [ ] 更新离线奖励计算逻辑

### 阶段二：算法逻辑完善

#### 2.1 时间加速逻辑优化
- [ ] 统一各服务中的速度倍数计算
- [ ] 确保农场区和配送线都应用加速效果
- [ ] 验证VIP和道具效果的正确叠加

#### 2.2 离线奖励算法完善  
- [ ] 实现道具剩余时间的精确计算
- [ ] 实现24小时上限逻辑
- [ ] 添加调试日志和测试用例

### 阶段三：UI界面开发

#### 3.1 主界面加速状态
- [ ] 设计加速状态显示组件
- [ ] 实现时间格式化函数
- [ ] 实现超长时间特殊显示
- [ ] 实现排序逻辑

#### 3.2 道具管理界面
- [ ] 设计道具列表UI
- [ ] 实现激活/暂停功能
- [ ] 实现一键激活功能
- [ ] 集成商城跳转

### 阶段四：测试和优化

#### 4.1 功能测试
- [ ] 单元测试：各种道具效果计算
- [ ] 集成测试：完整游戏流程
- [ ] 边界测试：极端情况处理

#### 4.2 性能优化
- [ ] 数据库查询优化
- [ ] 缓存策略实现
- [ ] 前端性能优化

## 📊 技术实现细节

### 1. 数据模型变更

#### Booster模型更新
```typescript
type: 'speed_boost' | 'income_boost'  // 修改类型定义
```

#### ActiveBooster模型保持不变
现有结构已满足需求

### 2. 服务层改造

#### iapController.getBoosterEffects()扩展
```typescript
return {
  speedMultiplier: number,      // 速度倍数
  incomeMultiplier: number,     // 收益倍数  
  hasActiveBooster: boolean
}
```

#### 配送线服务更新
```typescript
// 添加收益倍数计算
const finalPrice = basePrice * vipPriceMultiplier * boosterIncomeMultiplier;
```

### 3. 时间处理工具

#### 时间格式化函数
```typescript
formatBoosterTime(endTime: Date): string {
  // 实现"时:分:秒"格式
  // 处理超过99小时的情况
}
```

### 4. UI组件设计

#### 加速状态组件
- 道具图标显示
- 倒计时显示
- 排序和布局

#### 道具管理组件  
- 道具列表
- 操作按钮
- 状态提示

## 🎯 验收标准

### 功能验收
- [ ] 时间加速道具正确影响生产和出货速度
- [ ] 收益加倍道具正确影响牛奶价格
- [ ] 同类型道具互斥规则正确执行
- [ ] 道具时间叠加功能正常
- [ ] 离线奖励正确计算道具效果

### UI验收
- [ ] 主界面正确显示激活道具状态
- [ ] 时间显示格式符合规范
- [ ] 道具管理界面功能完整
- [ ] 一键激活功能正常

### 性能验收
- [ ] 道具效果计算性能良好
- [ ] UI响应流畅
- [ ] 数据库查询优化

## 📝 相关文档

- 原始规则文档：`/Users/<USER>/Desktop/xinxuqu/加速道具规则.docx`
- 当前实现代码：
  - `src/models/Booster.ts`
  - `src/models/ActiveBooster.ts`  
  - `src/services/boosterMutexService.ts`
  - `src/controllers/iapController.ts`

---

**文档创建时间**：2025年7月25日  
**最后更新**：2025年7月25日  
**状态**：需求确认中

# 农场配置系统部署指南

## 📋 概述

本文档提供农场配置系统从硬编码配置到动态数据库配置的完整部署指南。

## 🎯 部署目标

- ✅ 将硬编码的农场升级配置迁移到数据库
- ✅ 提供Excel文件上传接口，支持配置动态更新
- ✅ 实现配置版本管理和回滚功能
- ✅ 保持现有API兼容性，零停机部署
- ✅ 添加Redis缓存，提升性能

## 🔧 环境要求

### 系统要求
- Node.js 22.x
- MySQL 8.0+
- Redis 6.0+
- 至少 2GB 可用内存
- 至少 10GB 可用磁盘空间

### 依赖包
```json
{
  "xlsx": "^0.18.5",
  "multer": "^1.4.5",
  "dayjs": "^1.11.7"
}
```

## 📦 部署步骤

### 第一步：备份现有数据

```bash
# 1. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 备份代码
cp -r wolf_fun wolf_fun_backup_$(date +%Y%m%d_%H%M%S)

# 3. 备份Redis数据（如果需要）
redis-cli BGSAVE
```

### 第二步：部署新代码

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖
npm install

# 3. 编译TypeScript（如果需要）
npm run build
```

### 第三步：数据库迁移

```bash
# 1. 运行数据库迁移
npm run migrate

# 或者手动运行迁移脚本
node scripts/migrate-farm-config.js
```

### 第四步：数据初始化

```bash
# 运行数据初始化脚本
node scripts/migrate-farm-config.js
```

### 第五步：验证部署

```bash
# 1. 运行迁移验证
node scripts/validate-migration.js

# 2. 运行完整测试套件
node scripts/run-all-farm-config-tests.js

# 3. 运行性能测试
node scripts/performance-test-farm-config.js
```

### 第六步：启动服务

```bash
# 1. 启动应用
npm start

# 或者使用PM2
pm2 start ecosystem.config.js
```

## 🔍 部署验证清单

### 数据库验证
- [ ] `farm_configs` 表已创建
- [ ] `farm_config_versions` 表已创建
- [ ] 默认配置数据已导入（51条记录，等级0-50）
- [ ] 默认版本已激活
- [ ] 数据库索引已创建

### 功能验证
- [ ] 配置查询API正常工作
- [ ] Excel上传功能正常
- [ ] 配置版本管理功能正常
- [ ] 缓存功能正常工作
- [ ] 管理员权限验证正常

### 性能验证
- [ ] 配置查询响应时间 < 50ms
- [ ] 缓存命中率 > 90%
- [ ] 并发处理能力满足要求
- [ ] 内存使用正常

### 兼容性验证
- [ ] 现有游戏API接口正常工作
- [ ] 配置函数返回正确数据
- [ ] 新旧等级系统兼容
- [ ] 降级方案可用

## 🚨 回滚方案

如果部署出现问题，可以按以下步骤回滚：

### 快速回滚（代码回滚）
```bash
# 1. 停止服务
pm2 stop all

# 2. 恢复代码
rm -rf wolf_fun
mv wolf_fun_backup_YYYYMMDD_HHMMSS wolf_fun

# 3. 重启服务
cd wolf_fun
npm start
```

### 完整回滚（包含数据库）
```bash
# 1. 停止服务
pm2 stop all

# 2. 恢复数据库
mysql -u username -p database_name < backup_YYYYMMDD_HHMMSS.sql

# 3. 恢复代码
rm -rf wolf_fun
mv wolf_fun_backup_YYYYMMDD_HHMMSS wolf_fun

# 4. 重启服务
cd wolf_fun
npm start
```

## 📊 监控和维护

### 关键指标监控

1. **配置查询性能**
   - 响应时间：< 50ms
   - 错误率：< 1%
   - QPS：根据业务需求

2. **缓存性能**
   - 命中率：> 90%
   - 内存使用：< 500MB
   - 过期策略：1小时TTL

3. **数据库性能**
   - 连接数：< 100
   - 慢查询：< 1%
   - 存储空间：监控增长

### 日常维护任务

1. **每日检查**
   - 检查应用日志
   - 监控性能指标
   - 验证配置数据完整性

2. **每周检查**
   - 清理过期缓存
   - 检查数据库性能
   - 备份配置数据

3. **每月检查**
   - 分析配置使用情况
   - 优化数据库索引
   - 更新监控阈值

## 🔧 故障排除

### 常见问题

1. **配置查询失败**
   ```bash
   # 检查数据库连接
   mysql -u username -p -e "SELECT COUNT(*) FROM farm_configs WHERE isActive = 1;"
   
   # 检查缓存状态
   redis-cli GET farm_config:active
   ```

2. **Excel上传失败**
   ```bash
   # 检查文件权限
   ls -la uploads/
   
   # 检查磁盘空间
   df -h
   ```

3. **性能问题**
   ```bash
   # 检查数据库慢查询
   mysql -u username -p -e "SHOW PROCESSLIST;"
   
   # 检查Redis内存使用
   redis-cli INFO memory
   ```

### 错误代码对照表

| 错误代码 | 描述 | 解决方案 |
|---------|------|----------|
| FC001 | 配置数据不存在 | 运行数据初始化脚本 |
| FC002 | 版本激活失败 | 检查数据库事务 |
| FC003 | 缓存连接失败 | 检查Redis服务 |
| FC004 | Excel解析失败 | 检查文件格式 |
| FC005 | 权限验证失败 | 检查管理员配置 |

## 📞 支持联系

如果在部署过程中遇到问题，请联系：

- **技术支持**：开发团队
- **紧急联系**：系统管理员
- **文档更新**：请提交PR到项目仓库

## 📝 更新日志

### v1.0.0 (2025-07-21)
- 初始版本发布
- 支持动态配置管理
- 实现Excel上传功能
- 添加版本管理和回滚
- 集成Redis缓存
- 保持API兼容性

---

**注意**：请在生产环境部署前，务必在测试环境完整验证所有功能。

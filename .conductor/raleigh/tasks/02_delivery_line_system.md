# 任务：出货线（Delivery Line）系统实现

## 任务描述
实现出货线（Delivery Line）系统，作为Moofun奶牛农场游戏的核心变现设施。出货线将累积的牛奶自动打包成「牛奶方塊」（Milk Block），并将其出售获得宝石（Gem）收入。

## 功能需求

1. **出货线基础属性**
   - 出货速度：每次出货所需时间（秒）
   - 方块单位：每个牛奶方块包含的牛奶数量
   - 方块价格：每个牛奶方块出售获得的GEM数量
   - 升级费用：提升出货线等级所需的GEM

2. **初始设定**
   - 出货速度：1秒/次
   - 方块单位：5牛奶/方块
   - 方块价格：5 GEM/方块
   - 初始升级费用：500 GEM

3. **升级机制**
   - 每次升级提升出货线等级+1
   - 每次升级提升方块单位2.0倍
   - 每次升级提升方块价格2.0倍
   - 每次升级提升出货速度1%（秒数 / 1.01）
   - 每次升级提升下次升级费用2.0倍

4. **自动出货机制**
   - 系统自动将累积的牛奶打包成牛奶方块
   - 当牛奶数量达到方块单位要求时，自动创建牛奶方块
   - 按照设定的出货速度，自动出售牛奶方块获得GEM

## 技术实现要点

1. **数据模型设计**
   - 创建DeliveryLine模型，包含所有必要属性
   - 设计用户与出货线的关联关系
   - 实现出货线状态的持久化存储

2. **升级逻辑**
   - 实现升级计算公式
   - 确保GEM消费正确记录

3. **出货循环系统**
   - 实现基于时间的自动出货循环
   - 确保离线时间的出货计算准确
   - 实现GEM收入的累积机制

4. **API设计**
   - 设计出货线数据接口
   - 实现升级接口
   - 提供出货状态和参数查询接口

## 验收标准

1. 出货线系统功能完整，包括升级和自动出货
2. 升级机制正确计算并应用
3. 自动出货循环正常运行，GEM收入计算准确
4. API接口设计合理，提供完整的数据访问能力
5. 系统能够正确处理边界情况（如牛奶不足）
6. 性能良好，支持高并发访问

## 优先级
高 - 作为游戏核心变现系统
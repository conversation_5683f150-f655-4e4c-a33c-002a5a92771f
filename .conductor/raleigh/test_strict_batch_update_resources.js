// 严格验证批量资源更新接口测试脚本
// 测试三项验证逻辑：牛奶产量验证、牛奶消耗验证、宝石增加验证

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg'; // 有效的JWT token

// 请求配置
const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 测试用例
const testCases = [
  {
    name: '正常请求 - 应该通过严格验证',
    request: {
      gemRequest: 5.000,
      milkOperations: {
        produce: 10.000,
        consume: 5.000
      }
    },
    expectedValidation: true
  },
  {
    name: '牛奶产量超限 - 应该触发验证失败',
    request: {
      gemRequest: 5.000,
      milkOperations: {
        produce: 1000.000, // 故意设置很大的值
        consume: 5.000
      }
    },
    expectedValidation: false
  },
  {
    name: '牛奶消耗超限 - 应该触发验证失败',
    request: {
      gemRequest: 5.000,
      milkOperations: {
        produce: 10.000,
        consume: 1000.000 // 故意设置很大的值
      }
    },
    expectedValidation: false
  },
  {
    name: '宝石增加超限 - 应该触发验证失败',
    request: {
      gemRequest: 1000.000, // 故意设置很大的值
      milkOperations: {
        produce: 10.000,
        consume: 5.000
      }
    },
    expectedValidation: false
  },
  {
    name: '多项验证失败 - 应该触发验证失败',
    request: {
      gemRequest: 1000.000,
      milkOperations: {
        produce: 1000.000,
        consume: 1000.000
      }
    },
    expectedValidation: false
  },
  {
    name: '只请求GEM - 应该通过验证',
    request: {
      gemRequest: 3.000
    },
    expectedValidation: true
  },
  {
    name: '只请求牛奶操作 - 应该通过验证',
    request: {
      milkOperations: {
        produce: 8.000,
        consume: 3.000
      }
    },
    expectedValidation: true
  }
];

// 执行测试
async function runTests() {
  console.log('🚀 开始测试严格验证批量资源更新接口');
  console.log('='.repeat(60));
  
  // 首先调用旧接口获取基准数据
  console.log('📊 获取基准数据（调用旧接口）...');
  try {
    const baselineResponse = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      {
        gemRequest: 1.000,
        milkOperations: {
          produce: 1.000,
          consume: 0.500
        }
      },
      config
    );
    
    if (baselineResponse.data.ok) {
      console.log('✅ 基准数据获取成功');
      console.log(`   时间间隔: ${baselineResponse.data.data.changes.productionRates.timeElapsedSeconds}秒`);
      console.log(`   农场产量: ${baselineResponse.data.data.changes.productionRates.farmMilkPerCycle}`);
      console.log(`   出货单位: ${baselineResponse.data.data.changes.productionRates.deliveryBlockUnit}`);
      console.log(`   出货价格: ${baselineResponse.data.data.changes.productionRates.deliveryBlockPrice}`);
    }
  } catch (error) {
    console.log('⚠️  基准数据获取失败，继续测试...');
  }
  
  console.log('\n' + '='.repeat(60));
  
  // 等待5秒确保时间窗口有效
  console.log('⏳ 等待5秒确保时间窗口有效...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // 执行测试用例
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n🧪 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log('-'.repeat(50));
    
    try {
      const response = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        testCase.request,
        config
      );
      
      if (response.data.ok) {
        const data = response.data.data;
        const changes = data.changes;
        
        console.log('✅ 请求成功');
        console.log(`   消息: ${response.data.message}`);
        console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
        console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
        console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
        
        // 显示验证详情
        if (changes.strictValidationDetails) {
          const details = changes.strictValidationDetails;
          console.log('   验证详情:');
          console.log(`     牛奶产量验证: ${details.milkProductionValid ? '✅' : '❌'}`);
          console.log(`     牛奶消耗验证: ${details.milkConsumptionValid ? '✅' : '❌'}`);
          console.log(`     宝石转换验证: ${details.gemConversionValid ? '✅' : '❌'}`);
          
          if (details.reason) {
            console.log(`     失败原因: ${details.reason}`);
          }
          
          // 显示具体数值
          console.log('   验证数值:');
          console.log(`     牛奶产量 - 请求:${details.validationDetails.milkProduction.requested.toFixed(3)} 允许:${details.validationDetails.milkProduction.maxAllowed.toFixed(3)}`);
          console.log(`     牛奶消耗 - 请求:${details.validationDetails.milkConsumption.requested.toFixed(3)} 允许:${details.validationDetails.milkConsumption.maxAllowed.toFixed(3)}`);
          console.log(`     宝石转换 - 请求:${details.validationDetails.gemConversion.requested.toFixed(3)} 允许:${details.validationDetails.gemConversion.maxAllowed.toFixed(3)}`);
          console.log(`     转换汇率: ${details.validationDetails.gemConversion.conversionRate.toFixed(3)}`);
        }
        
        // 显示资源变化
        console.log('   资源变化:');
        console.log(`     GEM: ${data.beforeUpdate.gem} → ${data.afterUpdate.gem} (${changes.details.gem.increased > 0 ? '+' : ''}${changes.details.gem.increased})`);
        console.log(`     牛奶: ${data.beforeUpdate.pendingMilk} → ${data.afterUpdate.pendingMilk} (+${changes.details.milk.increased} -${changes.details.milk.decreased})`);
        
        // 验证预期结果
        const actualValidation = changes.validationPassed;
        if (actualValidation === testCase.expectedValidation) {
          console.log(`✅ 验证结果符合预期: ${actualValidation ? '通过' : '失败'}`);
        } else {
          console.log(`❌ 验证结果不符合预期: 期望${testCase.expectedValidation ? '通过' : '失败'}，实际${actualValidation ? '通过' : '失败'}`);
        }
        
      } else {
        console.log('❌ 请求失败');
        console.log(`   错误: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log('❌ 请求异常');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试间隔，避免频率限制
    if (i < testCases.length - 1) {
      console.log('⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);

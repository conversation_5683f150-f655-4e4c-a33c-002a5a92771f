// 测试修复后的严格验证批量资源更新接口
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';

// 测试用的JWT token（需要替换为有效token）
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJpYXQiOjE3MzYwMDAwMDAsImV4cCI6MTczNjA4NjQwMH0.test_token'; // 请替换为有效token

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 测试时间窗口逻辑
async function testTimeWindowLogic() {
  console.log('🧪 测试时间窗口逻辑...');
  console.log('='.repeat(50));
  
  try {
    // 测试1: 正常请求（应该根据实际时间窗口返回结果）
    console.log('\n📝 测试1: 正常请求');
    const response1 = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      {
        gemRequest: 1.000,
        milkOperations: {
          produce: 2.000,
          consume: 1.000
        }
      },
      config
    );
    
    if (response1.data.ok) {
      const data = response1.data.data;
      const changes = data.changes;
      
      console.log('✅ 请求成功');
      console.log(`   消息: ${response1.data.message}`);
      console.log(`   使用严格验证: ${changes.usedStrictValidation}`);
      console.log(`   验证通过: ${changes.validationPassed}`);
      console.log(`   回退到旧方法: ${changes.fallbackToOldMethod}`);
      console.log(`   时间间隔: ${changes.productionRates.timeElapsedSeconds}秒`);
      
      // 检查新增的时间窗口字段
      if (changes.timeWindowValid !== undefined) {
        console.log(`   时间窗口有效: ${changes.timeWindowValid}`);
        if (changes.timeWindowReason) {
          console.log(`   时间窗口原因: ${changes.timeWindowReason}`);
        }
      }
      
      // 分析时间窗口逻辑
      const timeElapsed = changes.productionRates.timeElapsedSeconds;
      if (timeElapsed < 5) {
        console.log('   📊 分析: 时间间隔太短，应该拒绝请求');
        if (!changes.usedStrictValidation && !changes.validationPassed) {
          console.log('   ✅ 逻辑正确: 未进行严格验证，直接拒绝');
        } else {
          console.log('   ❌ 逻辑可能有问题: 时间太短但仍进行了验证');
        }
      } else if (timeElapsed > 120) {
        console.log('   📊 分析: 用户离线超过2分钟，应该拒绝请求');
        if (!changes.usedStrictValidation && !changes.validationPassed) {
          console.log('   ✅ 逻辑正确: 未进行严格验证，直接拒绝');
        } else {
          console.log('   ❌ 逻辑可能有问题: 用户离线但仍进行了验证');
        }
      } else {
        console.log('   📊 分析: 时间窗口有效，应该进行严格验证');
        if (changes.usedStrictValidation) {
          console.log('   ✅ 逻辑正确: 进行了严格验证');
        } else {
          console.log('   ❌ 逻辑可能有问题: 时间窗口有效但未进行严格验证');
        }
      }
      
      // 显示资源变化
      console.log('   💰 资源变化:');
      console.log(`     GEM: ${data.beforeUpdate.gem} → ${data.afterUpdate.gem} (${changes.details.gem.increased > 0 ? '+' : ''}${changes.details.gem.increased})`);
      console.log(`     牛奶: ${data.beforeUpdate.pendingMilk} → ${data.afterUpdate.pendingMilk} (+${changes.details.milk.increased} -${changes.details.milk.decreased})`);
      
    } else {
      console.log('❌ 请求失败');
      console.log(`   错误: ${response1.data.message}`);
    }
    
  } catch (error) {
    console.log('❌ 请求异常');
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      
      if (error.response.status === 401) {
        console.log('\n💡 提示: JWT token可能无效或已过期');
        console.log('   请更新JWT_TOKEN变量为有效的token');
        return false;
      }
    } else {
      console.log(`   错误: ${error.message}`);
    }
  }
  
  return true;
}

// 测试接口对比
async function compareWithOldAPI() {
  console.log('\n🔄 对比新旧接口行为...');
  console.log('='.repeat(50));
  
  const testRequest = {
    gemRequest: 1.000,
    milkOperations: {
      produce: 2.000,
      consume: 1.000
    }
  };
  
  try {
    // 调用旧接口
    console.log('📝 调用旧接口...');
    const oldResponse = await axios.post(
      `${BASE_URL}/api/wallet/batch-update-resources`,
      testRequest,
      config
    );
    
    // 等待6秒避免频率限制
    console.log('⏳ 等待6秒避免频率限制...');
    await new Promise(resolve => setTimeout(resolve, 6000));
    
    // 调用新接口
    console.log('📝 调用新接口...');
    const newResponse = await axios.post(
      `${BASE_URL}/api/wallet/strict-batch-update-resources`,
      testRequest,
      config
    );
    
    // 对比结果
    console.log('\n📊 对比结果:');
    
    if (oldResponse.data.ok && newResponse.data.ok) {
      const oldData = oldResponse.data.data;
      const newData = newResponse.data.data;
      
      console.log('旧接口:');
      console.log(`   GEM变化: ${oldData.changes.details.gem.increased}`);
      console.log(`   牛奶变化: +${oldData.changes.details.milk.increased} -${oldData.changes.details.milk.decreased}`);
      console.log(`   时间间隔: ${oldData.changes.productionRates.timeElapsedSeconds}秒`);
      
      console.log('新接口:');
      console.log(`   GEM变化: ${newData.changes.details.gem.increased}`);
      console.log(`   牛奶变化: +${newData.changes.details.milk.increased} -${newData.changes.details.milk.decreased}`);
      console.log(`   时间间隔: ${newData.changes.productionRates.timeElapsedSeconds}秒`);
      console.log(`   使用严格验证: ${newData.changes.usedStrictValidation}`);
      console.log(`   验证通过: ${newData.changes.validationPassed}`);
      console.log(`   回退到旧方法: ${newData.changes.fallbackToOldMethod}`);
      
      // 分析差异
      const gemDiff = Math.abs(oldData.changes.details.gem.increased - newData.changes.details.gem.increased);
      const milkProduceDiff = Math.abs(oldData.changes.details.milk.increased - newData.changes.details.milk.increased);
      const milkConsumeDiff = Math.abs(oldData.changes.details.milk.decreased - newData.changes.details.milk.decreased);
      
      console.log('\n📈 差异分析:');
      console.log(`   GEM差异: ${gemDiff.toFixed(3)}`);
      console.log(`   牛奶产量差异: ${milkProduceDiff.toFixed(3)}`);
      console.log(`   牛奶消耗差异: ${milkConsumeDiff.toFixed(3)}`);
      
      if (newData.changes.fallbackToOldMethod) {
        console.log('   ✅ 新接口回退到旧方法，结果应该相似');
      } else if (newData.changes.validationPassed) {
        console.log('   ✅ 新接口验证通过，使用前端请求值');
      } else {
        console.log('   ⚠️  新接口验证失败但未回退，可能是时间窗口问题');
      }
      
    } else {
      console.log('❌ 其中一个接口调用失败');
      if (!oldResponse.data.ok) {
        console.log(`   旧接口错误: ${oldResponse.data.message}`);
      }
      if (!newResponse.data.ok) {
        console.log(`   新接口错误: ${newResponse.data.message}`);
      }
    }
    
  } catch (error) {
    console.log('❌ 对比测试失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试修复后的严格验证批量资源更新接口');
  console.log('='.repeat(80));
  
  // 测试时间窗口逻辑
  const timeWindowTestPassed = await testTimeWindowLogic();
  
  if (timeWindowTestPassed) {
    // 对比新旧接口
    await compareWithOldAPI();
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 修复总结:');
  console.log('1. ✅ 时间窗口无效时，usedStrictValidation应该为false');
  console.log('2. ✅ 添加了timeWindowValid和timeWindowReason字段');
  console.log('3. ✅ 改进了响应消息的准确性');
  console.log('4. ✅ 确保了逻辑的一致性');
  
  console.log('\n💡 使用建议:');
  console.log('- 前端可以根据timeWindowValid字段判断是否为时间窗口问题');
  console.log('- usedStrictValidation字段准确反映是否进行了严格验证');
  console.log('- 时间窗口问题不会触发严格验证，直接返回0资源');
}

// 运行测试
runTests().catch(console.error);

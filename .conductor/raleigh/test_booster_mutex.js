// 测试加速道具互斥功能的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3457/api';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.lXoRXISFZ1PoPkGOOaDzgTBD8fR856jsi5_CAiFnmqY';

const createAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Accept-Language': 'zh'
  };
};

// 测试获取道具互斥状态
async function testGetMutexStatus() {
  console.log('\n=== 测试获取道具互斥状态 ===');
  try {
    const response = await axios.get(`${BASE_URL}/iap/boosters/mutex-status`, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 获取道具互斥状态成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 获取道具互斥状态失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试获取用户拥有的道具
async function testGetUserBoosters() {
  console.log('\n=== 测试获取用户拥有的道具 ===');
  try {
    const response = await axios.get(`${BASE_URL}/iap/boosters`, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 获取用户道具成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 获取用户道具失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试获取激活的道具
async function testGetActiveBoosters() {
  console.log('\n=== 测试获取激活的道具 ===');
  try {
    const response = await axios.get(`${BASE_URL}/iap/boosters/active`, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 获取激活道具成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 获取激活道具失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试使用道具
async function testUseBooster(boosterId) {
  console.log(`\n=== 测试使用道具 (ID: ${boosterId}) ===`);
  try {
    const response = await axios.post(`${BASE_URL}/iap/boosters/use`, {
      boosterId: boosterId
    }, {
      headers: createAuthHeaders()
    });
    
    console.log('✅ 使用道具成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data;
  } catch (error) {
    console.log('❌ 使用道具失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return null;
  }
}

// 测试健康检查
async function testHealthCheck() {
  console.log('\n=== 测试健康检查 ===');
  try {
    const response = await axios.get(`${BASE_URL}/health/health`);
    console.log('✅ 健康检查成功');
    console.log('响应状态:', response.status);
    return true;
  } catch (error) {
    console.log('❌ 健康检查失败');
    if (error.response) {
      console.log('错误状态:', error.response.status);
      console.log('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('错误信息:', error.message);
    }
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试加速道具互斥功能...');
  
  // 首先测试健康检查
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ 服务器健康检查失败，停止测试');
    return;
  }
  
  // 测试获取道具互斥状态
  await testGetMutexStatus();
  
  // 测试获取用户拥有的道具
  const userBoosters = await testGetUserBoosters();
  
  // 测试获取激活的道具
  await testGetActiveBoosters();
  
  // 如果用户有道具，测试使用道具的互斥逻辑
  if (userBoosters && userBoosters.ok && userBoosters.data && userBoosters.data.length > 0) {
    console.log('\n📋 用户拥有的道具:');
    userBoosters.data.forEach((booster, index) => {
      console.log(`${index + 1}. ID: ${booster.id}, 类型: ${booster.type}, 倍数: ${booster.multiplier}x, 数量: ${booster.quantity}`);
    });
    
    // 找到Speed Boost道具进行测试
    const speedBoosts = userBoosters.data.filter(b => b.type === 'speed_boost' && b.quantity > 0);
    const timeWarps = userBoosters.data.filter(b => b.type === 'time_warp' && b.quantity > 0);
    
    if (speedBoosts.length >= 2) {
      console.log('\n🧪 测试Speed Boost互斥逻辑:');
      console.log('1. 使用第一个Speed Boost道具');
      await testUseBooster(speedBoosts[0].id);
      
      console.log('2. 尝试使用第二个Speed Boost道具（应该失败）');
      await testUseBooster(speedBoosts[1].id);
      
      // 再次检查互斥状态
      await testGetMutexStatus();
    } else if (speedBoosts.length === 1) {
      console.log('\n🧪 测试Speed Boost使用:');
      await testUseBooster(speedBoosts[0].id);
      
      // 再次检查互斥状态
      await testGetMutexStatus();
    }
    
    if (timeWarps.length > 0) {
      console.log('\n🧪 测试Time Warp使用（应该不受互斥限制）:');
      await testUseBooster(timeWarps[0].id);
    }
  } else {
    console.log('\n⚠️ 用户没有可用的道具，无法测试使用功能');
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);

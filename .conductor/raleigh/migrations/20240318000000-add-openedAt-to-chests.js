'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('chests');
    if (!table.openedAt) {
      await queryInterface.addColumn('chests', 'openedAt', {
        type: Sequelize.DATE,
        allowNull: true,
        after: 'type'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('chests');
    if (table.openedAt) {
      await queryInterface.removeColumn('chests', 'openedAt');
    }
  }
};
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 检查farm_plots表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('farm_plots')) {
        console.log('farm_plots表不存在，跳过迁移');
        return;
      }

      // 检查userId列是否存在
      const columns = await queryInterface.describeTable('farm_plots');
      if (columns.userId && !columns.walletId) {
        // 重命名userId列为walletId
        await queryInterface.renameColumn('farm_plots', 'userId', 'walletId');
        console.log('成功将farm_plots表中的userId字段重命名为walletId');

        // 更新外键引用
        await queryInterface.removeConstraint('farm_plots', 'farm_plots_ibfk_1');
        await queryInterface.addConstraint('farm_plots', {
          fields: ['walletId'],
          type: 'foreign key',
          name: 'farm_plots_wallet_fk',
          references: {
            table: 'user_wallets',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('成功更新外键引用从Users表到user_wallets表');

        // 更新索引
        // 检查索引是否存在
        const tableIndexes = await queryInterface.showIndex('farm_plots');
        const indexExists = tableIndexes.some(index => 
          index.name === 'farm_plots_user_plot_unique'
        );
        
        if (indexExists) {
          await queryInterface.removeIndex('farm_plots', 'farm_plots_user_plot_unique');
        }
        await queryInterface.addIndex('farm_plots', ['walletId', 'plotNumber'], {
          unique: true,
          name: 'farm_plots_wallet_plot_unique',
        });
        console.log('成功更新索引');
      } else if (columns.walletId) {
        console.log('walletId字段已存在于farm_plots表中，跳过迁移');
      } else {
        console.log('userId字段不存在于farm_plots表中，跳过迁移');
      }
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查farm_plots表是否存在
      const tables = await queryInterface.showAllTables();
      if (!tables.includes('farm_plots')) {
        console.log('farm_plots表不存在，跳过回滚');
        return;
      }

      // 检查walletId列是否存在
      const columns = await queryInterface.describeTable('farm_plots');
      if (columns.walletId && !columns.userId) {
        // 更新外键引用
        await queryInterface.removeConstraint('farm_plots', 'farm_plots_wallet_fk');
        
        // 重命名walletId列为userId
        await queryInterface.renameColumn('farm_plots', 'walletId', 'userId');
        console.log('成功将farm_plots表中的walletId字段重命名为userId');

        // 添加外键约束回到Users表
        await queryInterface.addConstraint('farm_plots', {
          fields: ['userId'],
          type: 'foreign key',
          name: 'farm_plots_ibfk_1',
          references: {
            table: 'Users',
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        });
        console.log('成功更新外键引用从user_wallets表到Users表');

        // 更新索引
        // 检查索引是否存在
        const tableIndexes = await queryInterface.showIndex('farm_plots');
        const indexExists = tableIndexes.some(index => 
          index.name === 'farm_plots_wallet_plot_unique'
        );
        
        if (indexExists) {
          await queryInterface.removeIndex('farm_plots', 'farm_plots_wallet_plot_unique');
        }
        await queryInterface.addIndex('farm_plots', ['userId', 'plotNumber'], {
          unique: true,
          name: 'farm_plots_user_plot_unique',
        });
        console.log('成功更新索引');
      } else if (columns.userId) {
        console.log('userId字段已存在于farm_plots表中，跳过回滚');
      } else {
        console.log('walletId字段不存在于farm_plots表中，跳过回滚');
      }
    } catch (error) {
      console.error('迁移回滚错误:', error);
      throw error;
    }
  },
};
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // 移除User表中的gems字段，因为我们使用UserWallet表中的gem字段
      // 将milk字段从User表移到UserWallet表
      
      // 检查milk列是否已存在
      const columns = await queryInterface.describeTable('user_wallets');
      if (!columns.milk) {
        await queryInterface.addColumn('user_wallets', 'milk', {
          type: Sequelize.FLOAT,
          allowNull: false,
          defaultValue: 0,
        });
        console.log('成功添加milk字段到user_wallets表');
      } else {
        console.log('milk字段已存在于user_wallets表中');
      }
    } catch (error) {
      console.error('迁移错误:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // 检查milk列是否存在
      const columns = await queryInterface.describeTable('user_wallets');
      if (columns.milk) {
        await queryInterface.removeColumn('user_wallets', 'milk');
        console.log('成功从user_wallets表中移除milk字段');
      } else {
        console.log('milk字段不存在于user_wallets表中');
      }
    } catch (error) {
      console.error('迁移回滚错误:', error);
      throw error;
    }
  },
};
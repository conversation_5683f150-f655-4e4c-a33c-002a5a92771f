'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查表是否已存在
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('free_ticket_transfers')) {
      await queryInterface.createTable('free_ticket_transfers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER.UNSIGNED
      },
      fromUserId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      fromWalletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      toUserId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      toWalletId: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      amount: {
        type: Sequelize.INTEGER.UNSIGNED,
        allowNull: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    if (tables.includes('free_ticket_transfers')) {
      await queryInterface.dropTable('free_ticket_transfers');
    }
  }
};
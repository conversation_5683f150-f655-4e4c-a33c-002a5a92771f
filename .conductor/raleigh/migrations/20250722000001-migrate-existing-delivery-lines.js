'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('开始迁移现有流水线数据...');
      
      // 获取所有现有的流水线
      const [deliveryLines] = await queryInterface.sequelize.query(
        'SELECT * FROM delivery_lines',
        { transaction }
      );
      
      console.log(`找到 ${deliveryLines.length} 个现有流水线`);
      
      // 获取所有配置
      const [configs] = await queryInterface.sequelize.query(
        'SELECT * FROM delivery_line_configs ORDER BY grade ASC',
        { transaction }
      );
      
      console.log(`找到 ${configs.length} 个配置`);
      
      if (configs.length === 0) {
        throw new Error('没有找到流水线配置，请先运行配置创建迁移');
      }
      
      // 创建配置映射
      const configMap = {};
      configs.forEach(config => {
        configMap[config.grade] = config;
      });
      
      let updatedCount = 0;
      
      // 更新每个流水线
      for (const deliveryLine of deliveryLines) {
        const level = deliveryLine.level;
        const config = configMap[level];
        
        if (!config) {
          console.warn(`警告: 等级 ${level} 没有对应的配置，跳过流水线 ID ${deliveryLine.id}`);
          continue;
        }
        
        // 更新流水线数据以匹配配置
        await queryInterface.sequelize.query(`
          UPDATE delivery_lines 
          SET 
            deliverySpeed = :deliverySpeed,
            blockUnit = :blockUnit,
            blockPrice = :blockPrice,
            upgradeCost = :upgradeCost,
            updatedAt = NOW()
          WHERE id = :id
        `, {
          replacements: {
            id: deliveryLine.id,
            deliverySpeed: config.production_interval,
            blockUnit: config.capacity,
            blockPrice: config.profit,
            upgradeCost: config.upgrade_cost
          },
          transaction
        });
        
        updatedCount++;
      }
      
      console.log(`成功更新 ${updatedCount} 个流水线`);
      
      // 验证更新结果
      const [updatedLines] = await queryInterface.sequelize.query(
        'SELECT level, deliverySpeed, blockUnit, blockPrice, upgradeCost FROM delivery_lines ORDER BY level',
        { transaction }
      );
      
      console.log('更新后的流水线数据样本:');
      updatedLines.slice(0, 5).forEach(line => {
        console.log(`等级${line.level}: 速度=${line.deliverySpeed}s, 容量=${line.blockUnit}, 价格=${line.blockPrice}, 费用=${line.upgradeCost}`);
      });
      
      await transaction.commit();
      console.log('✅ 流水线数据迁移完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 流水线数据迁移失败:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      console.log('开始回滚流水线数据...');
      
      // 将所有流水线重置为旧的默认值
      await queryInterface.sequelize.query(`
        UPDATE delivery_lines 
        SET 
          deliverySpeed = 1.0,
          blockUnit = 5.0,
          blockPrice = 5.0,
          upgradeCost = 500.0,
          updatedAt = NOW()
        WHERE level = 1
      `, { transaction });
      
      // 对于高等级的流水线，使用旧的算法重新计算
      const [deliveryLines] = await queryInterface.sequelize.query(
        'SELECT * FROM delivery_lines WHERE level > 1 ORDER BY level',
        { transaction }
      );
      
      for (const line of deliveryLines) {
        let deliverySpeed = 1.0;
        let blockUnit = 5.0;
        let blockPrice = 5.0;
        let upgradeCost = 500.0;
        
        // 使用旧的升级公式计算
        for (let i = 1; i < line.level; i++) {
          deliverySpeed = deliverySpeed / 1.01;
          blockUnit = blockUnit * 2.0;
          blockPrice = blockPrice * 2.0;
          upgradeCost = upgradeCost * 2.0;
        }
        
        await queryInterface.sequelize.query(`
          UPDATE delivery_lines 
          SET 
            deliverySpeed = :deliverySpeed,
            blockUnit = :blockUnit,
            blockPrice = :blockPrice,
            upgradeCost = :upgradeCost,
            updatedAt = NOW()
          WHERE id = :id
        `, {
          replacements: {
            id: line.id,
            deliverySpeed: Math.round(deliverySpeed * 1000) / 1000,
            blockUnit: Math.round(blockUnit * 1000) / 1000,
            blockPrice: Math.round(blockPrice * 1000) / 1000,
            upgradeCost: Math.round(upgradeCost * 1000) / 1000
          },
          transaction
        });
      }
      
      await transaction.commit();
      console.log('✅ 流水线数据回滚完成');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ 流水线数据回滚失败:', error);
      throw error;
    }
  }
};

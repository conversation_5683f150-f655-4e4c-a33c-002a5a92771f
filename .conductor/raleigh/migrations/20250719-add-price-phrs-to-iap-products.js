'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🔍 检查 iap_products.pricePhrs 字段是否存在...');
    
    try {
      // 使用原生SQL查询检查列是否存在
      const [results] = await queryInterface.sequelize.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'iap_products' 
        AND COLUMN_NAME = 'pricePhrs'
      `);
      
      if (results.length > 0) {
        console.log('ℹ️  iap_products.pricePhrs 字段已存在，跳过创建');
        return;
      }
      
      // 字段不存在，添加字段
      await queryInterface.addColumn('iap_products', 'pricePhrs', {
        type: Sequelize.DECIMAL(20, 4), // 支持大数值，4位小数
        allowNull: true,
        comment: 'PHRS价格，基于USD价格和PHRS汇率计算'
      });
      
      console.log('✅ 已添加 iap_products.pricePhrs 字段');
      
    } catch (error) {
      // 如果是重复列错误，则忽略
      if (error.message && (
        error.message.includes('Duplicate column name') || 
        error.message.includes('duplicate column name') ||
        error.code === 'ER_DUP_FIELDNAME'
      )) {
        console.log('ℹ️  iap_products.pricePhrs 字段已存在（捕获重复列错误），跳过创建');
        return;
      }
      
      console.error('❌ 添加 pricePhrs 字段时出错:', error.message);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // 检查列是否存在
      const tableDescription = await queryInterface.describeTable('iap_products');
      
      if (tableDescription.pricePhrs) {
        // 只有在列存在时才删除
        await queryInterface.removeColumn('iap_products', 'pricePhrs');
        console.log('✅ 已删除 iap_products.pricePhrs 字段');
      } else {
        console.log('ℹ️  iap_products.pricePhrs 字段不存在，跳过删除');
      }
    } catch (error) {
      console.error('❌ 删除 pricePhrs 字段时出错:', error.message);
      throw error;
    }
  }
};

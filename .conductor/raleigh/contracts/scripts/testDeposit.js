const { ethers } = require("hardhat");
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用户输入的函数
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log("🚀 PHRS充值合约真实测试脚本");
  console.log("=====================================");

  try {
    // 1. 获取网络信息
    const network = await ethers.provider.getNetwork();
    console.log(`📡 连接网络: ${network.name} (Chain ID: ${network.chainId})`);

    // 2. 获取账户信息
    const [deployer] = await ethers.getSigners();
    console.log(`👤 测试账户: ${deployer.address}`);
    
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log(`💰 账户余额: ${ethers.formatEther(balance)} PHRS`);

    if (parseFloat(ethers.formatEther(balance)) < 0.1) {
      console.log("❌ 账户余额不足，请确保有足够的PHRS进行测试");
      process.exit(1);
    }

    // 3. 获取合约地址
    let contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    
    if (!contractAddress) {
      contractAddress = await askQuestion("📝 请输入PHRS充值合约地址: ");
    }

    if (!ethers.isAddress(contractAddress)) {
      console.log("❌ 无效的合约地址");
      process.exit(1);
    }

    console.log(`📋 合约地址: ${contractAddress}`);

    // 4. 连接合约
    const contractABI = [
      "function deposit() external payable",
      "function getBalance() external view returns (uint256)",
      "function getUserBalance(address user) external view returns (uint256)",
      "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
      "function detectForcedDeposits() external view returns (uint256, bool)",
      "function minDepositAmount() external view returns (uint256)",
      "function maxDepositAmount() external view returns (uint256)",
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, deployer);

    // 5. 获取合约信息
    console.log("\n📊 合约当前状态:");
    const contractInfo = await contract.getContractInfo();
    console.log(`   最小充值金额: ${ethers.formatEther(contractInfo[0])} PHRS`);
    console.log(`   最大充值金额: ${ethers.formatEther(contractInfo[1])} PHRS`);
    console.log(`   合约总余额: ${ethers.formatEther(contractInfo[2])} PHRS`);
    console.log(`   合法充值总额: ${ethers.formatEther(contractInfo[3])} PHRS`);

    // 6. 检查强制发送
    const [forcedAmount, hasForced] = await contract.detectForcedDeposits();
    if (hasForced) {
      console.log(`⚠️  检测到强制发送: ${ethers.formatEther(forcedAmount)} PHRS`);
    } else {
      console.log("✅ 未检测到强制发送");
    }

    // 7. 获取用户当前余额
    const userBalance = await contract.getUserBalance(deployer.address);
    console.log(`👤 您的链上余额: ${ethers.formatEther(userBalance)} PHRS`);

    // 8. 询问充值金额
    const depositAmountStr = await askQuestion("\n💸 请输入充值金额 (PHRS): ");
    const depositAmount = ethers.parseEther(depositAmountStr);

    // 9. 验证充值金额
    const minAmount = contractInfo[0];
    const maxAmount = contractInfo[1];

    if (depositAmount < minAmount) {
      console.log(`❌ 充值金额太小，最小金额为 ${ethers.formatEther(minAmount)} PHRS`);
      process.exit(1);
    }

    if (depositAmount > maxAmount) {
      console.log(`❌ 充值金额太大，最大金额为 ${ethers.formatEther(maxAmount)} PHRS`);
      process.exit(1);
    }

    // 10. 确认充值
    const confirm = await askQuestion(`\n🔍 确认充值 ${depositAmountStr} PHRS 到合约? (y/N): `);
    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log("❌ 用户取消充值");
      process.exit(0);
    }

    console.log("\n🔄 开始执行充值交易...");

    // 11. 估算Gas费用
    try {
      const gasEstimate = await contract.deposit.estimateGas({ value: depositAmount });
      const gasPrice = await ethers.provider.getFeeData();
      const estimatedCost = gasEstimate * gasPrice.gasPrice;
      
      console.log(`⛽ 预估Gas用量: ${gasEstimate.toString()}`);
      console.log(`💰 预估Gas费用: ${ethers.formatEther(estimatedCost)} PHRS`);
    } catch (error) {
      console.log("⚠️  Gas估算失败，继续执行交易");
    }

    // 12. 执行充值交易
    const tx = await contract.deposit({ 
      value: depositAmount,
      gasLimit: 200000 // 设置Gas限制
    });

    console.log(`📝 交易已提交: ${tx.hash}`);
    console.log("⏳ 等待交易确认...");

    // 13. 等待交易确认
    const receipt = await tx.wait();
    
    if (receipt.status === 1) {
      console.log("✅ 交易成功确认!");
      console.log(`📦 区块号: ${receipt.blockNumber}`);
      console.log(`⛽ 实际Gas用量: ${receipt.gasUsed.toString()}`);
      console.log(`💰 实际Gas费用: ${ethers.formatEther(receipt.gasUsed * receipt.gasPrice)} PHRS`);
    } else {
      console.log("❌ 交易失败");
      process.exit(1);
    }

    // 14. 解析事件
    console.log("\n📡 解析充值事件:");
    const depositEvents = receipt.logs.filter(log => {
      try {
        const parsed = contract.interface.parseLog(log);
        return parsed.name === 'Deposit';
      } catch {
        return false;
      }
    });

    if (depositEvents.length > 0) {
      const event = contract.interface.parseLog(depositEvents[0]);
      console.log(`   用户地址: ${event.args.user}`);
      console.log(`   充值金额: ${ethers.formatEther(event.args.amount)} PHRS`);
      console.log(`   时间戳: ${new Date(Number(event.args.timestamp) * 1000).toLocaleString()}`);
    }

    // 15. 验证充值后状态
    console.log("\n🔍 验证充值后状态:");
    
    const newContractInfo = await contract.getContractInfo();
    const newUserBalance = await contract.getUserBalance(deployer.address);
    
    console.log(`📊 合约状态更新:`);
    console.log(`   合约总余额: ${ethers.formatEther(contractInfo[2])} → ${ethers.formatEther(newContractInfo[2])} PHRS`);
    console.log(`   合法充值总额: ${ethers.formatEther(contractInfo[3])} → ${ethers.formatEther(newContractInfo[3])} PHRS`);
    console.log(`   您的链上余额: ${ethers.formatEther(userBalance)} → ${ethers.formatEther(newUserBalance)} PHRS`);

    // 16. 验证数据一致性
    const balanceIncrease = newContractInfo[2] - contractInfo[2];
    const legitimateIncrease = newContractInfo[3] - contractInfo[3];
    const userBalanceIncrease = newUserBalance - userBalance;

    console.log(`\n✅ 数据一致性检查:`);
    console.log(`   充值金额: ${ethers.formatEther(depositAmount)} PHRS`);
    console.log(`   合约余额增加: ${ethers.formatEther(balanceIncrease)} PHRS`);
    console.log(`   合法充值增加: ${ethers.formatEther(legitimateIncrease)} PHRS`);
    console.log(`   用户余额增加: ${ethers.formatEther(userBalanceIncrease)} PHRS`);

    if (balanceIncrease === depositAmount && 
        legitimateIncrease === depositAmount && 
        userBalanceIncrease === depositAmount) {
      console.log("🎉 数据一致性验证通过!");
    } else {
      console.log("⚠️  数据一致性验证失败，请检查合约状态");
    }

    // 17. 再次检查强制发送
    const [newForcedAmount, newHasForced] = await contract.detectForcedDeposits();
    if (newHasForced) {
      console.log(`⚠️  检测到强制发送: ${ethers.formatEther(newForcedAmount)} PHRS`);
    } else {
      console.log("✅ 未检测到强制发送");
    }

    console.log("\n🎉 充值测试完成!");
    console.log("=====================================");

  } catch (error) {
    console.error("❌ 测试过程中发生错误:", error.message);
    if (error.reason) {
      console.error("错误原因:", error.reason);
    }
    process.exit(1);
  } finally {
    rl.close();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (error) => {
  console.error('未处理的Promise拒绝:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n👋 用户中断测试');
  rl.close();
  process.exit(0);
});

// 运行主函数
main().catch((error) => {
  console.error("脚本执行失败:", error);
  process.exit(1);
});

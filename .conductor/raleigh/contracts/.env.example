# PHRS充值合约部署配置示例
# 复制此文件为 .env 并填入实际值

# ===========================================
# 部署账户私钥
# ===========================================

# 部署账户私钥（确保有足够的原生币用于gas费）
PRIVATE_KEY=your_private_key_here

# ===========================================
# 充值限制配置
# ===========================================

# 最小充值金额（PHRS原生币）
MIN_DEPOSIT_AMOUNT=1

# 最大充值金额（PHRS原生币）
MAX_DEPOSIT_AMOUNT=10000

# ===========================================
# Pharos网络配置
# ===========================================

# Pharos测试网RPC URL
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network

# Pharos主网RPC URL
PHAROS_MAINNET_RPC_URL=https://rpc.pharos.network

# ===========================================
# 区块链浏览器配置（用于合约验证）
# ===========================================

# Pharos测试网浏览器
PHAROS_TESTNET_EXPLORER=https://testnet-explorer.pharos.network
PHAROS_TESTNET_EXPLORER_API=https://testnet-explorer.pharos.network/api

# Pharos主网浏览器
PHAROS_MAINNET_EXPLORER=https://explorer.pharos.network
PHAROS_MAINNET_EXPLORER_API=https://explorer.pharos.network/api

# 浏览器API密钥（用于合约验证）
PHAROS_EXPLORER_API_KEY=your_explorer_api_key_here

# ===========================================
# 部署配置
# ===========================================

# 是否在部署后自动验证合约
AUTO_VERIFY=true

# Gas价格设置（可选，单位：gwei）
GAS_PRICE=20

# Gas限制（可选）
GAS_LIMIT=8000000

# ===========================================
# 注意事项
# ===========================================

# 1. PHRS现在是原生币，不需要代币合约地址
# 2. 确保部署账户有足够的PHRS原生币支付gas费
# 3. 测试网和主网使用不同的RPC URL
# 4. 合约使用透明代理模式
# 5. 保存好代理合约地址，用户应该与代理合约交互
# 6. 实现合约地址会在升级时改变，但代理地址保持不变
# 7. 升级权限由ProxyAdmin合约管理

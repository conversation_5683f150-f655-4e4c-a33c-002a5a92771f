{"name": "phrs-deposit-contracts", "version": "1.0.0", "description": "PHRS代币充值智能合约", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:testnet": "hardhat run scripts/deploy.js --network pharos_testnet", "deploy:mainnet": "hardhat run scripts/deploy.js --network pharos_mainnet", "upgrade:testnet": "hardhat run scripts/upgrade.js --network pharos_testnet", "upgrade:mainnet": "hardhat run scripts/upgrade.js --network pharos_mainnet", "test:deposit": "hardhat run scripts/testDeposit.js --network pharos_testnet", "test:deposit:mainnet": "hardhat run scripts/testDeposit.js --network pharos_mainnet", "test:quick": "hardhat run scripts/quickDeposit.js --network pharos_testnet", "test:quick:mainnet": "hardhat run scripts/quickDeposit.js --network pharos_mainnet", "test:security": "hardhat run scripts/testDirectSend.js --network pharos_testnet", "test:security:mainnet": "hardhat run scripts/testDirectSend.js --network pharos_mainnet", "verify": "hardhat verify", "clean": "hardhat clean"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@openzeppelin/contracts": "^5.3.0", "@openzeppelin/contracts-upgradeable": "^5.3.0", "@openzeppelin/hardhat-upgrades": "^3.0.0", "hardhat": "^2.19.0", "ethers": "^6.8.0"}, "keywords": ["solidity", "ethereum", "smart-contracts", "phrs", "deposit", "pharos"], "author": "Wolf Fun Team", "license": "MIT"}
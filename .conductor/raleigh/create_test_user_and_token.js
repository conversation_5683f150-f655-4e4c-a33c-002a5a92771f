// 创建测试用户并获取JWT token的脚本
const axios = require('axios');
const crypto = require('crypto');

const BASE_URL = 'http://localhost:3456';

// 生成测试钱包地址
function generateTestWalletAddress() {
  const randomBytes = crypto.randomBytes(20);
  return '0x' + randomBytes.toString('hex');
}

// 生成测试签名
function generateTestSignature() {
  return '0x' + crypto.randomBytes(65).toString('hex');
}

// 生成随机nonce
function generateNonce() {
  return crypto.randomBytes(16).toString('hex');
}

// 创建测试用户并获取token
async function createTestUserAndGetToken() {
  console.log('🔧 创建测试用户并获取JWT token...');
  
  const testWalletAddress = generateTestWalletAddress();
  const testSignature = generateTestSignature();
  const nonce = generateNonce();
  
  console.log(`📝 测试钱包地址: ${testWalletAddress}`);
  
  try {
    // 方法1: 尝试Web3认证流程
    console.log('\n📝 方法1: 尝试Web3认证流程...');

    // 步骤1: 获取nonce
    console.log('1️⃣ 获取nonce...');
    try {
      const nonceResponse = await axios.post(`${BASE_URL}/api/web3-auth/nonce`, {
        walletAddress: testWalletAddress
      });

      if (nonceResponse.data.ok) {
        console.log('✅ Nonce获取成功:', nonceResponse.data.data.nonce);
        const serverNonce = nonceResponse.data.data.nonce;
        const message = nonceResponse.data.data.message;

        // 步骤2: 使用nonce进行认证
        console.log('2️⃣ 进行Web3认证...');
        const authResponse = await axios.post(`${BASE_URL}/api/web3-auth/login`, {
          walletAddress: testWalletAddress,
          signature: testSignature,
          message: message
        });

        if (authResponse.data.ok && authResponse.data.data.token) {
          console.log('✅ Web3认证成功！');
          console.log(`🎫 JWT Token: ${authResponse.data.data.token}`);
          return {
            success: true,
            token: authResponse.data.data.token,
            walletAddress: testWalletAddress,
            method: 'web3'
          };
        }
      }
    } catch (error) {
      console.log('❌ Web3认证失败:', error.response?.data?.message || error.message);
    }
    
    // 方法2: 尝试直接钱包登录
    console.log('\n📝 方法2: 尝试直接钱包登录...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/wallet-login`, {
        walletAddress: testWalletAddress,
        signature: testSignature,
        message: `Welcome to Wolf Fun! Nonce: ${nonce}`
      });
      
      if (loginResponse.data.ok && loginResponse.data.data.token) {
        console.log('✅ 钱包登录成功！');
        console.log(`🎫 JWT Token: ${loginResponse.data.data.token}`);
        return {
          success: true,
          token: loginResponse.data.data.token,
          walletAddress: testWalletAddress,
          method: 'wallet-login'
        };
      }
    } catch (error) {
      console.log('❌ 钱包登录失败:', error.response?.data?.message || error.message);
    }
    
    // 方法3: 尝试Telegram认证（如果有测试用户）
    console.log('\n📝 方法3: 尝试Telegram测试认证...');
    try {
      const telegramData = {
        id: Math.floor(Math.random() * 1000000),
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser_' + Date.now(),
        photo_url: '',
        auth_date: Math.floor(Date.now() / 1000),
        hash: crypto.createHash('sha256').update('test').digest('hex')
      };
      
      const telegramResponse = await axios.post(`${BASE_URL}/api/auth/telegram`, telegramData);
      
      if (telegramResponse.data.ok && telegramResponse.data.data.token) {
        console.log('✅ Telegram认证成功！');
        console.log(`🎫 JWT Token: ${telegramResponse.data.data.token}`);
        return {
          success: true,
          token: telegramResponse.data.data.token,
          walletAddress: null,
          method: 'telegram'
        };
      }
    } catch (error) {
      console.log('❌ Telegram认证失败:', error.response?.data?.message || error.message);
    }
    
    console.log('\n❌ 所有认证方法都失败了');
    return { success: false };
    
  } catch (error) {
    console.log('❌ 创建测试用户失败:', error.message);
    return { success: false };
  }
}

// 验证token有效性
async function validateToken(token) {
  try {
    console.log('\n🔍 验证token有效性...');
    const response = await axios.get(`${BASE_URL}/api/test/health`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Token验证成功');
      return true;
    }
  } catch (error) {
    console.log('❌ Token验证失败:', error.response?.data?.message || error.message);
  }
  return false;
}

// 获取用户信息
async function getUserInfo(token) {
  try {
    console.log('👤 获取用户信息...');
    const response = await axios.get(`${BASE_URL}/api/wallet/info`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 用户信息获取成功:');
      console.log(`   用户ID: ${response.data.data.userId}`);
      console.log(`   钱包ID: ${response.data.data.walletId}`);
      console.log(`   钱包地址: ${response.data.data.walletAddress || 'N/A'}`);
      console.log(`   GEM: ${response.data.data.gem}`);
      console.log(`   牛奶: ${response.data.data.milk || response.data.data.pendingMilk || 0}`);
      return response.data.data;
    }
  } catch (error) {
    console.log('❌ 获取用户信息失败:', error.response?.data?.message || error.message);
  }
  return null;
}

// 主函数
async function main() {
  console.log('🚀 开始创建测试用户并获取JWT token');
  console.log('='.repeat(60));
  
  const result = await createTestUserAndGetToken();
  
  if (result.success) {
    console.log('\n🎉 成功获取JWT token！');
    console.log('='.repeat(60));
    
    // 验证token
    const isValid = await validateToken(result.token);
    
    if (isValid) {
      // 获取用户信息
      const userInfo = await getUserInfo(result.token);
      
      console.log('\n📋 测试配置信息:');
      console.log('='.repeat(60));
      console.log('请将以下token复制到测试脚本中：');
      console.log(`JWT_TOKEN: '${result.token}'`);
      console.log('='.repeat(60));
      
      // 生成测试脚本配置
      console.log('\n📝 测试脚本配置代码:');
      console.log('```javascript');
      console.log('const TEST_CONFIG = {');
      console.log(`  JWT_TOKEN: '${result.token}',`);
      console.log(`  // 认证方式: ${result.method}`);
      if (result.walletAddress) {
        console.log(`  // 钱包地址: ${result.walletAddress}`);
      }
      if (userInfo) {
        console.log(`  // 用户ID: ${userInfo.userId}`);
        console.log(`  // 钱包ID: ${userInfo.walletId}`);
      }
      console.log('  // ... 其他配置');
      console.log('};');
      console.log('```');
      
      console.log('\n✅ 现在可以运行完整的功能测试了！');
      console.log('运行命令: node comprehensive_strict_api_test.js');
      
    } else {
      console.log('\n❌ Token验证失败，无法继续');
    }
    
  } else {
    console.log('\n❌ 无法获取有效的JWT token');
    console.log('\n💡 替代方案:');
    console.log('1. 手动登录游戏获取token');
    console.log('2. 从浏览器开发者工具复制token');
    console.log('3. 使用现有的有效token');
    console.log('4. 检查服务器是否正常运行');
  }
}

main().catch(console.error);

// 测试离线收益计算的VIP和Speed Boost加成效果
// 验证离线奖励计算逻辑的正确性

console.log('=== 离线收益加成效果测试 ===\n');

// 基础出货线数据
const baseDeliveryLine = {
  id: 1,
  walletId: 1,
  level: 2,
  deliverySpeed: 4.950,    // 基础出货速度（秒）
  blockUnit: 10.000,       // 方块容量
  blockPrice: 10.000,      // 基础方块价格（GEM）
  pendingBlocks: 100       // 待出售方块数量
};

// VIP效果
const vipEffects = {
  isVip: true,
  deliverySpeedMultiplier: 1.3,  // 30% 出货线速度加成
  blockPriceMultiplier: 1.2,     // 20% 出货线价格加成
  productionSpeedMultiplier: 1.3  // 30% 牧场区生产速度加成
};

// Speed Boost道具效果
const speedBoostEffects = {
  none: { speedMultiplier: 1.0 },     // 无道具
  x2: { speedMultiplier: 3.0 },       // +200% 速度加成
  x4: { speedMultiplier: 5.0 }        // +400% 速度加成
};

// 非VIP效果
const nonVipEffects = {
  isVip: false,
  deliverySpeedMultiplier: 1,
  blockPriceMultiplier: 1,
  productionSpeedMultiplier: 1
};

// 测试函数：计算离线收益
function calculateOfflineRewardWithBoosts(deliveryLine, offlineTime, vipEffects, boosterEffects) {
  // 设置最大离线奖励时间为8小时
  const maxOfflineTime = 8 * 60 * 60; // 8小时
  const effectiveOfflineTime = Math.min(offlineTime, maxOfflineTime);
  
  // 计算总的出货线速度加成倍率
  const deliverySpeedMultiplier = vipEffects.deliverySpeedMultiplier * boosterEffects.speedMultiplier;
  
  // 计算出货线价格加成倍率（只有VIP提供20%的价格加成）
  const blockPriceMultiplier = vipEffects.blockPriceMultiplier;
  
  // 计算应用加成后的实际出货速度和方块价格
  const actualDeliverySpeed = deliveryLine.deliverySpeed / deliverySpeedMultiplier;
  const actualBlockPrice = deliveryLine.blockPrice * blockPriceMultiplier;
  
  // 计算离线期间可以完成的出货周期数（使用应用加成后的速度）
  const cycles = Math.floor(effectiveOfflineTime / actualDeliverySpeed);
  
  if (cycles <= 0 || deliveryLine.pendingBlocks <= 0) {
    return {
      gem: 0,
      hasBoost: deliverySpeedMultiplier > 1 || blockPriceMultiplier > 1,
      speedMultiplier: deliverySpeedMultiplier,
      priceMultiplier: blockPriceMultiplier,
      deliveredBlocks: 0,
      actualDeliverySpeed: Number(actualDeliverySpeed.toFixed(3)),
      actualBlockPrice: Number(actualBlockPrice.toFixed(3))
    };
  }
  
  // 计算离线期间可以出售的方块数量
  const deliveredBlocks = Math.min(cycles, deliveryLine.pendingBlocks);
  
  // 计算获得的GEM数量（使用应用加成后的价格）
  const earnedGem = deliveredBlocks * actualBlockPrice;
  
  return {
    gem: Number(earnedGem.toFixed(3)),
    hasBoost: deliverySpeedMultiplier > 1 || blockPriceMultiplier > 1,
    speedMultiplier: deliverySpeedMultiplier,
    priceMultiplier: blockPriceMultiplier,
    deliveredBlocks: deliveredBlocks,
    actualDeliverySpeed: Number(actualDeliverySpeed.toFixed(3)),
    actualBlockPrice: Number(actualBlockPrice.toFixed(3)),
    cycles: cycles,
    effectiveOfflineTime: effectiveOfflineTime
  };
}

// 测试不同加成组合
function testBoostCombinations() {
  console.log('🧪 测试不同加成组合的离线收益\n');
  
  const offlineTime = 2 * 60 * 60; // 2小时离线时间
  
  const testCases = [
    {
      name: '无加成',
      vip: nonVipEffects,
      booster: speedBoostEffects.none
    },
    {
      name: '仅VIP会员',
      vip: vipEffects,
      booster: speedBoostEffects.none
    },
    {
      name: 'VIP + Speed Boost x2',
      vip: vipEffects,
      booster: speedBoostEffects.x2
    },
    {
      name: 'VIP + Speed Boost x4',
      vip: vipEffects,
      booster: speedBoostEffects.x4
    },
    {
      name: '仅Speed Boost x2',
      vip: nonVipEffects,
      booster: speedBoostEffects.x2
    },
    {
      name: '仅Speed Boost x4',
      vip: nonVipEffects,
      booster: speedBoostEffects.x4
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`📊 ${testCase.name}:`);
    console.log('=====================================');
    
    const result = calculateOfflineRewardWithBoosts(
      baseDeliveryLine, 
      offlineTime, 
      testCase.vip, 
      testCase.booster
    );
    
    console.log(`离线时间: ${offlineTime / 3600}小时`);
    console.log(`基础出货速度: ${baseDeliveryLine.deliverySpeed}秒`);
    console.log(`实际出货速度: ${result.actualDeliverySpeed}秒`);
    console.log(`速度加成倍数: ${result.speedMultiplier}x`);
    console.log('');
    console.log(`基础方块价格: ${baseDeliveryLine.blockPrice} GEM`);
    console.log(`实际方块价格: ${result.actualBlockPrice} GEM`);
    console.log(`价格加成倍数: ${result.priceMultiplier}x`);
    console.log('');
    console.log(`可完成出货次数: ${result.cycles}次`);
    console.log(`出售方块数量: ${result.deliveredBlocks}个`);
    console.log(`获得GEM: ${result.gem}`);
    console.log(`hasBoost: ${result.hasBoost}`);
    console.log('');
  });
}

// 测试不同离线时间
function testDifferentOfflineTimes() {
  console.log('⏰ 测试不同离线时间的收益\n');
  
  const offlineTimes = [
    { name: '30分钟', time: 30 * 60 },
    { name: '1小时', time: 1 * 60 * 60 },
    { name: '2小时', time: 2 * 60 * 60 },
    { name: '4小时', time: 4 * 60 * 60 },
    { name: '8小时', time: 8 * 60 * 60 },
    { name: '12小时', time: 12 * 60 * 60 }, // 应该被限制在8小时
    { name: '24小时', time: 24 * 60 * 60 }  // 应该被限制在8小时
  ];
  
  // 使用VIP + Speed Boost x2的组合
  const testVip = vipEffects;
  const testBooster = speedBoostEffects.x2;
  
  offlineTimes.forEach(timeCase => {
    const result = calculateOfflineRewardWithBoosts(
      baseDeliveryLine, 
      timeCase.time, 
      testVip, 
      testBooster
    );
    
    console.log(`📅 ${timeCase.name}:`);
    console.log(`实际计算时间: ${result.effectiveOfflineTime / 3600}小时`);
    console.log(`出货次数: ${result.cycles}次`);
    console.log(`获得GEM: ${result.gem}`);
    console.log('');
  });
}

// 测试方块数量限制
function testBlockLimitation() {
  console.log('📦 测试方块数量限制\n');
  
  const testCases = [
    { pendingBlocks: 10, name: '方块不足' },
    { pendingBlocks: 50, name: '方块适中' },
    { pendingBlocks: 200, name: '方块充足' },
    { pendingBlocks: 0, name: '无方块' }
  ];
  
  const offlineTime = 2 * 60 * 60; // 2小时
  
  testCases.forEach(testCase => {
    const testDeliveryLine = {
      ...baseDeliveryLine,
      pendingBlocks: testCase.pendingBlocks
    };
    
    const result = calculateOfflineRewardWithBoosts(
      testDeliveryLine, 
      offlineTime, 
      vipEffects, 
      speedBoostEffects.x2
    );
    
    console.log(`📊 ${testCase.name} (${testCase.pendingBlocks}个方块):`);
    console.log(`可出货次数: ${result.cycles}次`);
    console.log(`实际出售: ${result.deliveredBlocks}个方块`);
    console.log(`获得GEM: ${result.gem}`);
    console.log('');
  });
}

// 测试加成效果的准确性
function testBoostAccuracy() {
  console.log('🎯 测试加成效果计算准确性\n');
  
  const offlineTime = 1 * 60 * 60; // 1小时
  
  // 无加成基准
  const baseResult = calculateOfflineRewardWithBoosts(
    baseDeliveryLine, 
    offlineTime, 
    nonVipEffects, 
    speedBoostEffects.none
  );
  
  // VIP加成
  const vipResult = calculateOfflineRewardWithBoosts(
    baseDeliveryLine, 
    offlineTime, 
    vipEffects, 
    speedBoostEffects.none
  );
  
  // VIP + Speed Boost x2
  const vipBoostResult = calculateOfflineRewardWithBoosts(
    baseDeliveryLine, 
    offlineTime, 
    vipEffects, 
    speedBoostEffects.x2
  );
  
  console.log('📊 加成效果对比:');
  console.log('=====================================');
  console.log(`无加成:`);
  console.log(`  出货速度: ${baseResult.actualDeliverySpeed}秒`);
  console.log(`  方块价格: ${baseResult.actualBlockPrice} GEM`);
  console.log(`  出货次数: ${baseResult.cycles}次`);
  console.log(`  获得GEM: ${baseResult.gem}`);
  console.log('');
  
  console.log(`仅VIP加成:`);
  console.log(`  出货速度: ${vipResult.actualDeliverySpeed}秒`);
  console.log(`  方块价格: ${vipResult.actualBlockPrice} GEM`);
  console.log(`  出货次数: ${vipResult.cycles}次`);
  console.log(`  获得GEM: ${vipResult.gem}`);
  console.log(`  相比无加成提升: ${((vipResult.gem - baseResult.gem) / baseResult.gem * 100).toFixed(1)}%`);
  console.log('');
  
  console.log(`VIP + Speed Boost x2:`);
  console.log(`  出货速度: ${vipBoostResult.actualDeliverySpeed}秒`);
  console.log(`  方块价格: ${vipBoostResult.actualBlockPrice} GEM`);
  console.log(`  出货次数: ${vipBoostResult.cycles}次`);
  console.log(`  获得GEM: ${vipBoostResult.gem}`);
  console.log(`  相比无加成提升: ${((vipBoostResult.gem - baseResult.gem) / baseResult.gem * 100).toFixed(1)}%`);
  console.log(`  相比仅VIP提升: ${((vipBoostResult.gem - vipResult.gem) / vipResult.gem * 100).toFixed(1)}%`);
  console.log('');
}

// 测试API响应格式
function testApiResponseFormat() {
  console.log('📋 API响应格式测试\n');
  
  const offlineTime = 2 * 60 * 60; // 2小时
  const result = calculateOfflineRewardWithBoosts(
    baseDeliveryLine, 
    offlineTime, 
    vipEffects, 
    speedBoostEffects.x2
  );
  
  console.log('getOfflineReward API响应示例:');
  console.log(JSON.stringify({
    ok: true,
    data: {
      isOffline: true,
      offlineTime: offlineTime,
      offlineReward: result
    }
  }, null, 2));
  
  console.log('\nclaimOfflineReward API响应示例:');
  console.log(JSON.stringify({
    ok: true,
    data: {
      offlineTime: offlineTime,
      claimedReward: result,
      currentGem: 1000 + result.gem
    }
  }, null, 2));
  
  console.log('\n字段说明:');
  console.log('✅ gem: 应用加成后的实际离线收益');
  console.log('✅ hasBoost: 是否有任何加成效果');
  console.log('✅ speedMultiplier: 总的速度加成倍数');
  console.log('✅ priceMultiplier: 价格加成倍数');
  console.log('✅ deliveredBlocks: 实际出售的方块数量');
  console.log('✅ actualDeliverySpeed: 应用加成后的出货速度');
  console.log('✅ actualBlockPrice: 应用加成后的方块价格');
  console.log('');
}

// 运行所有测试
console.log('开始测试离线收益加成效果...\n');

testBoostCombinations();
testDifferentOfflineTimes();
testBlockLimitation();
testBoostAccuracy();
testApiResponseFormat();

console.log('🎉 所有测试完成！');
console.log('');
console.log('📝 总结:');
console.log('=====================================');
console.log('✅ VIP会员提供30%出货速度和20%价格加成');
console.log('✅ Speed Boost x2提供200%速度加成 (3.0x倍数)');
console.log('✅ Speed Boost x4提供400%速度加成 (5.0x倍数)');
console.log('✅ 加成效果可以叠加 (VIP × 道具)');
console.log('✅ 离线收益最大时间限制为8小时');
console.log('✅ 考虑了方块数量限制');
console.log('✅ API响应包含详细的加成信息');
console.log('✅ 计算结果使用3位小数精度');

console.log('\n=== 测试完成 ===');

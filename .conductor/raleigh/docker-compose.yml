
services:
  redis2:
    image: redis:6
    restart: always
    ports:
      - "6258:6379"
    privileged: true
    volumes:
      - ./redis_data2:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wolf_fun

  redisinsight:
    image: redislabs/redisinsight:latest
    restart: always
    ports:
      - "5578:5540"
    depends_on:
      - redis2
    volumes:
      - ./redis-insight2:/db
    networks:
      - wolf_fun

networks:
  wolf_fun:
    external: true
    
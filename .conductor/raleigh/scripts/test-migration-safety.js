// 测试迁移文件的安全性检查
// 运行命令: node scripts/test-migration-safety.js

const fs = require('fs');
const path = require('path');

// 需要检查的迁移文件
const MIGRATION_FILES = [
  '20250722000000-create-delivery-line-configs.js',
  '20250723000001-create-task-system-tables.js',
  '20250724062137-add-diamond-to-user-wallets.js'
];

function checkMigrationSafety() {
  console.log('🔍 检查迁移文件的安全性...\n');

  const migrationsDir = path.join(__dirname, '../migrations');
  let allPassed = true;

  MIGRATION_FILES.forEach(filename => {
    console.log(`📋 检查文件: ${filename}`);
    
    const filePath = path.join(migrationsDir, filename);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filename}`);
      allPassed = false;
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查项目
    const checks = getChecksForFile(filename);
    
    checks.forEach(check => {
      const passed = check.test(content);
      if (passed) {
        console.log(`   ✅ ${check.description}`);
      } else {
        console.log(`   ❌ ${check.description}`);
        allPassed = false;
      }
    });
    
    console.log('');
  });

  // 总结
  console.log('='.repeat(50));
  if (allPassed) {
    console.log('🎉 所有迁移文件都通过了安全性检查！');
    console.log('\n✅ 检查项目:');
    console.log('   - 表存在性检查');
    console.log('   - 列存在性检查');
    console.log('   - 索引重复处理');
    console.log('   - 错误处理机制');
  } else {
    console.log('❌ 部分迁移文件未通过安全性检查，请修复后重试。');
  }
  console.log('='.repeat(50));
}

function getChecksForFile(filename) {
  switch (filename) {
    case '20250722000000-create-delivery-line-configs.js':
      return [
        {
          description: '检查表是否存在',
          test: (content) => content.includes('showAllTables()') && content.includes('includes(\'delivery_line_configs\')')
        },
        {
          description: '索引创建有错误处理',
          test: (content) => content.includes('try') && content.includes('catch') && content.includes('addIndex')
        },
        {
          description: '有适当的日志输出',
          test: (content) => content.includes('console.log') && content.includes('跳过创建')
        },
        {
          description: '事务处理正确',
          test: (content) => content.includes('transaction') && content.includes('commit') && content.includes('rollback')
        }
      ];

    case '20250723000001-create-task-system-tables.js':
      return [
        {
          description: '检查所有表是否存在',
          test: (content) => {
            const tables = ['task_configs', 'user_task_statuses', 'task_config_versions', 'task_config_logs'];
            return tables.every(table => content.includes(`includes('${table}')`));
          }
        },
        {
          description: '索引创建有错误处理',
          test: (content) => content.includes('try') && content.includes('catch') && content.includes('addIndex')
        },
        {
          description: '使用showAllTables检查',
          test: (content) => content.includes('showAllTables()')
        }
      ];

    case '20250724062137-add-diamond-to-user-wallets.js':
      return [
        {
          description: '检查列是否存在',
          test: (content) => content.includes('describeTable') && content.includes('diamond')
        },
        {
          description: '有条件添加列',
          test: (content) => content.includes('if (!tableDescription.diamond)')
        },
        {
          description: '有适当的日志输出',
          test: (content) => content.includes('console.log') && content.includes('跳过添加')
        }
      ];

    default:
      return [];
  }
}

// 额外的语法检查
function checkSyntax() {
  console.log('\n🔧 检查语法正确性...\n');

  MIGRATION_FILES.forEach(filename => {
    console.log(`📋 语法检查: ${filename}`);
    
    const filePath = path.join(__dirname, '../migrations', filename);
    
    if (!fs.existsSync(filePath)) {
      console.log(`❌ 文件不存在: ${filename}`);
      return;
    }

    try {
      // 尝试require文件来检查语法
      delete require.cache[require.resolve(filePath)];
      const migration = require(filePath);
      
      // 检查必要的方法
      if (typeof migration.up === 'function') {
        console.log('   ✅ up 方法存在且为函数');
      } else {
        console.log('   ❌ up 方法不存在或不是函数');
      }
      
      if (typeof migration.down === 'function') {
        console.log('   ✅ down 方法存在且为函数');
      } else {
        console.log('   ❌ down 方法不存在或不是函数');
      }
      
    } catch (error) {
      console.log(`   ❌ 语法错误: ${error.message}`);
    }
    
    console.log('');
  });
}

// 运行检查
if (require.main === module) {
  checkMigrationSafety();
  checkSyntax();
}

module.exports = { checkMigrationSafety, checkSyntax };

#!/usr/bin/env node

/**
 * 农场配置系统生产环境部署脚本
 * 用于安全地将新配置系统部署到生产环境
 */

// 注意：部署脚本主要用于协调部署流程，不直接操作数据库
// 数据库操作通过调用其他脚本完成
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class FarmConfigDeployer {
  constructor() {
    this.backupPath = null;
    this.deploymentLog = [];
  }

  /**
   * 记录部署日志
   */
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, type, message };
    this.deploymentLog.push(logEntry);
    
    const prefix = {
      'info': '📝',
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'step': '🔄'
    }[type] || '📝';
    
    console.log(`${prefix} ${message}`);
  }

  /**
   * 运行命令
   */
  async runCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      this.log(`执行命令: ${command} ${args.join(' ')}`, 'step');
      
      const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        ...options
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ success: true, code });
        } else {
          resolve({ success: false, code });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  /**
   * 备份数据库
   */
  async backupDatabase() {
    try {
      this.log('开始备份数据库...', 'step');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `farm_config_backup_${timestamp}.sql`;
      this.backupPath = path.join(__dirname, 'backups', backupFileName);
      
      // 确保备份目录存在
      const backupDir = path.dirname(this.backupPath);
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // 执行数据库备份
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || '3306',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'wolf_fun'
      };

      const mysqldumpCmd = 'mysqldump';
      const mysqldumpArgs = [
        '-h', dbConfig.host,
        '-P', dbConfig.port,
        '-u', dbConfig.user,
        `-p${dbConfig.password}`,
        '--single-transaction',
        '--routines',
        '--triggers',
        dbConfig.database
      ];

      const result = await this.runCommand(mysqldumpCmd, mysqldumpArgs, {
        stdio: ['inherit', fs.openSync(this.backupPath, 'w'), 'inherit']
      });

      if (result.success) {
        this.log(`数据库备份成功: ${this.backupPath}`, 'success');
        return true;
      } else {
        this.log('数据库备份失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`数据库备份失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 检查环境依赖
   */
  async checkEnvironment() {
    this.log('检查环境依赖...', 'step');
    
    const checks = [
      { name: 'Node.js版本', command: 'node', args: ['--version'], expected: 'v22' },
      { name: 'MySQL连接', command: 'mysql', args: ['--version'] },
      { name: 'Redis连接', command: 'redis-cli', args: ['ping'] }
    ];

    let allPassed = true;

    for (const check of checks) {
      try {
        const result = await this.runCommand(check.command, check.args, { stdio: 'pipe' });
        if (result.success) {
          this.log(`${check.name}: 通过`, 'success');
        } else {
          this.log(`${check.name}: 失败`, 'error');
          allPassed = false;
        }
      } catch (error) {
        this.log(`${check.name}: 失败 - ${error.message}`, 'error');
        allPassed = false;
      }
    }

    return allPassed;
  }

  /**
   * 运行数据库迁移
   */
  async runMigrations() {
    this.log('运行数据库迁移...', 'step');
    
    try {
      // 运行迁移脚本
      const migrationScript = path.join(__dirname, '../migrations/20250721000000-create-farm-configs.js');
      
      if (!fs.existsSync(migrationScript)) {
        this.log('迁移文件不存在，跳过迁移', 'warning');
        return true;
      }

      // 这里应该使用实际的迁移命令，比如 sequelize-cli
      // 暂时使用直接执行迁移脚本的方式
      const result = await this.runCommand('node', ['-e', `
        const migration = require('${migrationScript}');
        const { QueryInterface, Sequelize } = require('sequelize');
        // 注意：部署脚本主要用于协调部署流程，不直接操作数据库
// 数据库操作通过调用其他脚本完成
        
        migration.up(sequelize.getQueryInterface(), Sequelize)
          .then(() => {
            console.log('迁移成功');
            process.exit(0);
          })
          .catch(err => {
            console.error('迁移失败:', err);
            process.exit(1);
          });
      `]);

      if (result.success) {
        this.log('数据库迁移成功', 'success');
        return true;
      } else {
        this.log('数据库迁移失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`数据库迁移失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 初始化配置数据
   */
  async initializeConfig() {
    this.log('初始化配置数据...', 'step');
    
    try {
      const initScript = path.join(__dirname, 'init-farm-config.js');
      const result = await this.runCommand('node', [initScript]);

      if (result.success) {
        this.log('配置数据初始化成功', 'success');
        return true;
      } else {
        this.log('配置数据初始化失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`配置数据初始化失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 验证部署
   */
  async validateDeployment() {
    this.log('验证部署结果...', 'step');
    
    try {
      const validationScript = path.join(__dirname, 'validate-migration.js');
      const result = await this.runCommand('node', [validationScript]);

      if (result.success) {
        this.log('部署验证成功', 'success');
        return true;
      } else {
        this.log('部署验证失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`部署验证失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 预热缓存
   */
  async warmupCache() {
    this.log('预热应用缓存...', 'step');

    try {
      // 通过API调用预热缓存
      const result = await this.runCommand('curl', [
        '-X', 'POST',
        'http://localhost:3000/api/admin/farm-config/warmup-cache'
      ], { stdio: 'pipe' });

      if (result.success) {
        this.log('缓存预热成功', 'success');
        return true;
      } else {
        this.log('缓存预热失败', 'warning');
        return false; // 缓存预热失败不影响部署
      }
    } catch (error) {
      this.log(`缓存预热失败: ${error.message}`, 'warning');
      return false; // 缓存预热失败不影响部署
    }
  }

  /**
   * 回滚部署
   */
  async rollback() {
    this.log('开始回滚部署...', 'step');
    
    if (!this.backupPath || !fs.existsSync(this.backupPath)) {
      this.log('没有找到备份文件，无法回滚', 'error');
      return false;
    }

    try {
      // 恢复数据库
      const dbConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || '3306',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'wolf_fun'
      };

      const mysqlCmd = 'mysql';
      const mysqlArgs = [
        '-h', dbConfig.host,
        '-P', dbConfig.port,
        '-u', dbConfig.user,
        `-p${dbConfig.password}`,
        dbConfig.database
      ];

      const result = await this.runCommand(mysqlCmd, mysqlArgs, {
        stdio: [fs.openSync(this.backupPath, 'r'), 'inherit', 'inherit']
      });

      if (result.success) {
        this.log('数据库回滚成功', 'success');
        return true;
      } else {
        this.log('数据库回滚失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`回滚失败: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * 生成部署报告
   */
  generateReport() {
    const reportPath = path.join(__dirname, 'deployment-report.json');
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'production',
      backupPath: this.backupPath,
      logs: this.deploymentLog,
      summary: {
        totalSteps: this.deploymentLog.length,
        successSteps: this.deploymentLog.filter(log => log.type === 'success').length,
        errorSteps: this.deploymentLog.filter(log => log.type === 'error').length,
        warningSteps: this.deploymentLog.filter(log => log.type === 'warning').length
      }
    };

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    this.log(`部署报告已保存: ${reportPath}`, 'info');
    
    return report;
  }

  /**
   * 执行完整部署
   */
  async deploy() {
    this.log('🚀 开始农场配置系统生产环境部署', 'info');
    this.log('='.repeat(60), 'info');

    try {
      // 1. 环境检查
      const envOk = await this.checkEnvironment();
      if (!envOk) {
        this.log('环境检查失败，终止部署', 'error');
        return false;
      }

      // 2. 备份数据库
      const backupOk = await this.backupDatabase();
      if (!backupOk) {
        this.log('数据库备份失败，终止部署', 'error');
        return false;
      }

      // 3. 运行迁移
      const migrationOk = await this.runMigrations();
      if (!migrationOk) {
        this.log('数据库迁移失败，开始回滚', 'error');
        await this.rollback();
        return false;
      }

      // 4. 初始化配置
      const initOk = await this.initializeConfig();
      if (!initOk) {
        this.log('配置初始化失败，开始回滚', 'error');
        await this.rollback();
        return false;
      }

      // 5. 验证部署
      const validationOk = await this.validateDeployment();
      if (!validationOk) {
        this.log('部署验证失败，开始回滚', 'error');
        await this.rollback();
        return false;
      }

      // 6. 预热缓存
      await this.warmupCache();

      // 7. 生成报告
      const report = this.generateReport();

      this.log('='.repeat(60), 'info');
      this.log('🎉 农场配置系统部署成功！', 'success');
      this.log(`📊 成功步骤: ${report.summary.successSteps}/${report.summary.totalSteps}`, 'info');
      this.log(`💾 备份文件: ${this.backupPath}`, 'info');

      return true;

    } catch (error) {
      this.log(`部署过程中发生错误: ${error.message}`, 'error');
      this.log('开始回滚...', 'warning');
      await this.rollback();
      return false;
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const rollback = args.includes('--rollback');

  const deployer = new FarmConfigDeployer();

  try {
    if (rollback) {
      console.log('⚠️  回滚模式：请确保您有有效的备份文件');
      const success = await deployer.rollback();
      process.exit(success ? 0 : 1);
    } else {
      const success = await deployer.deploy();
      process.exit(success ? 0 : 1);
    }
  } catch (error) {
    console.error('❌ 部署失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FarmConfigDeployer;

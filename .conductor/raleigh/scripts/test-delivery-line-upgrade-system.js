const { DeliveryLine } = require('../src/models/DeliveryLine');
const { DeliveryLineConfig } = require('../src/models/DeliveryLineConfig');
const { UserWallet } = require('../src/models/UserWallet');
const { sequelize } = require('../src/config/db');

async function testDeliveryLineUpgradeSystem() {
    console.log('🧪 开始测试流水线升级系统...\n');
    
    try {
        // 1. 测试配置数据
        console.log('1️⃣ 测试配置数据...');
        const configs = await DeliveryLineConfig.getAllConfigs();
        console.log(`✅ 找到 ${configs.length} 个配置`);
        
        if (configs.length !== 50) {
            throw new Error(`配置数量不正确，期望50个，实际${configs.length}个`);
        }
        
        // 验证关键等级的配置
        const level1 = await DeliveryLineConfig.getConfigByGrade(1);
        const level50 = await DeliveryLineConfig.getConfigByGrade(50);
        
        console.log(`等级1配置: 利润=${level1.profit}, 容量=${level1.capacity}, 间隔=${level1.production_interval}s, 费用=${level1.upgrade_cost}`);
        console.log(`等级50配置: 利润=${level50.profit}, 容量=${level50.capacity}, 间隔=${level50.production_interval}s, 费用=${level50.upgrade_cost}`);
        
        // 2. 测试流水线初始化
        console.log('\n2️⃣ 测试流水线初始化...');
        
        // 创建测试用户
        let testUser = await UserWallet.findOne({ where: { walletAddress: 'test-delivery-line-upgrade' } });
        if (!testUser) {
            testUser = await UserWallet.create({
                walletAddress: 'test-delivery-line-upgrade',
                username: 'test-user',
                gem: '1000000.000', // 给足够的GEM用于测试
                milk: '0.000'
            });
        } else {
            // 更新GEM数量
            testUser.gem = '1000000.000';
            await testUser.save();
        }
        
        // 删除现有的流水线
        await DeliveryLine.destroy({ where: { walletId: testUser.id } });
        
        // 使用新配置初始化流水线
        const deliveryLine = await DeliveryLine.initializeWithConfig(testUser.id);
        console.log(`✅ 初始化流水线: 等级=${deliveryLine.level}, 速度=${deliveryLine.deliverySpeed}s, 容量=${deliveryLine.blockUnit}, 价格=${deliveryLine.blockPrice}`);
        
        // 验证初始化数据与配置一致
        const initialConfig = await deliveryLine.getConfig();
        if (deliveryLine.deliverySpeed !== initialConfig.production_interval ||
            deliveryLine.blockUnit !== initialConfig.capacity ||
            deliveryLine.blockPrice !== initialConfig.profit) {
            throw new Error('初始化数据与配置不一致');
        }
        
        // 3. 测试升级功能
        console.log('\n3️⃣ 测试升级功能...');
        
        const upgradeCost = await deliveryLine.getUpgradeCost();
        console.log(`升级成本: ${upgradeCost} GEM`);
        
        const canUpgrade = await deliveryLine.canUpgrade();
        console.log(`可以升级: ${canUpgrade}`);
        
        if (canUpgrade) {
            const beforeLevel = deliveryLine.level;
            const beforeSpeed = deliveryLine.deliverySpeed;
            const beforeCapacity = deliveryLine.blockUnit;
            const beforePrice = deliveryLine.blockPrice;
            
            // 执行升级
            await deliveryLine.upgradeWithConfig();
            await deliveryLine.save();
            
            console.log(`升级前: 等级=${beforeLevel}, 速度=${beforeSpeed}s, 容量=${beforeCapacity}, 价格=${beforePrice}`);
            console.log(`升级后: 等级=${deliveryLine.level}, 速度=${deliveryLine.deliverySpeed}s, 容量=${deliveryLine.blockUnit}, 价格=${deliveryLine.blockPrice}`);
            
            // 验证升级后的数据与配置一致
            const newConfig = await deliveryLine.getConfig();
            if (deliveryLine.deliverySpeed !== newConfig.production_interval ||
                deliveryLine.blockUnit !== newConfig.capacity ||
                deliveryLine.blockPrice !== newConfig.profit) {
                throw new Error('升级后数据与配置不一致');
            }
            
            console.log('✅ 升级功能正常');
        }
        
        // 4. 测试多级升级
        console.log('\n4️⃣ 测试多级升级...');
        
        let upgradeCount = 0;
        const maxUpgrades = 5; // 测试升级5次
        
        while (upgradeCount < maxUpgrades && await deliveryLine.canUpgrade()) {
            const beforeLevel = deliveryLine.level;
            await deliveryLine.upgradeWithConfig();
            await deliveryLine.save();
            upgradeCount++;
            
            console.log(`第${upgradeCount}次升级: ${beforeLevel} → ${deliveryLine.level}`);
        }
        
        console.log(`✅ 完成 ${upgradeCount} 次升级测试`);
        
        // 5. 测试边界情况
        console.log('\n5️⃣ 测试边界情况...');
        
        // 测试等级50的情况
        const level50Line = await DeliveryLine.create({
            walletId: testUser.id,
            level: 50,
            deliverySpeed: level50.production_interval,
            blockUnit: level50.capacity,
            blockPrice: level50.profit,
            upgradeCost: level50.upgrade_cost,
            lastDeliveryTime: new Date(),
            pendingMilk: 0,
            pendingBlocks: 0
        });
        
        const canUpgradeLevel50 = await level50Line.canUpgrade();
        console.log(`等级50可以升级: ${canUpgradeLevel50}`);
        
        if (canUpgradeLevel50) {
            throw new Error('等级50不应该能够升级');
        }
        
        try {
            await level50Line.upgradeWithConfig();
            throw new Error('等级50升级应该抛出异常');
        } catch (error) {
            if (error.message.includes('已达到最高等级')) {
                console.log('✅ 等级50升级正确抛出异常');
            } else {
                throw error;
            }
        }
        
        // 6. 性能测试
        console.log('\n6️⃣ 性能测试...');
        
        const startTime = Date.now();
        
        // 测试批量配置查询
        for (let i = 1; i <= 50; i++) {
            await DeliveryLineConfig.getConfigByGrade(i);
        }
        
        const endTime = Date.now();
        const queryTime = endTime - startTime;
        
        console.log(`50次配置查询耗时: ${queryTime}ms (平均 ${(queryTime/50).toFixed(2)}ms/次)`);
        
        if (queryTime > 1000) {
            console.warn('⚠️ 配置查询性能较慢，建议添加缓存');
        } else {
            console.log('✅ 配置查询性能良好');
        }
        
        // 清理测试数据
        await DeliveryLine.destroy({ where: { walletId: testUser.id } });
        await UserWallet.destroy({ where: { id: testUser.id } });
        
        console.log('\n🎉 所有测试通过！流水线升级系统工作正常');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// 执行测试
if (require.main === module) {
    testDeliveryLineUpgradeSystem()
        .then(() => {
            console.log('\n✅ 测试完成');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ 测试异常:', error);
            process.exit(1);
        });
}

module.exports = { testDeliveryLineUpgradeSystem };

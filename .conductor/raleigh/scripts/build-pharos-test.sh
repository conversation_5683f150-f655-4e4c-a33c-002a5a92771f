#!/bin/bash

# Pharos Test 分支构建脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 开始构建 Pharos Test 环境...${NC}"

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker 未运行，请先启动 Docker${NC}"
    exit 1
fi

# 检查必要文件是否存在
echo -e "${YELLOW}📋 检查必要文件...${NC}"
required_files=(
    "Dockerfile.pharos-test"
    ".env.pharos-test"
    "docker-compose.pharos-test.yml"
    "package.json"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo -e "${RED}❌ 缺少必要文件: $file${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ $file${NC}"
done

# 清理旧的构建
echo -e "${YELLOW}🧹 清理旧的构建...${NC}"
docker compose -f docker-compose.pharos-test.yml down --remove-orphans
docker image prune -f

# 构建新镜像
echo -e "${YELLOW}🔨 构建 Docker 镜像...${NC}"
docker compose -f docker-compose.pharos-test.yml build --no-cache

# 检查构建结果
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker 镜像构建成功！${NC}"
else
    echo -e "${RED}❌ Docker 镜像构建失败${NC}"
    exit 1
fi

# 显示镜像信息
echo -e "${BLUE}📊 镜像信息:${NC}"
docker images | grep wolf-fun | grep pharos-test

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
docker compose -f docker-compose.pharos-test.yml up -d

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "尝试 $attempt/$max_attempts..."
    
    if curl -f http://localhost:3456/api/health/ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 健康检查通过！${NC}"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}❌ 健康检查失败，请检查日志${NC}"
        docker compose -f docker-compose.pharos-test.yml logs app
        exit 1
    fi
    
    sleep 5
    ((attempt++))
done

# 显示服务状态
echo -e "${BLUE}📊 服务状态:${NC}"
docker compose -f docker-compose.pharos-test.yml ps

echo -e "${GREEN}🎉 Pharos Test 环境构建并启动成功！${NC}"
echo -e "${BLUE}📋 访问信息:${NC}"
echo -e "   - API: http://localhost:3456"
echo -e "   - 健康检查: http://localhost:3456/api/health/ping"
echo -e "   - phpMyAdmin: http://localhost:8271"
echo -e "   - Redis 端口: 6258"
echo -e "   - MySQL 端口: 3671"

echo -e "${YELLOW}💡 常用命令:${NC}"
echo -e "   - 查看日志: docker compose -f docker-compose.pharos-test.yml logs -f"
echo -e "   - 停止服务: docker compose -f docker-compose.pharos-test.yml down"
echo -e "   - 重启服务: docker compose -f docker-compose.pharos-test.yml restart"

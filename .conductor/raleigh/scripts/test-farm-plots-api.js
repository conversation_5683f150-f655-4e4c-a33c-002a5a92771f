#!/usr/bin/env node

/**
 * 测试农场区块API脚本
 * 用于验证外键约束和新配置系统集成
 */

const axios = require('axios');

async function testFarmPlotsAPI() {
  console.log('🧪 测试农场区块API\n');

  const baseUrl = 'http://localhost:3456/api';
  
  try {
    // 1. 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await axios.get(`${baseUrl}/health`);
    console.log(`✅ 健康检查: ${healthResponse.status} - ${healthResponse.data.message}`);

    // 2. 测试农场配置API
    console.log('\n2. 测试农场配置API...');
    const configResponse = await axios.get(`${baseUrl}/admin/farm-config/current`);
    console.log(`✅ 配置API: ${configResponse.status} - ${configResponse.data.data.totalConfigs} 条配置`);

    // 3. 测试农场区块API（这里会触发外键约束问题）
    console.log('\n3. 测试农场区块API...');
    
    // 使用一个测试用的 walletId
    const testWalletId = 1;
    
    try {
      const farmPlotsResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
        headers: {
          'wallet-id': testWalletId
        }
      });
      
      console.log(`✅ 农场区块API: ${farmPlotsResponse.status}`);
      console.log(`📊 返回数据: ${farmPlotsResponse.data.data.length} 个农场区块`);
      
      if (farmPlotsResponse.data.data.length > 0) {
        const firstPlot = farmPlotsResponse.data.data[0];
        console.log(`📦 第一个区块: 等级${firstPlot.level}, 产量=${firstPlot.milkProduction}, 牛=${firstPlot.barnCount}`);
        
        // 检查是否使用了新配置
        if (firstPlot.nextUpgradeGrowth) {
          console.log(`🔧 升级预览: 产量增长=${firstPlot.nextUpgradeGrowth.milkProductionGrowth}`);
          console.log(`✅ 新配置系统已集成`);
        } else {
          console.log(`⚠️ 升级预览数据缺失`);
        }
      }
      
    } catch (farmError) {
      if (farmError.response) {
        console.log(`❌ 农场区块API错误: ${farmError.response.status} - ${farmError.response.data.message}`);
        
        if (farmError.response.data.message.includes('foreign key constraint')) {
          console.log(`\n🔍 外键约束错误分析:`);
          console.log(`   - 问题: walletId=${testWalletId} 在 user_wallets 表中不存在`);
          console.log(`   - 解决方案: 需要先创建用户钱包记录`);
          
          // 尝试创建测试用户
          await createTestUser(baseUrl, testWalletId);
          
          // 重新测试
          console.log(`\n🔄 重新测试农场区块API...`);
          const retryResponse = await axios.get(`${baseUrl}/farm/farm-plots`, {
            headers: {
              'wallet-id': testWalletId
            }
          });
          
          console.log(`✅ 重试成功: ${retryResponse.status}`);
          console.log(`📊 返回数据: ${retryResponse.data.data.length} 个农场区块`);
        }
      } else {
        console.log(`❌ 农场区块API网络错误: ${farmError.message}`);
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

async function createTestUser(baseUrl, walletId) {
  console.log(`\n🔧 创建测试用户 (walletId=${walletId})...`);
  
  try {
    // 这里需要根据实际的用户创建API来调整
    // 暂时使用模拟的方式
    const testUserData = {
      walletAddress: `test_wallet_${walletId}`,
      // 其他必要字段...
    };
    
    // 如果有用户创建API，可以这样调用：
    // const createResponse = await axios.post(`${baseUrl}/user/create`, testUserData);
    
    console.log(`⚠️ 需要手动创建测试用户，或者使用现有的用户ID`);
    console.log(`💡 建议: 查询现有用户并使用有效的 walletId`);
    
  } catch (error) {
    console.log(`❌ 创建测试用户失败: ${error.message}`);
  }
}

async function queryExistingUsers(baseUrl) {
  console.log('\n🔍 查询现有用户...');
  
  try {
    // 如果有用户查询API，可以这样调用：
    // const usersResponse = await axios.get(`${baseUrl}/user/list`);
    
    console.log(`💡 建议: 使用数据库查询现有用户:`);
    console.log(`   SELECT id, walletAddress FROM user_wallets LIMIT 5;`);
    
  } catch (error) {
    console.log(`❌ 查询用户失败: ${error.message}`);
  }
}

// 主函数
async function main() {
  console.log('🚀 农场区块API测试开始');
  console.log('='.repeat(50));
  
  await testFarmPlotsAPI();
  
  console.log('\n' + '='.repeat(50));
  console.log('📋 测试总结:');
  console.log('1. 如果遇到外键约束错误，需要使用有效的 walletId');
  console.log('2. 新配置系统已集成到农场功能中');
  console.log('3. 建议查询数据库获取有效的用户ID进行测试');
  
  console.log('\n💡 下一步操作:');
  console.log('- 查询现有用户: SELECT id FROM user_wallets LIMIT 5;');
  console.log('- 使用有效ID测试: curl -H "wallet-id: <valid_id>" http://localhost:3456/api/farm/farm-plots');
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = { testFarmPlotsAPI, createTestUser };

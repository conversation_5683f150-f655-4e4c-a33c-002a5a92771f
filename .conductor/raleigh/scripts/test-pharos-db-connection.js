#!/usr/bin/env node

// Pharos Test 数据库连接测试脚本

const { Sequelize } = require('sequelize');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 测试 Pharos Test 数据库连接...');
  console.log('📊 配置信息:');
  console.log(`   数据库: ${process.env.DB_NAME}`);
  console.log(`   用户: ${process.env.DB_USER}`);
  console.log(`   主机: ${process.env.DB_HOST}`);
  console.log(`   端口: ${process.env.DB_PORT}`);
  
  const sequelize = new Sequelize(
    process.env.DB_NAME || 'pharos_test_db',
    process.env.DB_USER || 'pharos_test',
    process.env.DB_PASS || '00321zixunadmin',
    {
      host: process.env.DB_HOST || 'localhost',
      dialect: 'mysql',
      port: Number(process.env.DB_PORT) || 3671,
      logging: false,
      timezone: '+08:00',
      dialectOptions: {
        charset: 'utf8mb4',
        connectTimeout: 60000,
      },
      pool: {
        max: 5,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    }
  );

  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 查询表信息
    const [results] = await sequelize.query('SHOW TABLES');
    console.log('📋 数据库表列表:');
    results.forEach(row => {
      const tableName = Object.values(row)[0];
      console.log(`   - ${tableName}`);
    });

    // 测试查询用户表
    const [userCount] = await sequelize.query('SELECT COUNT(*) as count FROM users');
    console.log(`👥 用户数量: ${userCount[0].count}`);

    // 测试查询钱包表
    const [walletCount] = await sequelize.query('SELECT COUNT(*) as count FROM user_wallets');
    console.log(`💰 钱包数量: ${walletCount[0].count}`);

    // 测试查询农场表
    const [farmCount] = await sequelize.query('SELECT COUNT(*) as count FROM farm_plots');
    console.log(`🚜 农场区数量: ${farmCount[0].count}`);

    // 测试查询配送线表
    const [deliveryCount] = await sequelize.query('SELECT COUNT(*) as count FROM delivery_lines');
    console.log(`🚚 配送线数量: ${deliveryCount[0].count}`);

    // 测试查询PHRS充值表
    const [phrsCount] = await sequelize.query('SELECT COUNT(*) as count FROM phrs_deposits');
    console.log(`💎 PHRS充值记录: ${phrsCount[0].count}`);

    console.log('🎉 数据库连接测试完成！');

  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// 运行测试
if (require.main === module) {
  testConnection()
    .then(() => {
      console.log('✅ 测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testConnection };

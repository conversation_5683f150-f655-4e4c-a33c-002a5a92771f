// 测试钻石字段的正确性
// 运行命令: node scripts/test-diamond-field.js

// 模拟新的generateChestReward函数
function generateChestReward() {
  const random = Math.random() * 100;
  
  const items = [];
  let level = 1;

  if (random < 40) {
    level = 1;
    items.push({ type: 'fragment_green', amount: 16 });
    items.push({ type: 'diamond', amount: 389712 });
  }
  else if (random < 70) {
    level = 2;
    items.push({ type: 'fragment_blue', amount: 4 });
    items.push({ type: 'diamond', amount: 519616 });
  }
  else if (random < 90) {
    level = 3;
    items.push({ type: 'fragment_purple', amount: 1 });
    items.push({ type: 'diamond', amount: 779425 });
  }
  else {
    level = 4;
    items.push({ type: 'fragment_gold', amount: 1 });
    items.push({ type: 'diamond', amount: 1558850 });
  }

  return { level, items };
}

function testDiamondField() {
  console.log('🔍 测试钻石字段的正确性...\n');
  
  const testCount = 1000;
  let diamondCount = 0;
  let gemCount = 0;
  let totalDiamond = 0;
  
  for (let i = 0; i < testCount; i++) {
    const reward = generateChestReward();
    
    reward.items.forEach(item => {
      if (item.type === 'diamond') {
        diamondCount++;
        totalDiamond += item.amount;
      } else if (item.type === 'gem') {
        gemCount++;
      }
    });
  }
  
  console.log(`📊 测试结果 (${testCount}次):`);
  console.log(`   钻石奖励次数: ${diamondCount}`);
  console.log(`   宝石奖励次数: ${gemCount}`);
  console.log(`   钻石总数: ${totalDiamond.toLocaleString()}`);
  
  // 验证结果
  console.log('\n✅ 验证结果:');
  if (diamondCount === testCount) {
    console.log('   ✓ 每个宝箱都包含钻石奖励');
  } else {
    console.log('   ✗ 钻石奖励数量不正确');
  }
  
  if (gemCount === 0) {
    console.log('   ✓ 没有使用旧的gem字段');
  } else {
    console.log('   ✗ 仍在使用旧的gem字段');
  }
  
  // 验证钻石数量是否符合预期
  const expectedDiamonds = [389712, 519616, 779425, 1558850];
  let validDiamonds = true;
  
  for (let i = 0; i < 100; i++) {
    const reward = generateChestReward();
    const diamondItem = reward.items.find(item => item.type === 'diamond');
    
    if (diamondItem && !expectedDiamonds.includes(diamondItem.amount)) {
      validDiamonds = false;
      break;
    }
  }
  
  if (validDiamonds) {
    console.log('   ✓ 钻石数量符合预期配置');
  } else {
    console.log('   ✗ 钻石数量不符合预期配置');
  }
  
  console.log('\n🎯 字段映射验证:');
  console.log('   金牛币 → diamond 字段 ✓');
  console.log('   宝石 → gem 字段 (保留用于其他功能)');
  
  console.log('\n📋 数据库字段说明:');
  console.log('   - diamond: 钻石/金牛币 (宝箱奖励)');
  console.log('   - gem: 宝石 (其他功能使用)');
  console.log('   - fragment_*: 各种颜色的碎片');
}

// 运行测试
testDiamondField();

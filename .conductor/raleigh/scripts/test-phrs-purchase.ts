import { sequelize } from '../src/config/db';
import { UserWallet, IapProduct, IapPurchase, VipMembership, TimeWarpHistory, FarmPlot, DeliveryLine } from '../src/models';
import { PhrsPaymentController } from '../src/controllers/phrsPaymentController';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';

const phrsController = new PhrsPaymentController();

async function testPhrsPurchase() {
  try {
    console.log('🚀 开始测试PHRS购买功能...\n');

    // 1. 创建测试用户钱包
    console.log('1. 创建测试用户钱包...');
    const testWallet = await UserWallet.create({
      userId: 1, // 假设用户ID为1
      walletAddress: '0xtest123456789',
      phrsBalance: '10000', // 给用户10000 PHRS余额
      gem: '0',
      milk: 0,
      phrsWalletAddress: '0xphrstest123456789',
      lastPhrsUpdateTime: new Date()
    });
    console.log(`✅ 测试钱包创建成功，ID: ${testWallet.id}, PHRS余额: ${testWallet.phrsBalance}`);

    // 为用户创建农场区和出货线（时间跳跃功能需要）
    console.log('   创建农场区和出货线...');
    await FarmPlot.create({
      walletId: testWallet.id,
      plotNumber: 1,
      level: 1,
      isUnlocked: true,
      barnCount: 1,
      milkProduction: 1,
      productionSpeed: 5,
      unlockCost: 0,
      upgradeCost: 200,
      lastProductionTime: new Date(),
      accumulatedMilk: 0
    });

    await DeliveryLine.create({
      walletId: testWallet.id,
      level: 1,
      deliverySpeed: 1.0,
      blockUnit: 10,
      blockPrice: 1.0,
      upgradeCost: 500,
      lastDeliveryTime: new Date(),
      pendingMilk: 0,
      pendingBlocks: 0
    });
    console.log('   ✅ 农场区和出货线创建完成\n');

    // 2. 获取支持PHRS支付的商品列表
    console.log('2. 获取支持PHRS支付的商品列表...');
    const products = await IapProduct.findAll({
      where: { isActive: true },
      order: [['type', 'ASC'], ['priceUsd', 'ASC']]
    });

    const phrsProducts = products.filter(product => 
      product.pricePhrs != null || product.priceKaia != null
    );

    console.log('✅ 支持PHRS支付的商品:');
    phrsProducts.forEach(product => {
      const phrsPrice = product.pricePhrs || product.priceKaia;
      console.log(`   - ${product.name} (${product.type}): ${phrsPrice} PHRS, 每日限购: ${product.dailyLimit}, 账号限购: ${product.accountLimit || '无限制'}`);
    });
    console.log('');

    // 3. 测试购买Time Warp 1hr
    console.log('3. 测试购买Time Warp 1hr...');
    const timeWarp1hr = phrsProducts.find(p => p.productId === 'time_warp_1hr');
    if (timeWarp1hr) {
      const mockReq: any = {
        user: { walletId: testWallet.id },
        body: { productId: timeWarp1hr.id },
        t: (key: string) => key // 简单的翻译函数
      };
      const mockRes: any = {
        status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
        json: (data: any) => console.log('Response:', data)
      };

      console.log(`   尝试购买 ${timeWarp1hr.name}，价格: ${timeWarp1hr.pricePhrs || timeWarp1hr.priceKaia} PHRS`);
      
      // 检查购买前的状态
      const beforeWallet = await UserWallet.findByPk(testWallet.id);
      const beforeGems = new BigNumber(beforeWallet?.gem?.toString() || '0');
      console.log(`   购买前 - PHRS余额: ${beforeWallet?.phrsBalance}, GEM: ${beforeGems.toFixed(3)}`);

      // 执行购买
      await phrsController.purchaseWithPhrs(mockReq, mockRes);

      // 检查购买后的状态
      const afterWallet = await UserWallet.findByPk(testWallet.id);
      const afterGems = new BigNumber(afterWallet?.gem?.toString() || '0');
      const gemsEarned = afterGems.minus(beforeGems);
      console.log(`   购买后 - PHRS余额: ${afterWallet?.phrsBalance}, GEM: ${afterGems.toFixed(3)}, 获得GEM: ${gemsEarned.toFixed(3)}`);

      // 检查时间跳跃历史
      const timeWarpHistory = await TimeWarpHistory.findOne({
        where: { walletId: testWallet.id },
        order: [['createdAt', 'DESC']]
      });
      if (timeWarpHistory) {
        console.log(`   时间跳跃记录: ${timeWarpHistory.hours}小时, 获得GEM: ${timeWarpHistory.gemsEarned}, 生产牛奶: ${timeWarpHistory.milkProduced}, 处理牛奶: ${timeWarpHistory.milkProcessed}`);
      }
    }
    console.log('');

    // 4. 测试重复购买（应该失败，因为每日限购1次）
    console.log('4. 测试重复购买Time Warp 1hr（应该失败）...');
    if (timeWarp1hr) {
      const mockReq: any = {
        user: { walletId: testWallet.id },
        body: { productId: timeWarp1hr.id },
        t: (key: string) => key
      };
      const mockRes: any = {
        status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
        json: (data: any) => console.log('Response:', data)
      };

      await phrsController.purchaseWithPhrs(mockReq, mockRes);
    }
    console.log('');

    // 5. 测试购买VIP会员
    console.log('5. 测试购买VIP会员...');
    const vipProduct = phrsProducts.find(p => p.type === 'vip_membership');
    if (vipProduct) {
      const mockReq: any = {
        user: { walletId: testWallet.id },
        body: { productId: vipProduct.id },
        t: (key: string) => key
      };
      const mockRes: any = {
        status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
        json: (data: any) => console.log('Response:', data)
      };

      console.log(`   尝试购买 ${vipProduct.name}，价格: ${vipProduct.pricePhrs || vipProduct.priceKaia} PHRS`);
      await phrsController.purchaseWithPhrs(mockReq, mockRes);

      // 检查VIP会员状态
      const vipMembership = await VipMembership.findOne({
        where: { walletId: testWallet.id, isActive: true }
      });
      if (vipMembership) {
        console.log(`   VIP会员已激活: ${dayjs(vipMembership.startTime).format('YYYY-MM-DD HH:mm')} 到 ${dayjs(vipMembership.endTime).format('YYYY-MM-DD HH:mm')}`);
      }
    }
    console.log('');

    // 6. 测试重复购买VIP会员（应该失败）
    console.log('6. 测试重复购买VIP会员（应该失败）...');
    if (vipProduct) {
      const mockReq: any = {
        user: { walletId: testWallet.id },
        body: { productId: vipProduct.id },
        t: (key: string) => key
      };
      const mockRes: any = {
        status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
        json: (data: any) => console.log('Response:', data)
      };

      await phrsController.purchaseWithPhrs(mockReq, mockRes);
    }
    console.log('');

    // 7. 测试购买特殊套餐
    console.log('7. 测试购买特殊套餐...');
    const specialOffer = phrsProducts.find(p => p.type === 'special_offer');
    if (specialOffer) {
      const mockReq: any = {
        user: { walletId: testWallet.id },
        body: { productId: specialOffer.id },
        t: (key: string) => key
      };
      const mockRes: any = {
        status: (code: number) => ({ json: (data: any) => console.log(`Status ${code}:`, data) }),
        json: (data: any) => console.log('Response:', data)
      };

      console.log(`   尝试购买 ${specialOffer.name}，价格: ${specialOffer.pricePhrs || specialOffer.priceKaia} PHRS`);
      
      const beforeWallet = await UserWallet.findByPk(testWallet.id);
      const beforeGems = new BigNumber(beforeWallet?.gem?.toString() || '0');
      
      await phrsController.purchaseWithPhrs(mockReq, mockRes);
      
      const afterWallet = await UserWallet.findByPk(testWallet.id);
      const afterGems = new BigNumber(afterWallet?.gem?.toString() || '0');
      const gemsEarned = afterGems.minus(beforeGems);
      console.log(`   特殊套餐购买后获得GEM: ${gemsEarned.toFixed(3)}`);
    }
    console.log('');

    // 8. 显示最终状态
    console.log('8. 最终状态总结...');
    const finalWallet = await UserWallet.findByPk(testWallet.id);
    const purchases = await IapPurchase.findAll({
      where: { walletId: testWallet.id, paymentMethod: 'phrs' },
      include: [{ model: IapProduct }]
    });

    console.log(`   最终PHRS余额: ${finalWallet?.phrsBalance}`);
    console.log(`   最终GEM余额: ${finalWallet?.gem}`);
    console.log(`   总购买次数: ${purchases.length}`);
    purchases.forEach(purchase => {
      console.log(`   - ${(purchase as any).IapProduct?.name}: ${purchase.amount} PHRS, 状态: ${purchase.status}`);
    });

    console.log('\n✅ PHRS购买功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testPhrsPurchase();

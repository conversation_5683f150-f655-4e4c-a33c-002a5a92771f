#!/usr/bin/env node

/**
 * 修复农场区块字段映射脚本
 * 1. milkProduction 使用 production 字段而不是 milk 字段
 * 2. upgradeCost 使用当前等级的 cost 而不是下一等级的 cost
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

class FieldMappingFixer {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT) || 3669,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASS || 'root',
      database: process.env.DB_NAME || 'wolf_fun_db'
    };
  }

  /**
   * 创建数据库连接
   */
  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      console.log('✅ 数据库连接成功');
      return connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取当前激活的配置
   */
  async getActiveConfigs(connection) {
    try {
      const [configs] = await connection.execute(
        'SELECT grade, production, cow, speed, milk, cost, offline FROM farm_configs WHERE isActive = 1 ORDER BY grade'
      );
      
      console.log(`📊 获取到 ${configs.length} 条激活配置`);
      return configs;
    } catch (error) {
      console.error('❌ 获取配置失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取需要修复的农场区块
   */
  async getFarmPlotsToFix(connection) {
    try {
      const [plots] = await connection.execute(`
        SELECT id, walletId, plotNumber, level, isUnlocked, 
               barnCount, milkProduction, productionSpeed, upgradeCost
        FROM farm_plots 
        ORDER BY walletId, plotNumber
      `);
      
      console.log(`🔍 找到 ${plots.length} 个农场区块需要检查`);
      return plots;
    } catch (error) {
      console.error('❌ 获取农场区块失败:', error.message);
      throw error;
    }
  }

  /**
   * 计算正确的配置值
   */
  calculateCorrectValues(plot, configs) {
    const { level, plotNumber, isUnlocked } = plot;
    
    // 获取对应等级的配置
    const levelConfig = configs.find(c => c.grade === level);
    
    if (!levelConfig) {
      console.warn(`⚠️ 找不到等级 ${level} 的配置，跳过区块 ${plotNumber}`);
      return null;
    }

    if (isUnlocked) {
      // 已解锁的区块使用配置数据
      return {
        barnCount: levelConfig.cow,
        milkProduction: levelConfig.production, // 使用 production 字段而不是 milk
        productionSpeed: levelConfig.speed,
        upgradeCost: levelConfig.cost // 使用当前等级的 cost 作为升级费用
      };
    } else {
      // 未解锁的区块
      return {
        barnCount: 0,
        milkProduction: 0,
        productionSpeed: 100, // 默认速度
        upgradeCost: 0
      };
    }
  }

  /**
   * 检查区块是否需要更新
   */
  needsUpdate(currentPlot, correctValues) {
    if (!correctValues) return false;

    const tolerance = 0.001; // 浮点数比较容差

    return (
      currentPlot.barnCount !== correctValues.barnCount ||
      Math.abs(parseFloat(currentPlot.milkProduction) - correctValues.milkProduction) > tolerance ||
      currentPlot.productionSpeed !== correctValues.productionSpeed ||
      Math.abs(parseFloat(currentPlot.upgradeCost) - correctValues.upgradeCost) > tolerance
    );
  }

  /**
   * 更新农场区块
   */
  async updateFarmPlot(connection, plotId, correctValues) {
    try {
      await connection.execute(`
        UPDATE farm_plots 
        SET barnCount = ?, milkProduction = ?, productionSpeed = ?, upgradeCost = ?, updatedAt = NOW()
        WHERE id = ?
      `, [
        correctValues.barnCount,
        correctValues.milkProduction,
        correctValues.productionSpeed,
        correctValues.upgradeCost,
        plotId
      ]);
      
      return true;
    } catch (error) {
      console.error(`❌ 更新区块 ${plotId} 失败:`, error.message);
      return false;
    }
  }

  /**
   * 执行修复
   */
  async fix(dryRun = false) {
    let connection;
    
    try {
      console.log('🚀 开始修复农场区块字段映射...\n');
      console.log(`模式: ${dryRun ? '预览模式（不会实际更新）' : '实际更新模式'}`);
      console.log('='.repeat(60));

      // 1. 创建数据库连接
      connection = await this.createConnection();

      // 2. 获取激活配置
      const configs = await this.getActiveConfigs(connection);
      
      if (configs.length === 0) {
        throw new Error('没有找到激活的配置数据');
      }

      // 显示配置映射信息
      console.log('\n📋 配置字段映射说明:');
      console.log('   milkProduction ← production 字段 (不是 milk 字段)');
      console.log('   upgradeCost ← 当前等级的 cost 字段');
      console.log('');

      // 3. 获取需要修复的农场区块
      const plots = await this.getFarmPlotsToFix(connection);

      // 4. 分析和更新
      let totalChecked = 0;
      let totalNeedsUpdate = 0;
      let totalUpdated = 0;
      let totalErrors = 0;

      console.log('\n📋 开始分析农场区块...\n');

      for (const plot of plots) {
        totalChecked++;
        
        // 计算正确的值
        const correctValues = this.calculateCorrectValues(plot, configs);
        
        if (!correctValues) {
          continue;
        }

        // 检查是否需要更新
        if (this.needsUpdate(plot, correctValues)) {
          totalNeedsUpdate++;
          
          console.log(`🔄 区块需要更新:`);
          console.log(`   用户: ${plot.walletId}, 区块: ${plot.plotNumber}, 等级: ${plot.level}, 解锁: ${plot.isUnlocked}`);
          console.log(`   当前值: 产量=${plot.milkProduction}, 费用=${plot.upgradeCost}`);
          console.log(`   正确值: 产量=${correctValues.milkProduction}, 费用=${correctValues.upgradeCost}`);
          
          if (!dryRun) {
            const success = await this.updateFarmPlot(connection, plot.id, correctValues);
            if (success) {
              totalUpdated++;
              console.log(`   ✅ 更新成功`);
            } else {
              totalErrors++;
              console.log(`   ❌ 更新失败`);
            }
          } else {
            console.log(`   📝 预览模式 - 将会更新`);
          }
          console.log('');
        }
      }

      // 5. 输出统计结果
      console.log('='.repeat(60));
      console.log('📊 修复统计:');
      console.log(`   检查的区块: ${totalChecked}`);
      console.log(`   需要更新: ${totalNeedsUpdate}`);
      
      if (!dryRun) {
        console.log(`   成功更新: ${totalUpdated}`);
        console.log(`   更新失败: ${totalErrors}`);
        
        if (totalUpdated > 0) {
          console.log('\n✅ 字段映射修复完成！农场区块现在使用正确的字段映射');
        }
      } else {
        console.log('\n📝 预览完成！使用 --execute 参数执行实际更新');
      }

      return {
        totalChecked,
        totalNeedsUpdate,
        totalUpdated,
        totalErrors
      };

    } catch (error) {
      console.error('❌ 修复失败:', error.message);
      throw error;
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--execute');
  
  if (dryRun) {
    console.log('⚠️  预览模式：将显示需要更新的内容，但不会实际修改数据');
    console.log('💡 使用 --execute 参数执行实际更新');
    console.log('');
  }

  const fixer = new FieldMappingFixer();

  try {
    const result = await fixer.fix(dryRun);
    
    if (dryRun && result.totalNeedsUpdate > 0) {
      console.log('\n🔄 要执行实际更新，请运行:');
      console.log('node scripts/fix-field-mapping.js --execute');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FieldMappingFixer;

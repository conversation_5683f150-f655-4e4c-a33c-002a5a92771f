import { sequelize } from '../src/config/db';
import { UserWallet, IapProduct, IapPurchase, FarmPlot, DeliveryLine, User } from '../src/models';
import { PhrsPaymentController } from '../src/controllers/phrsPaymentController';

const phrsController = new PhrsPaymentController();

async function testErrorHandling() {
  try {
    console.log('🚀 测试PHRS支付错误处理...\n');

    // 1. 创建测试用户和钱包
    console.log('1. 创建测试数据...');
    
    let user = await User.findOne({ where: { telegramId: 'errortest123' } });
    if (!user) {
      user = await User.create({
        telegramId: 'errortest123',
        username: 'errortest',
        authDate: Math.floor(Date.now() / 1000), // 使用秒级时间戳
        hash: 'testhash',
        hasFollowedChannel: false
      });
    }

    let wallet = await UserWallet.findOne({ where: { walletAddress: '0xerrortest123' } });
    if (!wallet) {
      wallet = await UserWallet.create({
        userId: user.id,
        walletAddress: '0xerrortest123',
        phrsBalance: '100', // 给少量余额测试余额不足
        gem: '0',
        milk: 0,
        phrsWalletAddress: '0xerrortest123',
        lastPhrsUpdateTime: new Date()
      });

      // 创建农场区和出货线
      await FarmPlot.create({
        walletId: wallet.id,
        plotNumber: 1,
        level: 1,
        isUnlocked: true,
        barnCount: 1,
        milkProduction: 1,
        productionSpeed: 5,
        unlockCost: 0,
        upgradeCost: 200,
        lastProductionTime: new Date(),
        accumulatedMilk: 0
      });

      await DeliveryLine.create({
        walletId: wallet.id,
        level: 1,
        deliverySpeed: 1.0,
        blockUnit: 10,
        blockPrice: 1.0,
        upgradeCost: 500,
        lastDeliveryTime: new Date(),
        pendingMilk: 0,
        pendingBlocks: 0
      });
    }

    console.log(`✅ 测试数据准备完成，钱包ID: ${wallet.id}, PHRS余额: ${wallet.phrsBalance}\n`);

    // 2. 测试各种错误情况
    const errorTests = [
      {
        name: '缺少productId参数',
        req: {
          user: { walletId: wallet.id },
          body: {},
          t: (key: string) => key
        }
      },
      {
        name: 'productId类型错误',
        req: {
          user: { walletId: wallet.id },
          body: { productId: "invalid" },
          t: (key: string) => key
        }
      },
      {
        name: '不存在的商品ID',
        req: {
          user: { walletId: wallet.id },
          body: { productId: 99999 },
          t: (key: string) => key
        }
      },
      {
        name: 'PHRS余额不足',
        req: {
          user: { walletId: wallet.id },
          body: { productId: 4 }, // 8小时时间跳跃，价格较高
          t: (key: string) => key
        }
      }
    ];

    for (const test of errorTests) {
      console.log(`测试: ${test.name}`);
      
      const mockRes: any = {
        status: (code: number) => ({
          json: (data: any) => {
            console.log(`   状态码: ${code}`);
            console.log(`   响应: ${JSON.stringify(data, null, 2)}`);
            return mockRes;
          }
        }),
        json: (data: any) => {
          console.log(`   响应: ${JSON.stringify(data, null, 2)}`);
          return mockRes;
        }
      };

      try {
        await phrsController.purchaseWithPhrs(test.req as any, mockRes);
      } catch (error) {
        console.log(`   异常: ${error}`);
      }
      console.log('');
    }

    // 3. 测试重复购买限制
    console.log('3. 测试重复购买限制...');
    
    // 先成功购买一次
    const timeWarpProduct = await IapProduct.findOne({
      where: { type: 'time_warp', duration: 1 }
    });

    if (timeWarpProduct) {
      // 更新钱包余额，确保有足够的PHRS
      await wallet.update({ phrsBalance: '1000' });

      console.log(`首次购买 ${timeWarpProduct.name}...`);
      const firstPurchaseReq = {
        user: { walletId: wallet.id },
        body: { productId: timeWarpProduct.id },
        t: (key: string) => key
      };

      const mockRes1: any = {
        status: (code: number) => ({
          json: (data: any) => {
            console.log(`   首次购买状态码: ${code}`);
            if (code === 200) {
              console.log(`   ✅ 首次购买成功: ${data.data?.productName}`);
            } else {
              console.log(`   ❌ 首次购买失败: ${data.message}`);
            }
            return mockRes1;
          }
        }),
        json: (data: any) => {
          console.log(`   ✅ 首次购买成功: ${data.data?.productName}`);
          return mockRes1;
        }
      };

      await phrsController.purchaseWithPhrs(firstPurchaseReq as any, mockRes1);

      // 立即重复购买
      console.log(`\n重复购买 ${timeWarpProduct.name}...`);
      const secondPurchaseReq = {
        user: { walletId: wallet.id },
        body: { productId: timeWarpProduct.id },
        t: (key: string) => key
      };

      const mockRes2: any = {
        status: (code: number) => ({
          json: (data: any) => {
            console.log(`   重复购买状态码: ${code}`);
            console.log(`   重复购买响应: ${JSON.stringify(data, null, 2)}`);
            return mockRes2;
          }
        }),
        json: (data: any) => {
          console.log(`   重复购买响应: ${JSON.stringify(data, null, 2)}`);
          return mockRes2;
        }
      };

      await phrsController.purchaseWithPhrs(secondPurchaseReq as any, mockRes2);
    }

    console.log('\n✅ 错误处理测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

testErrorHandling();

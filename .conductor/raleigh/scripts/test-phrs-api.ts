import axios from 'axios';
import { sequelize } from '../src/config/db';
import { UserWallet, FarmPlot, DeliveryLine } from '../src/models';

const API_BASE_URL = 'http://localhost:3000/api';

async function testPhrsAPI() {
  try {
    console.log('🚀 测试PHRS支付API接口...\n');

    // 1. 创建测试用户钱包
    console.log('1. 创建测试用户钱包...');
    const testWallet = await UserWallet.create({
      userId: 1,
      walletAddress: '0xapitest123456789',
      phrsBalance: '15000',
      gem: '0',
      milk: 0,
      phrsWalletAddress: '0xphrsapitest123456789',
      lastPhrsUpdateTime: new Date()
    });

    // 创建农场区和出货线
    await FarmPlot.create({
      walletId: testWallet.id,
      plotNumber: 1,
      level: 1,
      isUnlocked: true,
      barnCount: 1,
      milkProduction: 1,
      productionSpeed: 5,
      unlockCost: 0,
      upgradeCost: 200,
      lastProductionTime: new Date(),
      accumulatedMilk: 0
    });

    await DeliveryLine.create({
      walletId: testWallet.id,
      level: 1,
      deliverySpeed: 1.0,
      blockUnit: 10,
      blockPrice: 1.0,
      upgradeCost: 500,
      lastDeliveryTime: new Date(),
      pendingMilk: 0,
      pendingBlocks: 0
    });

    console.log(`✅ 测试钱包创建成功，ID: ${testWallet.id}, 钱包地址: ${testWallet.walletAddress}\n`);

    // 2. 测试健康检查接口
    console.log('2. 测试健康检查接口...');
    try {
      const healthResponse = await axios.get(`${API_BASE_URL}/phrs-payment/health`);
      console.log('✅ 健康检查成功:', healthResponse.data);
    } catch (error: any) {
      console.log('❌ 健康检查失败:', error.response?.data || error.message);
    }
    console.log('');

    // 3. 测试获取商品列表接口
    console.log('3. 测试获取商品列表接口...');
    try {
      const productsResponse = await axios.get(`${API_BASE_URL}/phrs-payment/products`, {
        headers: {
          'wallet-address': testWallet.walletAddress
        }
      });
      console.log('✅ 获取商品列表成功:');
      console.log(`   找到 ${productsResponse.data.data.products.length} 个商品`);
      productsResponse.data.data.products.slice(0, 3).forEach((product: any) => {
        console.log(`   - ${product.name}: ${product.phrsPrice} PHRS`);
      });
    } catch (error: any) {
      console.log('❌ 获取商品列表失败:', error.response?.data || error.message);
    }
    console.log('');

    // 4. 测试获取余额接口
    console.log('4. 测试获取余额接口...');
    try {
      const balanceResponse = await axios.get(`${API_BASE_URL}/phrs-payment/balance`, {
        headers: {
          'wallet-address': testWallet.walletAddress
        }
      });
      console.log('✅ 获取余额成功:');
      console.log(`   PHRS余额: ${balanceResponse.data.data.phrsBalance}`);
      console.log(`   购买历史: ${balanceResponse.data.data.recentPurchases.length} 条记录`);
    } catch (error: any) {
      console.log('❌ 获取余额失败:', error.response?.data || error.message);
    }
    console.log('');

    // 5. 测试购买接口
    console.log('5. 测试购买接口...');
    try {
      // 先获取一个商品ID
      const productsResponse = await axios.get(`${API_BASE_URL}/phrs-payment/products`, {
        headers: {
          'wallet-address': testWallet.walletAddress
        }
      });
      
      const timeWarpProduct = productsResponse.data.data.products.find((p: any) => 
        p.type === 'time_warp' && p.duration === 1
      );

      if (timeWarpProduct) {
        console.log(`   尝试购买: ${timeWarpProduct.name} (${timeWarpProduct.phrsPrice} PHRS)`);
        
        const purchaseResponse = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
          productId: timeWarpProduct.id
        }, {
          headers: {
            'wallet-address': testWallet.walletAddress,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ 购买成功:');
        console.log(`   购买ID: ${purchaseResponse.data.data.purchaseId}`);
        console.log(`   支付金额: ${purchaseResponse.data.data.phrsPaid} PHRS`);
        console.log(`   剩余余额: ${purchaseResponse.data.data.remainingBalance} PHRS`);
        console.log(`   消息: ${purchaseResponse.data.data.message}`);
      } else {
        console.log('❌ 未找到合适的测试商品');
      }
    } catch (error: any) {
      console.log('❌ 购买失败:', error.response?.data || error.message);
    }
    console.log('');

    // 6. 测试重复购买（应该失败）
    console.log('6. 测试重复购买（应该失败）...');
    try {
      const productsResponse = await axios.get(`${API_BASE_URL}/phrs-payment/products`, {
        headers: {
          'wallet-address': testWallet.walletAddress
        }
      });
      
      const timeWarpProduct = productsResponse.data.data.products.find((p: any) => 
        p.type === 'time_warp' && p.duration === 1
      );

      if (timeWarpProduct) {
        const purchaseResponse = await axios.post(`${API_BASE_URL}/phrs-payment/purchase`, {
          productId: timeWarpProduct.id
        }, {
          headers: {
            'wallet-address': testWallet.walletAddress,
            'Content-Type': 'application/json'
          }
        });
        
        console.log('❌ 重复购买应该失败，但成功了:', purchaseResponse.data);
      }
    } catch (error: any) {
      if (error.response?.status === 400) {
        console.log('✅ 重复购买被正确拦截:', error.response.data.message);
      } else {
        console.log('❌ 重复购买失败（非预期错误）:', error.response?.data || error.message);
      }
    }

    console.log('\n✅ PHRS支付API接口测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await axios.get(`${API_BASE_URL}/health`);
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 服务器未运行，请先启动服务器：npm run dev');
    console.log('   然后运行：npx ts-node scripts/test-phrs-api.ts');
    return;
  }

  await testPhrsAPI();
}

main();

#!/usr/bin/env node

/**
 * 农场配置系统集成测试脚本
 * 测试完整的配置管理流程
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
const XLSX = require('xlsx');

// 配置
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// 测试数据
const TEST_CONFIG_DATA = [
  ['等级', '每秒产出计算用值', '奶牛数量', '生产速度百分比', '牛奶生产', '升级花费', '离线产出'],
  [0, 0, 0, 0, 0, 13096, 0],
  [1, 182, 1, 100, 60.63, 20043, 90.94],
  [2, 232, 1, 100, 77.33, 28583, 115.99],
  [3, 276, 2, 110, 95.06, 39214, 137.84],
  [4, 315, 2, 110, 108.68, 52496, 157.59]
];

class FarmConfigIntegrationTest {
  constructor() {
    this.testResults = [];
    this.tempFiles = [];
  }

  /**
   * 记录测试结果
   */
  logResult(testName, success, message = '', data = null) {
    const result = {
      test: testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    if (data && process.env.VERBOSE) {
      console.log('   数据:', JSON.stringify(data, null, 2));
    }
  }

  /**
   * 创建测试Excel文件
   */
  createTestExcelFile() {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(TEST_CONFIG_DATA);
    XLSX.utils.book_append_sheet(workbook, worksheet, '农场配置');
    
    const filePath = path.join(__dirname, 'test_farm_config.xlsx');
    XLSX.writeFile(workbook, filePath);
    
    this.tempFiles.push(filePath);
    return filePath;
  }

  /**
   * 测试获取当前配置
   */
  async testGetCurrentConfig() {
    try {
      const response = await axios.get(`${API_BASE}/admin/farm-config/current`);
      
      if (response.status === 200 && response.data.ok) {
        this.logResult(
          '获取当前配置',
          true,
          `成功获取 ${response.data.data.totalConfigs} 条配置`,
          { configCount: response.data.data.totalConfigs }
        );
        return response.data.data;
      } else {
        this.logResult('获取当前配置', false, '响应格式错误');
        return null;
      }
    } catch (error) {
      this.logResult('获取当前配置', false, error.message);
      return null;
    }
  }

  /**
   * 测试获取版本列表
   */
  async testGetVersions() {
    try {
      const response = await axios.get(`${API_BASE}/admin/farm-config/versions`);
      
      if (response.status === 200 && response.data.ok) {
        this.logResult(
          '获取版本列表',
          true,
          `成功获取 ${response.data.data.versions.length} 个版本`,
          { versionCount: response.data.data.versions.length }
        );
        return response.data.data.versions;
      } else {
        this.logResult('获取版本列表', false, '响应格式错误');
        return null;
      }
    } catch (error) {
      this.logResult('获取版本列表', false, error.message);
      return null;
    }
  }

  /**
   * 测试配置验证
   */
  async testValidateConfig() {
    try {
      const filePath = this.createTestExcelFile();
      const formData = new FormData();
      formData.append('excelFile', fs.createReadStream(filePath));

      const response = await axios.post(
        `${API_BASE}/excel/farm-config/validate`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
        }
      );

      if (response.status === 200 && response.data.ok) {
        this.logResult(
          '配置验证',
          response.data.data.isValid,
          response.data.data.isValid ? '配置验证通过' : `验证失败: ${response.data.data.errors.join(', ')}`,
          { 
            isValid: response.data.data.isValid,
            configCount: response.data.data.configCount,
            errors: response.data.data.errors
          }
        );
        return response.data.data;
      } else {
        this.logResult('配置验证', false, '响应格式错误');
        return null;
      }
    } catch (error) {
      this.logResult('配置验证', false, error.message);
      return null;
    }
  }

  /**
   * 测试配置上传（需要管理员权限）
   */
  async testUploadConfig() {
    try {
      const filePath = this.createTestExcelFile();
      const formData = new FormData();
      formData.append('excelFile', fs.createReadStream(filePath));
      formData.append('versionName', `集成测试版本_${Date.now()}`);
      formData.append('description', '这是一个集成测试版本');

      const response = await axios.post(
        `${API_BASE}/excel/farm-config/upload`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
          },
        }
      );

      if (response.status === 200 && response.data.ok) {
        this.logResult(
          '配置上传',
          true,
          `成功上传版本: ${response.data.data.version}`,
          { version: response.data.data.version }
        );
        return response.data.data.version;
      } else {
        this.logResult('配置上传', false, response.data.message || '上传失败');
        return null;
      }
    } catch (error) {
      if (error.response && error.response.status === 403) {
        this.logResult('配置上传', false, '权限不足（需要管理员权限）');
      } else {
        this.logResult('配置上传', false, error.message);
      }
      return null;
    }
  }

  /**
   * 测试获取配置模板
   */
  async testGetTemplate() {
    try {
      const response = await axios.get(
        `${API_BASE}/excel/farm-config/template`,
        { responseType: 'arraybuffer' }
      );

      if (response.status === 200) {
        const templatePath = path.join(__dirname, 'downloaded_template.xlsx');
        fs.writeFileSync(templatePath, response.data);
        this.tempFiles.push(templatePath);

        this.logResult(
          '获取配置模板',
          true,
          `模板下载成功，大小: ${response.data.length} 字节`,
          { fileSize: response.data.length }
        );
        return templatePath;
      } else {
        this.logResult('获取配置模板', false, '下载失败');
        return null;
      }
    } catch (error) {
      this.logResult('获取配置模板', false, error.message);
      return null;
    }
  }

  /**
   * 测试统计信息
   */
  async testGetStats() {
    try {
      const response = await axios.get(`${API_BASE}/admin/farm-config/stats`);
      
      if (response.status === 200 && response.data.ok) {
        this.logResult(
          '获取统计信息',
          true,
          '统计信息获取成功',
          response.data.data
        );
        return response.data.data;
      } else {
        this.logResult('获取统计信息', false, '响应格式错误');
        return null;
      }
    } catch (error) {
      this.logResult('获取统计信息', false, error.message);
      return null;
    }
  }

  /**
   * 测试缓存预热
   */
  async testWarmupCache() {
    try {
      const response = await axios.post(`${API_BASE}/admin/farm-config/warmup-cache`);
      
      if (response.status === 200 && response.data.ok) {
        this.logResult('缓存预热', true, '缓存预热成功');
        return true;
      } else {
        this.logResult('缓存预热', false, '预热失败');
        return false;
      }
    } catch (error) {
      if (error.response && error.response.status === 403) {
        this.logResult('缓存预热', false, '权限不足（需要管理员权限）');
      } else {
        this.logResult('缓存预热', false, error.message);
      }
      return false;
    }
  }

  /**
   * 清理临时文件
   */
  cleanup() {
    this.tempFiles.forEach(filePath => {
      try {
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
          console.log(`🗑️  清理临时文件: ${filePath}`);
        }
      } catch (error) {
        console.warn(`⚠️  清理文件失败: ${filePath} - ${error.message}`);
      }
    });
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log('\n' + '='.repeat(60));
    console.log('🧪 农场配置系统集成测试报告');
    console.log('='.repeat(60));
    console.log(`📊 总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`📈 通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));

    if (failedTests > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`   • ${r.test}: ${r.message}`);
        });
    }

    // 保存详细报告
    const reportPath = path.join(__dirname, 'farm-config-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      summary: {
        totalTests,
        passedTests,
        failedTests,
        passRate: (passedTests / totalTests) * 100,
        timestamp: new Date().toISOString()
      },
      results: this.testResults
    }, null, 2));

    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    
    return passedTests === totalTests;
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始农场配置系统集成测试...\n');

    try {
      // 基础功能测试
      await this.testGetCurrentConfig();
      await this.testGetVersions();
      await this.testGetStats();
      await this.testGetTemplate();
      
      // 配置管理测试
      await this.testValidateConfig();
      await this.testUploadConfig();
      await this.testWarmupCache();

      // 生成报告
      const allPassed = this.generateReport();
      
      return allPassed;
    } catch (error) {
      console.error('❌ 测试执行失败:', error);
      return false;
    } finally {
      this.cleanup();
    }
  }
}

// 主函数
async function main() {
  const tester = new FarmConfigIntegrationTest();
  
  try {
    const success = await tester.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FarmConfigIntegrationTest;

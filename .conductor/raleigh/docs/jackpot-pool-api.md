# Jackpot奖池相关测试接口文档

## 设置奖池金额

### 接口说明
- 接口路径：`/api/test-chest/set-jackpot-pool-amount`
- 请求方式：POST
- 功能描述：设置指定等级奖池的金额，用于测试奖池相关功能

### 请求参数
```json
{
  "level": "number",  // 奖池等级，1-5
  "amount": "number"  // 要设置的奖池金额，单位为USDT
}
```

### 响应格式
```json
{
	"ok": true,
	"message": "1级奖池金额已设置为2",
	"data": {
		"level": 1,
		"currentAmount": 2,
		"targetAmount": 10,
		"newUserAmount": 2,
		"lastWinnerId": null,
		"lastWinnerWalletId": null,
		"lastWinTime": null
	}
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误信息",
  "errors": ["奖池等级无效", "金额必须为正数"]
}
```

## 重置所有奖池金额

### 接口说明
- 接口路径：`/api/test-chest/reset-all-jackpot-pools`
- 请求方式：POST
- 功能描述：将所有等级的奖池金额重置为0，用于测试场景重置

### 请求参数
无需请求参数

### 响应格式
```json
{
	"ok": true,
	"message": "已重置所有奖池金额为0",
	"data": {
		"resetResults": [
			{
				"level": 1,
				"newUserAmount": 0,
				"currentAmount": 0,
				"lastWinnerId": null,
				"lastWinnerWalletId": null,
				"lastWinTime": null
			},
			{
				"level": 2,
				"currentAmount": 0,
				"newUserAmount": 0,
				"chestOpenAmount": 0,
				"lastWinnerId": null,
				"lastWinnerWalletId": null,
				"lastWinTime": null
			},
			{
				"level": 3,
				"currentAmount": 0,
				"newUserAmount": 0,
				"chestOpenAmount": 0,
				"lastWinnerId": null,
				"lastWinnerWalletId": null,
				"lastWinTime": null
			}
		]
	}
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "重置奖池失败",
  "error": "内部服务器错误"
}
```

### 注意事项
1. 这两个接口仅用于测试环境，不应在生产环境中使用
2. 接口调用需要管理员权限
3. 金额设置会立即生效，请谨慎操作
4. 建议在测试完成后使用重置接口恢复初始状态
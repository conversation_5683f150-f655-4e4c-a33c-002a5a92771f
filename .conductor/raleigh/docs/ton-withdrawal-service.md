# TON提现服务使用指南

## 概述

本文档描述了TON提现服务的使用方法、配置和集成说明。该服务用于自动化处理用户的TON代币提现请求，通过定时任务定期检查并处理待批准的提现记录。

## 功能特性

- 定时检查并处理待批准的TON提现请求
- 自动执行TON转账操作
- 更新提现记录状态
- 提供详细的操作日志
- 错误处理和异常恢复

## 环境变量配置

在使用TON提现服务前，请确保以下环境变量已正确配置：

```
TONCENTER_API_KEY=<您的TON Center API密钥>
WALLET_A_SEED=<钱包助记词，以空格分隔>
WALLET_B=<目标钱包地址，可选>
TON_WITHDRAWAL_SCHEDULE=*/5 * * * * # 定时任务执行频率，默认每5分钟
```

## 集成到应用程序

要将TON提现服务集成到主应用程序中，需要对`app.ts`文件进行如下修改：

1. 导入相关模块：
   ```typescript
   import { integrateTonWithdrawal } from "./jobs/integrateTonWithdrawal";
   ```

2. 在工作进程路径配置中添加TON提现工作器：
   ```typescript
   const workerPaths = {
     // 其他工作器...
     
     tonWithdrawal: process.env.NODE_ENV === 'production'
       ? path.join(__dirname, 'jobs', 'withdrawalWorker.js')
       : path.join(__dirname, 'jobs', 'withdrawalWorker.ts'),
   };
   ```

3. 在启动其他工作进程后添加TON提现系统集成：
   ```typescript
   // 集成TON提现系统
   try {
     const tonWithdrawalSystem = await integrateTonWithdrawal(
       startWorkerProcess,
       setupWorkerEvents,
       workers,
       queues
     );
     console.log('TON提现系统已成功集成到应用程序中');
   } catch (error) {
     console.error('集成TON提现系统失败:', error);
   }
   ```

## 手动启动提现工作器

如需单独运行TON提现工作器（例如用于测试或调试），可以使用以下命令：

```bash
# 开发环境
npx ts-node src/jobs/startWithdrawalWorker.ts

# 生产环境
node dist/jobs/startWithdrawalWorker.js
```

## 工作原理

1. 定时任务调度器(`scheduleTonWithdrawalJob.ts`)按照配置的时间间隔创建任务
2. 工作器(`withdrawalWorker.ts`)接收任务并调用服务处理提现
3. 提现服务(`tonWithdrawalService.ts`)查询待处理记录并执行转账操作
4. 转账完成后更新数据库中的提现记录状态

## 错误处理

TON提现服务包含完善的错误处理机制：

- 对每笔提现交易使用单独的try/catch块，确保单笔交易失败不影响其他交易
- 使用数据库事务保证数据一致性
- 详细的日志记录，便于问题排查
- 失败的提现会被标记为'failed'状态，并记录错误信息

## 文件结构

- **src/jobs/withdrawalWorker.ts**: TON提现工作器
- **src/jobs/scheduleTonWithdrawalJob.ts**: TON提现任务调度器
- **src/jobs/tonWithdrawalQueue.ts**: TON提现队列配置
- **src/jobs/integrateTonWithdrawal.ts**: 应用集成模块
- **src/services/tonWithdrawalService.ts**: TON提现核心服务

## 注意事项

- 确保钱包中有足够的TON余额用于转账
- TON Center API有调用频率限制，请合理配置定时任务执行频率
- 提现地址必须是有效的TON地址
- 建议在生产环境部署前在测试网络上充分测试
# 获取四个宝箱领取状态 API

## 接口说明

该接口用于获取用户是否已经领取过四个宝箱的状态。

## 请求信息

- **接口路径**: `/api/jackpot-chest/four-chests-status`
- **请求方法**: GET
- **认证要求**: 需要钱包认证（walletAuthMiddleware）

## 请求参数

无需请求参数

## 响应数据

### 成功响应

```json
{
  "ok": true,
  "data": {
    "hasCollected": true  // 布尔值，表示用户是否已经领取过四个宝箱
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| hasCollected | boolean | 用户是否已经领取过四个宝箱 |

### 错误响应

```json
{
  "code": 401,
  "message": "Unauthorized",
  "data": null
}
```

## 错误码说明

| 错误码 | 描述 | 说明 |
|--------|------|------|
| 401 | Unauthorized | 用户未登录或认证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 示例

### 请求示例

```bash
curl -X GET \
  'http://api.example.com/api/jackpot-chest/four-chests-status' \
  -H 'Authorization: Bearer YOUR_TOKEN'
```

### 成功响应示例

```json
{
  "code": 0,
  "data": {
    "hasCollectedFourChests": false
  },
  "message": "success"
}
```
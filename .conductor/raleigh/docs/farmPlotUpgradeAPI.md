# 接口文档：升级牧场区

## 1. 概述

该接口用于升级用户指定的牧场区。升级会消耗用户的 GEM，并提升牧场区的等级、牛舍数量、牛奶产量等属性。

## 2. 请求

-   **URL**: `/api/farm-plots/upgrade`
-   **方法**: `POST`
-   **认证**: 需要用户认证

## 3. 请求体 (Request Body)

-   **Content-Type**: `application/json`

| 参数名      | 类型   | 是否必需 | 描述           | 示例        |
| ----------- | ------ | -------- | -------------- | ----------- |
| `plotNumber` | number | 是       | 要升级的牧场区编号 | `2`         |

**示例请求体:**

```json
{
  "plotNumber": 2
}
```

## 4. 响应 (Response)

### 4.1. 成功响应

-   **状态码**: `200 OK`
-   **Content-Type**: `application/json`

**响应体结构:**

```json
{
  "ok": true,
  "data": {
    "id": "integer",
    "walletId": "integer",
    "plotNumber": "integer",
    "level": "integer",
    "barnCount": "integer",
    "milkProduction": "number", // 单次产奶量 (3位小数精度)
    "productionSpeed": "number", // 生产速度 (秒/次, 3位小数精度)
    "unlockCost": "number",
    "upgradeCost": "number", // 下一级升级所需GEM
    "lastProductionTime": "string(date-time)",
    "isUnlocked": "boolean",
    "accumulatedMilk": "number", // 累积的牛奶量
    "createdAt": "string(date-time)",
    "updatedAt": "string(date-time)",
    "baseProduction": "number", // 基础产量 (3位小数精度)
    "nextUpgradeGrowth": {
      "nextProductionSpeed": "number", // 下次升级后的生产速度 (3位小数精度)
      "nextBarnCount": "integer", // 下次升级后的牛舍数量
      "nextMilkProduction": "number" // 下次升级后的产量 (3位小数精度)
    }
  }
}
```

**响应字段说明:**

-   `id`: 牧场区记录的唯一ID
-   `walletId`: 用户钱包ID
-   `plotNumber`: 牧场区编号 (1-20)
-   `level`: 升级后的牧场区等级
-   `barnCount`: 升级后的牛舍数量
-   `milkProduction`: 升级后，牧场区单次生产的牛奶量 (3位小数精度)
-   `productionSpeed`: 升级后，牧场区的生产速度（单位：秒/次，3位小数精度）
-   `unlockCost`: 解锁该牧场区所需的GEM
-   `upgradeCost`: 升级到下一等级所需的GEM数量
-   `lastProductionTime`: 上次产奶的时间戳
-   `isUnlocked`: 牧场区是否已解锁 (升级操作时应为 `true`)
-   `accumulatedMilk`: 累积的牛奶量
-   `createdAt`: 牧场区记录创建时间
-   `updatedAt`: 牧场区记录最后更新时间
-   `baseProduction`: 基础产量 (3位小数精度)
-   `nextUpgradeGrowth`: 下次升级预览数据对象，包含：
    -   `nextProductionSpeed`: 下次升级后的生产速度 (3位小数精度)
    -   `nextBarnCount`: 下次升级后的牛舍数量
    -   `nextMilkProduction`: 下次升级后的产量 (3位小数精度)

**示例成功响应:**

```json
{
  "ok": true,
  "data": {
    "id": 1,
    "walletId": 1,
    "plotNumber": 1,
    "level": 3,
    "barnCount": 3,
    "milkProduction": 2.25,
    "productionSpeed": 4.535,
    "unlockCost": 0,
    "upgradeCost": 675,
    "lastProductionTime": "2025-06-18T03:58:09.880Z",
    "isUnlocked": true,
    "accumulatedMilk": 0,
    "createdAt": "2025-06-18 07:17:29",
    "updatedAt": "2025-06-18T03:58:09.881Z",
    "baseProduction": 2.25,
    "nextUpgradeGrowth": {
      "nextProductionSpeed": 4.319,
      "nextBarnCount": 4,
      "nextMilkProduction": 3.375
    }
  }
}
```

### 4.2. 错误响应

-   **状态码**: `400 Bad Request` 或 `500 Internal Server Error`
-   **Content-Type**: `application/json`

**常见错误情况:**

```json
{
  "ok": false,
  "error": "用户钱包或牧场区不存在"
}
```

```json
{
  "ok": false,
  "error": "牧场区未解锁"
}
```

```json
{
  "ok": false,
  "error": "牧场区已达到最高等级"
}
```

```json
{
  "ok": false,
  "error": "GEM不足"
}
```

## 5. 升级公式说明

### 5.1. 升级计算公式

-   **产量计算**: 1 × (1.5)^(等级-1)
-   **生产速度**: 每次升级提升5%，新速度 = 当前速度 ÷ 1.05
-   **升级费用**: 每次升级提升1.5倍，新费用 = 当前费用 × 1.5
-   **牛舍数量**: 每次升级 +1个

### 5.2. nextUpgradeGrowth 说明

升级成功后，响应中的 `nextUpgradeGrowth` 对象包含下次升级的预览数据：
-   `nextProductionSpeed`: 再次升级后的生产速度
-   `nextBarnCount`: 再次升级后的牛舍数量
-   `nextMilkProduction`: 再次升级后的产量

如果农场区已达到最高等级（20级），`nextUpgradeGrowth` 将为 `null`。

## 6. 注意事项

-   升级牧场区会消耗用户钱包中的 `GEM` 货币
-   牧场区有最高等级限制（当前为20级）
-   只有已解锁的牧场区才能进行升级
-   升级后，`upgradeCost` 字段会更新为升到下一级的费用
-   所有数值使用BigNumber.js计算，确保3位小数精度
-   升级后立即返回包含 `nextUpgradeGrowth` 的完整数据，前端无需额外API调用
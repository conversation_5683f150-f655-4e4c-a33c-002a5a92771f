# PHRS支付购买接口文档

本文档详细说明了使用PHRS余额购买IAP道具的API接口，包括请求参数、响应格式、业务逻辑和错误处理。

## 基本信息

- **接口路径**: `/api/phrs-payment/purchase`
- **请求方法**: POST
- **认证要求**: 需要钱包认证（walletAuthMiddleware）
- **语言支持**: 支持国际化（languageMiddleware）
- **Content-Type**: application/json

## API 接口详情

### 使用PHRS购买IAP道具

使用用户的PHRS余额购买游戏内道具，支持时间跳跃、速度加成、VIP会员等多种商品类型。

**请求方法**: POST

**路径**: `/api/phrs-payment/purchase`

**请求体参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| productId | integer | 是 | 商品ID，必须大于等于1 |

**认证要求**: 
- 需要在请求头中包含有效的JWT token
- 通过 `Authorization: Bearer <token>` 方式传递

**请求体示例**:

```json
{
  "productId": 8
}
```

## 参数验证规则

接口使用 JSON Schema 进行严格的参数验证：

```javascript
{
  type: 'object',
  properties: {
    productId: {
      type: 'integer',
      minimum: 1
    }
  },
  required: ['productId'],
  additionalProperties: false
}
```

## 业务逻辑流程

### 1. 购买流程

1. **用户身份验证**: 检查用户钱包ID是否存在
2. **商品信息验证**: 验证商品存在且处于激活状态
3. **PHRS支付支持检查**: 验证商品是否支持PHRS支付
4. **价格计算**: 使用商品的pricePhrs字段作为PHRS价格
5. **余额检查**: 验证用户PHRS余额是否足够支付
6. **购买限制检查**: 验证每日限制、账号限制、VIP状态等
7. **原子操作扣款**: 使用数据库事务扣减PHRS余额
8. **创建购买记录**: 记录购买信息，状态为FINALIZED
9. **商品发放**: 根据商品类型执行相应的发放逻辑
10. **返回结果**: 返回购买成功信息和详细结果

### 2. 购买限制检查

#### 每日限制
- 检查用户当天对该商品的购买次数
- 基于商品的 `dailyLimit` 字段进行限制
- 时间范围：当天00:00:00到23:59:59

#### 账号限制
- 检查用户历史对该商品的总购买次数
- 基于商品的 `accountLimit` 字段进行限制
- 适用于特殊套餐等限购商品

#### VIP会员特殊限制
- 如果用户已有激活的VIP会员，不能重复购买VIP商品
- 检查VIP会员的有效期和激活状态

### 3. 商品发放逻辑

#### 速度加成道具 (speed_boost)
- 添加到用户的道具背包
- 如果已有相同类型道具，增加数量
- 包含倍数和持续时间信息

#### 时间跳跃道具 (time_warp)
- 立即执行时间跳跃
- 计算并发放对应时间的收益（GEM和牛奶）
- 更新农场区块和出货线的时间状态

#### VIP会员 (vip_membership)
- 创建VIP会员记录
- 设置开始时间和结束时间
- 激活VIP状态

#### 特殊套餐 (special_offer)
- 根据套餐配置发放多种道具
- 可能包含时间跳跃和速度加成的组合

## 响应格式

### 成功响应

```json
{
  "ok": true,
  "message": "购买成功",
  "data": {
    "purchaseId": 123,
    "productName": "Time Warp 24hr",
    "productType": "time_warp",
    "phrsPaid": "1000.000",
    "remainingBalance": "4000.000",
    "product": {
      "id": 8,
      "name": "Time Warp 24hr",
      "type": "time_warp",
      "duration": 24,
      "multiplier": null
    },
    "timeWarpResult": {
      "gemsEarned": 1500.250,
      "milkProduced": 2400.500,
      "milkProcessed": 2000.000,
      "farmProductionPerSecond": 0.667,
      "deliveryProcessingPerSecond": 0.556,
      "hasVip": false,
      "hasSpeedBoost": false,
      "speedBoostMultiplier": 1
    },
    "summary": "获得了 1500.250 GEM，生产了 2400.500 牛奶，处理了 2000.000 牛奶"
  }
}
```

### 响应字段说明

#### 主要响应结构

| 字段名 | 类型 | 描述 |
|--------|------|------|
| ok | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data | object | 购买结果数据 |

#### data 对象字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| purchaseId | number | 购买记录ID |
| productName | string | 商品名称 |
| productType | string | 商品类型 |
| phrsPaid | string | 支付的PHRS金额 |
| remainingBalance | string | 剩余PHRS余额 |
| product | object | 商品详细信息 |
| timeWarpResult | object | 时间跳跃结果（仅时间跳跃商品） |
| message | string | 商品特定的成功消息 |
| summary | string | 收益汇总信息 |

#### timeWarpResult 对象字段（时间跳跃商品）

| 字段名 | 类型 | 描述 |
|--------|------|------|
| gemsEarned | number | 获得的GEM数量 |
| milkProduced | number | 生产的牛奶数量 |
| milkProcessed | number | 处理的牛奶数量 |
| farmProductionPerSecond | number | 农场每秒生产速度 |
| deliveryProcessingPerSecond | number | 出货线每秒处理速度 |
| hasVip | boolean | 是否有VIP加成 |
| hasSpeedBoost | boolean | 是否有速度加成 |
| speedBoostMultiplier | number | 速度加成倍数 |

## 错误响应

### 400 参数验证失败

```json
{
  "ok": false,
  "message": "参数验证失败",
  "details": "productId 必须是大于等于1的整数"
}
```

### 404 商品不存在

```json
{
  "ok": false,
  "message": "商品不存在或已下架",
  "error": "PRODUCT_NOT_FOUND"
}
```

### 400 不支持PHRS支付

```json
{
  "ok": false,
  "message": "该商品不支持PHRS支付",
  "error": "PHRS_PAYMENT_NOT_SUPPORTED"
}
```

### 400 余额不足

```json
{
  "ok": false,
  "message": "PHRS余额不足",
  "error": "INSUFFICIENT_PHRS_BALANCE",
  "data": {
    "required": "1000.000",
    "available": "500.000",
    "shortfall": "500.000"
  }
}
```

### 400 购买限制

```json
{
  "ok": false,
  "message": "已达到每日购买限制，请明天再试",
  "error": "PURCHASE_LIMIT_EXCEEDED",
  "details": {
    "limitType": "daily",
    "productName": "Time Warp 24hr",
    "productType": "time_warp"
  }
}
```

### 401 未授权

```json
{
  "ok": false,
  "message": "用户未登录"
}
```

### 500 服务器错误

```json
{
  "ok": false,
  "message": "服务器内部错误，请稍后重试",
  "error": "INTERNAL_SERVER_ERROR"
}
```

## 商品类型说明

### 1. 时间跳跃 (time_warp)
- **功能**: 立即跳跃指定小时数，获得对应时间的收益
- **限制**: 每日限购1次
- **收益**: GEM、牛奶生产和处理
- **示例**: Time Warp 24hr - 跳跃24小时

### 2. 速度加成 (speed_boost)
- **功能**: 增加生产速度的道具，添加到背包
- **限制**: 每日限购1次
- **效果**: 指定倍数的速度加成，持续指定小时数
- **示例**: Speed Boost x4 24hr - 4倍速度持续24小时

### 3. VIP会员 (vip_membership)
- **功能**: 激活VIP会员身份
- **限制**: 如果已有激活VIP，不能重复购买
- **效果**: 农场生产速度+30%，持续指定天数
- **示例**: VIP会员30天

### 4. 特殊套餐 (special_offer)
- **功能**: 包含多种道具的组合套餐
- **限制**: 通常限购一账号一次
- **内容**: 可能包含时间跳跃和速度加成道具
- **示例**: 新手礼包 - 包含Time Warp和Speed Boost

## 请求示例

### 基本请求

```bash
curl -X POST "https://api.example.com/api/phrs-payment/purchase" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 8
  }'
```

### JavaScript/TypeScript 示例

```javascript
async function purchaseWithPhrs(productId) {
  try {
    const response = await fetch('/api/phrs-payment/purchase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({
        productId: productId
      })
    });

    const result = await response.json();
    
    if (result.ok) {
      console.log('购买成功:', result.data);
      
      // 处理不同类型的商品结果
      if (result.data.productType === 'time_warp') {
        console.log(`时间跳跃完成，获得 ${result.data.timeWarpResult.gemsEarned} GEM`);
      } else if (result.data.productType === 'speed_boost') {
        console.log('速度加成道具已添加到背包');
      } else if (result.data.productType === 'vip_membership') {
        console.log('VIP会员已激活');
      }
      
      console.log(`剩余PHRS余额: ${result.data.remainingBalance}`);
    } else {
      console.error('购买失败:', result.message);
      
      // 处理特定错误
      if (result.error === 'INSUFFICIENT_PHRS_BALANCE') {
        console.log(`余额不足，需要 ${result.data.required} PHRS，当前只有 ${result.data.available} PHRS`);
      } else if (result.error === 'PURCHASE_LIMIT_EXCEEDED') {
        console.log(`购买限制: ${result.details.limitType} 限制`);
      }
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 使用示例
purchaseWithPhrs(8); // 购买商品ID为8的道具
```

## 安全特性

### 1. 原子操作
- 使用数据库事务确保余额扣减和商品发放的原子性
- 防止并发购买导致的余额问题
- 失败时自动回滚所有操作

### 2. 余额检查
- 双重余额检查：购买前检查和扣款时再次验证
- 使用SQL的WHERE条件确保余额足够才扣款
- 防止负余额情况

### 3. 购买限制
- 严格的每日和账号限制检查
- VIP状态验证防止重复购买
- 基于数据库记录的准确限制统计

### 4. 错误处理
- 完整的错误分类和处理
- 详细的错误信息和建议
- 开发环境下提供调试信息

## 相关接口

- [获取PHRS余额和购买历史](./phrs-payment-balance-api.md)
- [获取支持PHRS支付的商品列表](./phrs-payment-products-api.md)
- [PHRS充值合约对接](./phrs-deposit-contract-frontend-integration.md)

## 注意事项

1. **事务安全**: 所有购买操作都在数据库事务中执行，确保数据一致性
2. **购买限制**: 严格遵守每日和账号限制，防止滥用
3. **商品发放**: 不同类型商品有不同的发放逻辑，需要正确处理
4. **余额精度**: PHRS余额使用高精度计算，支持小数点后多位
5. **错误处理**: 客户端应正确处理各种错误情况并给用户友好提示
6. **国际化**: 错误消息支持多语言，根据请求语言返回对应文本

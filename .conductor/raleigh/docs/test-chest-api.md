# 宝箱测试 API 文档

本文档描述了用于测试宝箱功能的API接口。这些接口仅用于测试环境，可以帮助开发人员和测试人员更方便地测试宝箱相关功能。

## 基本信息

- 基础路径: `/api/test-chest`
- 所有接口都需要用户身份验证

## API 接口列表

### 1. 重置宝箱状态

将用户的所有已开启宝箱重置为未开启状态，便于重复测试宝箱开启功能。

**请求方法**: POST

**路径**: `/api/test-chest/reset-chest-status`

**请求参数**: 无需额外参数

**响应示例**:

```json
{
  "ok": true,
  "message": "宝箱状态已重置",
  "data": {
    "resetCount": 5  // 重置的宝箱数量
  }
}
```

### 2. 创建测试宝箱

为当前用户创建指定数量和类型的测试宝箱。

**请求方法**: POST

**路径**: `/api/test-chest/create-test-chests`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| count | Number | 否 | 要创建的宝箱数量，默认为1，最大为10 |
| type | String | 否 | 宝箱类型，默认为'test' |

**请求示例**:

```json
{
  "count": 3,
  "type": "test"
}
```

**响应示例**:

```json
{
  "ok": true,
  "message": "已创建3个测试宝箱",
  "data": {
    "chests": [
      { "id": 101, "type": "test" },
      { "id": 102, "type": "test" },
      { "id": 103, "type": "test" }
    ]
  }
}
```

### 3. 模拟开启宝箱

模拟开启指定ID的宝箱，并生成奖励。可以指定宝箱奖励等级，也可以随机生成。

**请求方法**: POST

**路径**: `/api/test-chest/simulate-open-chest`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| chestId | Number | 是 | 要开启的宝箱ID |
| level | Number | 否 | 指定宝箱奖励等级(1-4)，不指定则随机生成 |

**请求示例**:

```json
{
  "chestId": 101,
  "level": 3
}
```

**响应示例**:

```json
{
  "ok": true,
  "message": "宝箱已成功开启",
  "data": {
    "chest": {
      "id": 101,
      "type": "test",
      "openedAt": "2023-06-15T08:30:45.123Z"
    },
    "reward": {
      "level": 3,
      "items": [
        { "type": "fragment_blue", "amount": 8 },
        { "type": "fragment_purple", "amount": 3 },
        { "type": "gem", "amount": 35000 }
      ]
    }
  }
}
```

## 宝箱奖励等级说明

系统支持4个等级的宝箱奖励，每个等级的奖励内容如下：

### LV1宝箱
- 绿色碎片16-24个 (100%概率)
- 蓝色碎片1-4个 (30%概率)
- 宝石1,000-5,000 (100%概率)

### LV2宝箱
- 绿色碎片8-15个 (100%概率)
- 蓝色碎片4-8个 (100%概率)
- 紫色碎片0-1个 (20%概率)
- 宝石5,000-15,000 (100%概率)

### LV3宝箱
- 蓝色碎片6-12个 (100%概率)
- 紫色碎片2-4个 (100%概率)
- 金色碎片0-1个 (28%概率)
- 宝石20,000-50,000 (100%概率)

### LV4宝箱
- 紫色碎片6-10个 (100%概率)
- 金色碎片2-4个 (100%概率)
- 宝石50,000-100,000 (100%概率)

## 使用示例

以下是一个完整的测试流程示例：

1. 首先重置所有宝箱状态
2. 创建3个测试宝箱
3. 模拟开启其中一个宝箱，指定为3级宝箱

这样可以方便地测试宝箱开启和奖励发放功能，无需等待正常的宝箱生成周期。
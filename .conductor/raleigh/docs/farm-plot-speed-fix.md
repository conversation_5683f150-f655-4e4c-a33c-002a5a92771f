# 牧场区速度计算修复

## 问题描述

之前的实现错误地将所有牧场区的生产速度固定为1秒，没有根据 `farm_configs` 表中的 `speed` 字段来计算实际的生产速度。

## 修复内容

### 1. 理解 speed 字段

`farm_configs` 表中的 `speed` 字段是"生产速度百分比"：
- `speed = 100` 表示 100%，对应 1.0 秒
- `speed = 110` 表示 110%，对应 0.909 秒（更快）
- `speed = 200` 表示 200%，对应 0.5 秒（更快）

### 2. 速度计算公式

```javascript
// 基础生产速度（秒）= 100 / speed百分比
const baseProductionSpeed = 100.0 / config.speed;

// 实际生产速度（考虑VIP加成）= 基础速度 / VIP倍率
const actualProductionSpeed = baseProductionSpeed / productionSpeedMultiplier;
```

### 3. 示例计算

| 等级 | speed配置 | 基础速度 | VIP加成 | 实际速度 |
|------|-----------|----------|---------|----------|
| 1    | 100%      | 1.000秒  | 无      | 1.000秒  |
| 3    | 110%      | 0.909秒  | 1.3x    | 0.699秒  |
| 5    | 120%      | 0.833秒  | 1.3x    | 0.641秒  |
| 10   | 150%      | 0.667秒  | 2.6x    | 0.256秒  |

## 修改的文件

### 1. 核心服务文件
**文件**: `src/services/farmPlotService.ts`

#### 修改的方法：
- `getUserFarmPlots()` - 获取用户牧场区列表
- `upgradeFarmPlot()` - 升级牧场区
- `unlockFarmPlot()` - 解锁牧场区
- `calculateNextUpgradeGrowthWithBoosts()` - 计算升级增长

#### 修改内容：
```javascript
// 旧代码（错误）
productionSpeed: formatToThreeDecimalsNumber(1.0 / productionSpeedMultiplier)

// 新代码（正确）
let baseProductionSpeed = 1.0; // 默认1秒
try {
  const { FarmConfigService } = require('./farmConfigService');
  const config = await FarmConfigService.getConfigByGrade(plot.level);
  if (config && config.speed > 0) {
    // speed是百分比，100表示100%（1秒），200表示200%（0.5秒）
    baseProductionSpeed = 100.0 / config.speed;
  }
} catch (error) {
  console.warn('获取农场配置失败，使用默认速度1秒:', error);
}

productionSpeed: formatToThreeDecimalsNumber(baseProductionSpeed / productionSpeedMultiplier)
```

### 2. 测试文件
**文件**: `scripts/test-farm-plot-speed.js`

更新了测试逻辑以验证基于配置的速度计算。

## API 响应变化

### `/api/farm/farm-plots` 接口

**变化前**：
```json
{
  "level": 3,
  "productionSpeed": 0.769,  // 固定为 1.0 / 1.3
  "hasBoost": true,
  "boostMultiplier": 1.3
}
```

**变化后**：
```json
{
  "level": 3,
  "productionSpeed": 0.699,  // 根据配置计算：100/110/1.3
  "hasBoost": true,
  "boostMultiplier": 1.3
}
```

## 向后兼容性

### 降级方案
如果无法从数据库获取配置，系统会：
1. 使用默认的1秒基础速度
2. 记录警告日志
3. 继续正常运行

### 错误处理
- 配置获取失败时有完整的错误处理
- 不会影响其他功能的正常运行
- 提供有意义的日志信息

## 验证结果

运行 `node scripts/test-farm-plot-speed.js` 的测试结果：

```
🎉 模拟数据测试通过！逻辑正确。

✅ 验证项目:
   - 1级牧场区：100% = 1.000秒
   - 3级牧场区：110% = 0.909秒（VIP后0.699秒）
   - 10级牧场区：150% = 0.667秒（VIP+道具后0.256秒）
   - 速度计算公式：100 / speed% / VIP倍率
```

## 相关配置数据

### farm_configs 表示例数据
```sql
grade | speed | 说明
------|-------|------
1     | 100   | 100% = 1.000秒
2     | 100   | 100% = 1.000秒
3     | 110   | 110% = 0.909秒
4     | 110   | 110% = 0.909秒
5     | 120   | 120% = 0.833秒
...   | ...   | ...
49    | 300   | 300% = 0.333秒
50    | 300   | 300% = 0.333秒
```

## 影响范围

### 直接影响
- 牧场区生产速度显示
- 升级预览中的速度信息
- VIP和道具加成的速度计算

### 间接影响
- 牛奶生产效率计算
- 时间跳跃奖励计算
- 离线收益计算

## 部署注意事项

1. **数据一致性**: 确保 `farm_configs` 表有完整的配置数据
2. **缓存更新**: 可能需要清理相关的Redis缓存
3. **前端适配**: 前端显示的速度值会发生变化
4. **测试验证**: 在生产环境部署前进行完整测试

## 相关文件

- `src/services/farmPlotService.ts` - 主要修改文件
- `src/models/FarmConfig.ts` - 配置模型
- `src/services/farmConfigService.ts` - 配置服务
- `scripts/test-farm-plot-speed.js` - 测试脚本
- `docs/farm-plot-speed-fix.md` - 本文档

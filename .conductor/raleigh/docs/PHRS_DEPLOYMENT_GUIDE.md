# PHRS代币支付系统部署指南

## 概述

本指南详细说明如何部署PHRS代币支付系统，包括智能合约部署、后端服务配置和监控设置。

## 前置要求

### 系统要求
- Node.js 22.x
- PostgreSQL 13+
- Redis (可选，用于缓存)
- Git

### 网络要求
- Pharos主网/测试网RPC访问
- 稳定的互联网连接
- 防火墙配置允许必要端口

## 部署步骤

### 1. 智能合约部署

#### 1.1 准备环境

```bash
cd contracts
npm install
```

#### 1.2 配置环境变量

创建 `contracts/.env` 文件：

```bash
# 部署私钥（确保有足够的原生代币用于gas费）
PRIVATE_KEY=your_private_key_here

# PHRS代币合约地址
PHRS_TOKEN_ADDRESS=0x...

# 充值限制配置
MIN_DEPOSIT_AMOUNT=1      # 最小充值金额（PHRS）
MAX_DEPOSIT_AMOUNT=10000  # 最大充值金额（PHRS）

# Pharos网络配置
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network
PHAROS_MAINNET_RPC_URL=https://rpc.pharos.network

# 区块链浏览器API（用于合约验证）
PHAROS_EXPLORER_API_KEY=your_api_key_here
```

#### 1.3 部署到测试网

```bash
npm run deploy:testnet
```

#### 1.4 部署到主网

```bash
npm run deploy:mainnet
```

#### 1.5 验证合约

```bash
npx hardhat verify --network pharos_mainnet <CONTRACT_ADDRESS> "<PHRS_TOKEN_ADDRESS>" "<MIN_DEPOSIT_AMOUNT>" "<MAX_DEPOSIT_AMOUNT>"
```

### 2. 后端服务部署

#### 2.1 克隆代码库

```bash
git clone <repository_url>
cd wolf_fun
```

#### 2.2 安装依赖

```bash
npm install
```

#### 2.3 配置环境变量

创建 `.env` 文件：

```bash
# 基本配置
NODE_ENV=production
PORT=3456
BASE_URL=https://your-domain.com

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/wolf_fun

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret-here

# Pharos网络配置
PHAROS_RPC_URL=https://rpc.pharos.network
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network

# PHRS系统配置
PHRS_TOKEN_ADDRESS=0x...
PHRS_DEPOSIT_CONTRACT_ADDRESS=0x...

# 可选：Redis配置（用于缓存）
REDIS_URL=redis://localhost:6379

# 日志配置
LOG_LEVEL=info
```

#### 2.4 数据库迁移

```bash
npm run migrate
```

#### 2.5 构建应用

```bash
npm run build
```

#### 2.6 启动服务

```bash
npm start
```

### 3. 进程管理（推荐使用PM2）

#### 3.1 安装PM2

```bash
npm install -g pm2
```

#### 3.2 创建PM2配置文件

创建 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: 'wolf-fun-api',
    script: 'dist/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3456
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

#### 3.3 启动PM2

```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 4. Nginx反向代理配置

创建 `/etc/nginx/sites-available/wolf-fun`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3456;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }
}
```

启用站点：

```bash
sudo ln -s /etc/nginx/sites-available/wolf-fun /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL证书配置（Let's Encrypt）

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 6. 监控和日志

#### 6.1 系统监控

使用PM2监控：

```bash
pm2 monit
```

#### 6.2 日志管理

配置日志轮转 `/etc/logrotate.d/wolf-fun`：

```
/path/to/wolf_fun/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reloadLogs
    endscript
}
```

#### 6.3 健康检查

创建健康检查脚本 `scripts/health-check.sh`：

```bash
#!/bin/bash

# 检查API健康状态
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3456/api/health)

if [ "$response" != "200" ]; then
    echo "API health check failed: $response"
    # 发送告警通知
    # pm2 restart wolf-fun-api
    exit 1
fi

# 检查PHRS服务健康状态
phrs_response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3456/api/phrs-deposit/health)

if [ "$phrs_response" != "200" ]; then
    echo "PHRS service health check failed: $phrs_response"
    exit 1
fi

echo "All services healthy"
```

设置定时检查：

```bash
# 添加到crontab
*/5 * * * * /path/to/scripts/health-check.sh
```

## 安全配置

### 1. 防火墙设置

```bash
# 只允许必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. 数据库安全

```sql
-- 创建专用数据库用户
CREATE USER wolf_fun_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE wolf_fun TO wolf_fun_user;
GRANT USAGE ON SCHEMA public TO wolf_fun_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO wolf_fun_user;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO wolf_fun_user;
```

### 3. 环境变量安全

- 使用强密码和随机密钥
- 定期轮换JWT密钥
- 限制私钥访问权限

```bash
chmod 600 .env
chown app:app .env
```

## 备份策略

### 1. 数据库备份

创建备份脚本 `scripts/backup-db.sh`：

```bash
#!/bin/bash

BACKUP_DIR="/backups/wolf_fun"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="wolf_fun"

mkdir -p $BACKUP_DIR

pg_dump $DB_NAME | gzip > $BACKUP_DIR/wolf_fun_$DATE.sql.gz

# 保留最近30天的备份
find $BACKUP_DIR -name "wolf_fun_*.sql.gz" -mtime +30 -delete
```

设置定时备份：

```bash
# 每天凌晨2点备份
0 2 * * * /path/to/scripts/backup-db.sh
```

### 2. 代码备份

```bash
# 定期推送到远程仓库
git push origin main
```

## 故障排除

### 1. 常见问题

#### PHRS充值监听服务无法启动
- 检查RPC URL是否可访问
- 验证合约地址是否正确
- 确认网络连接稳定

#### 数据库连接失败
- 检查数据库服务状态
- 验证连接字符串
- 确认用户权限

#### 内存不足
- 增加服务器内存
- 优化PM2配置
- 检查内存泄漏

### 2. 日志分析

```bash
# 查看应用日志
pm2 logs wolf-fun-api

# 查看错误日志
tail -f logs/err.log

# 查看系统日志
sudo journalctl -u nginx -f
```

### 3. 性能优化

- 启用数据库连接池
- 配置Redis缓存
- 优化数据库查询
- 使用CDN加速静态资源

## 更新部署

### 1. 零停机更新

```bash
# 拉取最新代码
git pull origin main

# 安装新依赖
npm install

# 运行数据库迁移
npm run migrate

# 构建应用
npm run build

# 重启服务
pm2 reload wolf-fun-api
```

### 2. 回滚策略

```bash
# 回滚到上一个版本
git checkout <previous_commit>
npm install
npm run build
pm2 reload wolf-fun-api

# 如需要，回滚数据库
npm run migrate:undo
```

## 监控告警

### 1. 设置告警

- API响应时间监控
- 错误率监控
- 系统资源监控
- PHRS充值事件监控

### 2. 告警通知

配置邮件/短信/Slack通知，及时响应系统异常。

## 总结

按照本指南完成部署后，PHRS代币支付系统将稳定运行。定期检查系统状态，及时更新和维护，确保服务的高可用性和安全性。

# Web3钱包更新用户名接口
## 接口信息

- **路径**: `/api/web3-auth/update-username`
- **方法**: POST
- **认证要求**: 需要钱包认证（walletAuthMiddleware）
- **Content-Type**: application/json

## 功能说明

该接口允许已登录的用户更新其用户名。系统会验证新用户名是否已被其他用户使用，并确保用户名符合长度要求。

## 请求参数
### 请求头

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| Authorization | string | 是 | 认证令牌，格式为：`Bearer {token}` |

### 请求体 (JSON)

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| newUsername | string | 是 | 新的用户名，长度必须在3-30个字符之间 |

### 请求体示例

```json
{
  "newUsername": "my_new_username"
}
```

## 响应结果
### 成功响应

- **状态码**: 200 OK
- **响应体**: 

```json
{
  "ok": true,
  "data": {
    "id": 12,
    "username": "my_new_username"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | number | 用户ID |
| username | string | 更新后的用户名 |

### 错误响应
#### 参数验证失败

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": ["参数验证错误详情"]
}
```

#### 用户名已存在

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": "用户名已存在"
}
```

#### 用户不存在

- **状态码**: 400 Bad Request
- **响应体**:

```json
{
  "ok": false,
  "data": "用户不存在"
}
```

#### 服务器错误

- **状态码**: 500 Internal Server Error
- **响应体**:

```json
{
  "ok": false,
  "data": "服务器错误：具体错误信息"
}
```

## 注意事项

1. 用户必须先登录并获取有效的认证令牌
2. 用户名必须是唯一的，如果新用户名已被其他用户使用，将返回错误
3. 用户名长度必须在3-30个字符之间
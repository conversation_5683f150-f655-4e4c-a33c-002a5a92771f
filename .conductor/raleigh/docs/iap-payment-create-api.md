# IAP 支付订单创建 API

## 接口信息

- **路径**: `/api/iap/payment/create`
- **方法**: POST
- **描述**: 创建IAP商品支付订单，支持单个商品购买
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数

### 请求头

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| Authorization | string | 是 | Bearer 认证token |
| Content-Type | string | 是 | application/json |

### 请求体

| 参数名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| productId | number | 是 | 商品ID |
| imageUrl | string | 否 | 商品图片URL |
| paymentMethod | string | 是 | 支付方式，可选值："kaia"、"usd" |
| testMode | boolean | 否 | 测试模式，默认根据环境自动判断 |

### 请求体示例

```json
{
  "productId": 1,
  "imageUrl": "https://example.com/product1.jpg",
  "paymentMethod": "kaia",
  "testMode": false
}
```

## 响应参数

### 成功响应

**状态码**: 200

```json
{
  "ok": true,
  "paymentId": "payment_123456789",
  "amount": 100.5,
  "purchase": {
    "id": 1,
    "walletId": 123,
    "productId": 1,
    "paymentId": "payment_123456789",
    "paymentMethod": "kaia",
    "amount": 100.5,
    "currency": "KAIA",
    "status": "pending",
    "purchaseDate": "2024-01-15T10:30:00.000Z"
  },
  "product": {
    "id": 1,
    "name": "速度提升道具",
    "type": "speed_boost"
  }
}
```

#### 响应字段说明

| 参数名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 请求是否成功 |
| paymentId | string | DappPortal支付订单ID |
| amount | number | 商品金额 |
| purchase | object | 购买记录 |
| product | object | 商品信息 |

### 错误响应

#### 参数错误 (400)

```json
{
  "error": "Missing required parameters"
}
```

#### 商品不存在或未激活 (404)

```json
{
  "error": "Product not found or inactive"
}
```

#### 钱包不存在 (404)

```json
{
  "error": "Wallet not found"
}
```

#### 达到每日购买限制 (400)

```json
{
  "error": "Daily purchase limit reached for product: 速度提升道具",
  "productId": 1,
  "limit": 5,
  "purchased": 5
}
```

#### 达到账号购买限制 (400)

```json
{
  "error": "Account purchase limit reached for product: VIP会员",
  "productId": 3,
  "limit": 1,
  "purchased": 1
}
```

#### VIP会员已激活 (400)

```json
{
  "error": "VIP membership already active",
  "endTime": "2024-02-15T10:30:00.000Z"
}
```

#### 支付方式不支持 (400)

```json
{
  "error": "Price not available for selected payment method for product: 特殊套餐",
  "productId": 4
}
```

#### DappPortal配置错误 (500)

```json
{
  "error": "DappPortal configuration missing"
}
```

#### 用户钱包地址未找到 (400)

```json
{
  "error": "User wallet address not found"
}
```

#### 支付服务创建失败 (500)

```json
{
  "error": "Failed to create payment order",
  "details": "DappPortal service unavailable"
}
```

#### 支付服务响应无效 (500)

```json
{
  "error": "Invalid response from payment service"
}
```

#### 服务器内部错误 (500)

```json
{
  "error": "Internal server error"
}
```

## 业务逻辑说明

### 购买限制检查

1. **每日限制**: 检查用户当日已购买该商品的次数是否超过限制
2. **账号限制**: 检查用户账号总共购买该商品的次数是否超过限制
3. **VIP会员检查**: 如果购买VIP会员商品，检查是否已有激活的VIP会员

### 支付方式

- **kaia**: 使用KAIA代币支付，通过加密货币网关处理
- **usd**: 使用美元支付，通过Stripe支付网关处理

### 价格计算

- **KAIA支付**: 使用商品的 `priceKaia` 字段
- **USD支付**: 使用商品的 `priceUsd` 字段，并转换为最小单位（分）

### DappPortal集成

接口会调用DappPortal支付服务创建支付订单，包含以下信息：
- 买家钱包地址
- 支付网关类型（CRYPTO或STRIPE）
- 货币代码和价格
- 商品详情列表
- 回调URL配置

## 使用示例

### 购买商品示例

#### KAIA支付

```bash
curl -X POST http://your-domain/api/iap/payment/create \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 1,
    "imageUrl": "https://example.com/speed-boost.jpg",
    "paymentMethod": "kaia"
  }'
```

#### USD支付

```bash
curl -X POST http://your-domain/api/iap/payment/create \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "productId": 2,
    "imageUrl": "https://example.com/time-warp.jpg",
    "paymentMethod": "usd",
    "testMode": true
  }'
```

## 注意事项

1. **认证要求**: 所有请求必须包含有效的Bearer token
2. **商品状态**: 只能购买状态为激活（isActive: true）的商品
3. **购买限制**: 系统会自动检查每日和账号购买限制
4. **VIP会员**: 同一时间只能有一个激活的VIP会员
5. **单商品限制**: 每次只能购买一个商品，不支持批量购买
6. **图片URL**: imageUrl为可选参数，如果不提供将使用空字符串
6. **支付状态**: 创建的购买记录初始状态为"pending"，需要通过支付回调更新状态
7. **测试模式**: 在非生产环境下，DappPortal将运行在测试模式

## 相关接口

- [获取商店商品列表](/api/iap/store/products) - 获取可购买的商品列表
- [支付回调处理](/api/iap/payment/callback) - 处理支付状态变更
- [获取购买历史](/api/iap/purchase/history) - 查看用户购买记录
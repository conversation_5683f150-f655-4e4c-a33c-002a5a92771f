# IAP 使用道具 API
## 接口信息

- **路径**: `/api/iap/boosters/use`
- **方法**: POST
- **描述**: 使用用户拥有的道具，激活其效果
- **认证**: 需要钱包认证，请求头中必须包含 `Authorization: Bearer <token>`

## 请求参数
### 请求体

```json
{
  "boosterId": 1
}
```

| 字段名 | 类型 | 必填 | 描述 |
| ------ | ---- | ---- | ---- |
| boosterId | number | 是 | 要使用的道具ID |

## 响应参数
### 成功响应

**状态码**: 200

```json
{
  "ok": true,
  "activeBooster": {
    "id": 1,
    "walletId": 123,
    "productId": 1,
    "type": "speed_boost",
    "multiplier": 2,
    "startTime": "2024-01-15T10:30:00.000Z",
    "endTime": "2024-01-16T10:30:00.000Z",
    "status": "active",
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  },
  "message": "speed_boost activated successfully"
}
```

| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| ok | boolean | 操作是否成功 |
| activeBooster | object | 激活的道具信息 |
| activeBooster.id | number | 激活道具ID |
| activeBooster.walletId | number | 钱包ID |
| activeBooster.productId | number | 关联的商品ID |
| activeBooster.type | string | 道具类型，可能值: `speed_boost`(速度提升), `time_warp`(时间跳跃) |
| activeBooster.multiplier | number | 加成倍数 |
| activeBooster.startTime | string | 激活开始时间 |
| activeBooster.endTime | string | 激活结束时间 |
| activeBooster.status | string | 状态，值为 `active` |
| message | string | 成功消息 |

### 错误响应
#### 参数缺失 (400)

```json
{
  "error": "Wallet ID and Booster ID are required"
}
```

#### 道具不存在或数量不足 (404)

```json
{
  "error": "Booster not found or insufficient quantity"
}
```

#### 同类型道具已激活 (400)

```json
{
  "error": "Same type booster is already active"
}
```

#### 服务器错误 (500)

```json
{
  "error": "Internal server error"
}
```

## 业务逻辑说明

1. **道具使用流程**:
   - 验证用户拥有指定的道具且数量大于0
   - 检查是否已有同类型的激活道具
   - 减少道具数量
   - 创建激活道具记录

2. **激活时间计算**:
   - 激活开始时间为当前时间
   - 激活结束时间根据道具的持续时间（小时）计算

3. **事务处理**:
   - 整个操作在数据库事务中执行，确保数据一致性
   - 如果任何步骤失败，将回滚所有更改

4. **道具类型限制**:
   - 同一类型的道具不能同时激活多个
   - 必须等待当前激活的同类型道具过期后才能使用新的道具

## 相关接口

- [获取用户道具](/api/iap/boosters) - 查看用户拥有的道具
- [获取激活道具](/api/iap/boosters/active) - 查看当前激活的道具
- [获取商店商品列表](/api/iap/store/products) - 购买新的道具
# PHRS余额原子操作实现

## 概述

为了解决PHRS余额更新时的并发竞争条件问题，我们将余额更新操作改为使用数据库的原子操作。这确保了在高并发情况下余额的准确性和一致性。

## 问题背景

### 原有实现的问题

**非原子操作流程:**
```typescript
// 1. 读取当前余额
const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');

// 2. 计算新余额
const newBalance = currentBalance.plus(depositAmount);

// 3. 更新余额
await userWallet.update({
  phrsBalance: newBalance.toFixed(18)
});
```

**并发问题:**
- 两个请求同时读取相同的余额
- 都基于相同的初始值计算新余额
- 后执行的更新会覆盖先执行的更新
- 导致余额计算错误

## 解决方案

### 原子操作实现

使用数据库的原子 UPDATE 操作，直接在数据库层面进行余额计算：

#### 1. 充值增加余额（原子操作）

```typescript
// 使用数据库原子操作增加余额
const updateResult = await sequelize.query(
  `UPDATE user_wallets 
   SET phrsBalance = phrsBalance + :depositAmount,
       phrsWalletAddress = :walletAddress,
       lastPhrsUpdateTime = :updateTime,
       updatedAt = :updateTime
   WHERE id = :walletId`,
  {
    replacements: {
      depositAmount: depositAmount.toFixed(18),
      walletAddress: user.toLowerCase(),
      updateTime: new Date(),
      walletId: userWallet.id
    },
    type: QueryTypes.UPDATE,
    transaction: t
  }
);

// 检查是否成功更新
const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;
if (affectedRows === 0) {
  throw new Error(`钱包更新失败，可能已被删除`);
}
```

#### 2. 支付扣减余额（原子操作 + 余额检查）

```typescript
// 使用原子操作扣减PHRS余额，同时检查余额是否足够
const updateResult = await sequelize.query(
  `UPDATE user_wallets 
   SET phrsBalance = phrsBalance - :phrsAmount,
       lastPhrsUpdateTime = :updateTime,
       updatedAt = :updateTime
   WHERE id = :walletId AND phrsBalance >= :phrsAmount`,
  {
    replacements: {
      phrsAmount: phrsPrice.toFixed(18),
      updateTime: new Date(),
      walletId: walletId
    },
    type: QueryTypes.UPDATE,
    transaction
  }
);

// 检查是否成功扣减（余额足够）
const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;
if (affectedRows === 0) {
  // 余额不足或钱包不存在
  throw new Error('余额不足或钱包不存在');
}
```

## 实现细节

### 修改的文件

#### 1. PHRS充值服务 (`src/services/phrsDepositService.ts`)

**修改位置:** `handleDepositEvent` 方法中的余额更新逻辑

**修改内容:**
- 将读取-计算-更新的三步操作改为单一原子UPDATE
- 添加了影响行数检查，确保更新成功
- 保持事务一致性

#### 2. PHRS支付控制器 (`src/controllers/phrsPaymentController.ts`)

**修改位置:** `purchaseWithPhrs` 方法中的余额扣减逻辑

**修改内容:**
- 使用原子UPDATE操作扣减余额
- 在WHERE条件中添加余额检查 (`phrsBalance >= :phrsAmount`)
- 通过影响行数判断操作是否成功

### 技术要点

#### 1. 原子性保证

```sql
-- 原子增加操作
UPDATE user_wallets 
SET phrsBalance = phrsBalance + 1.234567890123456789
WHERE id = 123;

-- 原子扣减操作（带余额检查）
UPDATE user_wallets 
SET phrsBalance = phrsBalance - 0.123456789012345678
WHERE id = 123 AND phrsBalance >= 0.123456789012345678;
```

#### 2. 并发安全

- 数据库引擎保证UPDATE操作的原子性
- 多个并发请求会被数据库串行化处理
- 避免了应用层的竞争条件

#### 3. 余额检查

- 扣减操作在WHERE条件中检查余额
- 如果余额不足，UPDATE不会执行任何行
- 通过 `affectedRows` 判断操作是否成功

#### 4. 精度保持

- 继续使用18位精度 (`toFixed(18)`)
- 数据库字段为 `DECIMAL(65,18)`
- 保证精度不丢失

## 优势

### 1. 并发安全 ✅
- 消除了读取-计算-更新的竞争条件
- 多个并发请求不会相互干扰
- 数据库层面保证原子性

### 2. 性能提升 ✅
- 减少了数据库往返次数
- 避免了应用层的复杂锁机制
- 数据库优化的UPDATE操作更高效

### 3. 数据一致性 ✅
- 余额计算始终准确
- 不会出现余额丢失或重复计算
- 事务回滚时自动恢复

### 4. 余额保护 ✅
- 扣减操作自动检查余额是否足够
- 防止余额变为负数
- 操作失败时不影响数据

## 测试验证

### 测试脚本
创建了 `src/scripts/testAtomicOperations.ts` 来验证原子操作：

### 测试内容
1. **原子增加操作测试**
2. **原子减少操作测试**
3. **余额不足保护测试**
4. **并发操作安全测试**
5. **数据一致性验证**

### 测试结果
```
📊 原子操作测试结果总结:
   - 原子增加操作: ✅
   - 原子减少操作: ✅
   - 余额不足保护: ✅
   - 并发操作安全: ✅
   - 数据一致性: ✅
```

## 使用示例

### 充值场景
```typescript
// 原子增加余额
const depositAmount = new BigNumber(ethers.formatEther(amount));
await sequelize.query(
  `UPDATE user_wallets 
   SET phrsBalance = phrsBalance + :amount
   WHERE id = :walletId`,
  {
    replacements: { amount: depositAmount.toFixed(18), walletId: wallet.id },
    type: QueryTypes.UPDATE,
    transaction: t
  }
);
```

### 支付场景
```typescript
// 原子扣减余额（带余额检查）
const result = await sequelize.query(
  `UPDATE user_wallets 
   SET phrsBalance = phrsBalance - :amount
   WHERE id = :walletId AND phrsBalance >= :amount`,
  {
    replacements: { amount: price.toFixed(18), walletId: wallet.id },
    type: QueryTypes.UPDATE,
    transaction: t
  }
);

const affectedRows = Array.isArray(result) ? result[1] : 0;
if (affectedRows === 0) {
  throw new Error('余额不足');
}
```

## 注意事项

### 1. 返回值处理
MySQL UPDATE 查询返回 `[results, metadata]`，需要正确提取 `affectedRows`：

```typescript
const updateResult = await sequelize.query(/* ... */);
const affectedRows = Array.isArray(updateResult) ? updateResult[1] : 0;
```

### 2. 事务一致性
原子操作仍需要在事务中执行，确保与其他操作的一致性。

### 3. 错误处理
通过 `affectedRows` 判断操作是否成功，而不是依赖异常。

### 4. 精度保持
继续使用 `BigNumber` 和 `toFixed(18)` 确保精度。

## 总结

通过实现PHRS余额的原子操作，我们成功解决了并发更新时的竞争条件问题，提高了系统的可靠性和性能。这种方法在高并发场景下特别重要，确保了用户余额的准确性和系统的稳定性。

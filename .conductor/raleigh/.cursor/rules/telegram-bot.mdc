---
description: 
globs: 
alwaysApply: false
---
# Telegram Bot 集成指南

## Bot核心文件
- [src/bot.ts](mdc:src/bot.ts) - Grammy框架的Telegram Bot主文件
- [src/start-bot.ts](mdc:src/start-bot.ts) - Bot启动入口

## Telegram相关路由
- [src/routes/telegramShareRoutes.ts](mdc:src/routes/telegramShareRoutes.ts) - 分享功能
- [src/routes/telegramPaymentRoutes.ts](mdc:src/routes/telegramPaymentRoutes.ts) - Telegram Stars支付

## 用户认证
- [tgInitDataGenerator.js](mdc:tgInitDataGenerator.js) - Telegram初始化数据生成器
- [src/routes/web3AuthRoutes.ts](mdc:src/routes/web3AuthRoutes.ts) - Web3认证集成

## 分享和推荐系统
- [src/routes/referralRoutes.ts](mdc:src/routes/referralRoutes.ts) - 推荐系统API
- [src/models/ShareBoostLink.ts](mdc:src/models/ShareBoostLink.ts) - 分享加速链接

## Bot部署
- [Dockerfile.bot](mdc:Dockerfile.bot) - Bot专用Docker镜像
- [deploy-bot.sh](mdc:deploy-bot.sh) - Bot部署脚本

## 国际化支持
- [src/i18n/](mdc:src/i18n) - 多语言支持目录
- [src/middlewares/languageMiddleware.ts](mdc:src/middlewares/languageMiddleware.ts) - 语言中间件

## Grammy框架特性
Bot使用Grammy框架构建，支持:
- 消息处理和回调查询
- 内联键盘和菜单
- 文件上传和媒体处理
- 用户会话管理
- 中间件链式处理

## 环境变量配置
需要在`.env`文件中配置:
- `BOT_TOKEN` - Telegram Bot Token
- `TELEGRAM_WEBHOOK_URL` - Webhook URL
- `TELEGRAM_SECRET_TOKEN` - Webhook密钥

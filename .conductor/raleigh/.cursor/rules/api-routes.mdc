---
description: 
globs: 
alwaysApply: false
---
# API 路由结构

## 用户相关
- [src/routes/userRoutes.ts](mdc:src/routes/userRoutes.ts) - 用户管理API
- [src/routes/referralRoutes.ts](mdc:src/routes/referralRoutes.ts) - 推荐系统
- [src/routes/walletRoutes.ts](mdc:src/routes/walletRoutes.ts) - 钱包功能
- [src/routes/web3AuthRoutes.ts](mdc:src/routes/web3AuthRoutes.ts) - Web3认证

## 游戏核心功能
- [src/routes/game.ts](mdc:src/routes/game.ts) - 核心游戏逻辑
- [src/routes/gameLoopRoutes.ts](mdc:src/routes/gameLoopRoutes.ts) - 游戏循环机制
- [src/routes/farmPlotRoutes.ts](mdc:src/routes/farmPlotRoutes.ts) - 农场地块功能
- [src/routes/deliveryLineRoutes.ts](mdc:src/routes/deliveryLineRoutes.ts) - 配送线功能
- [src/routes/chestRoutes.ts](mdc:src/routes/chestRoutes.ts) - 宝箱系统
- [src/routes/jackpotChestRoutes.ts](mdc:src/routes/jackpotChestRoutes.ts) - 彩池宝箱

## 任务和奖励
- [src/routes/taskRoutes.ts](mdc:src/routes/taskRoutes.ts) - 任务系统
- [src/routes/dailyClaimRoutes.ts](mdc:src/routes/dailyClaimRoutes.ts) - 每日签到
- [src/routes/rewardsRouter.ts](mdc:src/routes/rewards.ts) - 奖励系统
- [src/routes/rebateRoutes.ts](mdc:src/routes/rebateRoutes.ts) - 返利系统

## 特殊功能
- [src/routes/bullKingRoutes.ts](mdc:src/routes/bullKingRoutes.ts) - 牛王功能
- [src/routes/kolRoutes.ts](mdc:src/routes/kolRoutes.ts) - KOL系统
- [src/routes/fragmentRoutes.ts](mdc:src/routes/fragmentRoutes.ts) - 碎片系统
- [src/routes/ticketRoutes.ts](mdc:src/routes/ticketRoutes.ts) - 门票系统

## 支付和区块链
- [src/routes/tonProof.ts](mdc:src/routes/tonProof.ts) - TON认证
- [src/routes/telegramPaymentRoutes.ts](mdc:src/routes/telegramPaymentRoutes.ts) - Telegram支付
- [src/routes/withdrawalRoutes.ts](mdc:src/routes/withdrawalRoutes.ts) - 提现功能

---
description: 
globs: 
alwaysApply: false
---
# Wolf Fun 项目概览

这是一个基于TypeScript的Telegram Bot游戏应用，集成了TON区块链功能。

## 项目架构
- **主应用**: [src/app.ts](mdc:src/app.ts) - Express.js服务器和主要配置
- **Telegram Bot**: [src/bot.ts](mdc:src/bot.ts) - Grammy框架实现的Telegram Bot
- **配置文件**: [package.json](mdc:package.json) - 项目依赖和脚本
- **数据库配置**: [config/config.js](mdc:config/config.js) - Sequelize数据库配置
- **TypeScript配置**: [tsconfig.json](mdc:tsconfig.json) - TypeScript编译设置

## 核心技术栈
- Node.js + TypeScript
- Express.js (Web服务器)
- Grammy (Telegram Bot框架)
- Sequelize (ORM)
- MySQL (数据库)
- Redis (缓存和队列)
- BullMQ (任务队列)
- TON区块链集成

## 部署配置
- [docker-compose.yml](mdc:docker-compose.yml) - Docker Compose配置
- [Dockerfile](mdc:Dockerfile) - 主应用镜像
- [Dockerfile.bot](mdc:Dockerfile.bot) - Bot镜像
- [deploy.sh](mdc:deploy.sh) - 部署脚本

// 测试宝石验证逻辑修复（带延迟）
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

function sleep(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function testGemValidationWithDelay() {
  console.log('🧪 测试修复后的宝石验证逻辑（带延迟）...');
  console.log('='.repeat(60));
  
  try {
    // 第一次调用：更新lastActiveTime
    console.log('📋 第一步：更新lastActiveTime...');
    await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 1,
      milkOperations: {
        produce: 1,
        consume: 1
      }
    }, config);
    
    console.log('⏰ 等待6秒钟以满足时间窗口要求...');
    await sleep(6);
    
    // 第二次调用：测试宝石验证逻辑
    console.log('📋 第二步：测试宝石验证逻辑...');
    const response = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 5,
      milkOperations: {
        produce: 2,
        consume: 0  // 不消耗牛奶
      }
    }, config);

    const { data } = response.data;
    const { changes } = data;
    
    console.log('✅ API响应成功');
    console.log('📊 验证结果:');
    console.log('   使用严格验证:', changes.usedStrictValidation);
    console.log('   验证通过:', changes.validationPassed);
    console.log('   回退到旧方法:', changes.fallbackToOldMethod);
    console.log('   时间窗口有效:', changes.timeWindowValid);
    
    if (changes.timeWindowReason) {
      console.log('   时间窗口原因:', changes.timeWindowReason);
    }
    
    if (changes.strictValidationDetails) {
      const details = changes.strictValidationDetails;
      console.log('📋 验证详情:');
      console.log('   牛奶产量验证:', details.milkProductionValid ? '✅' : '❌');
      console.log('   牛奶消耗验证:', details.milkConsumptionValid ? '✅' : '❌');
      console.log('   宝石转换验证:', details.gemConversionValid ? '✅' : '❌');
      
      console.log('📈 数值对比:');
      console.log('   牛奶产量: 请求', details.validationDetails.milkProduction.requested, '/ 允许', details.validationDetails.milkProduction.maxAllowed);
      console.log('   牛奶消耗: 请求', details.validationDetails.milkConsumption.requested, '/ 允许', details.validationDetails.milkConsumption.maxAllowed);
      console.log('   宝石增加: 请求', details.validationDetails.gemConversion.requested, '/ 允许', details.validationDetails.gemConversion.maxAllowed);
      console.log('   转换汇率:', details.validationDetails.gemConversion.conversionRate);
      console.log('   基础计算值:', details.validationDetails.gemConversion.calculatedFromMilk);
      
      if (details.reason) {
        console.log('❌ 失败原因:', details.reason);
      }
    }
    
    console.log('💎 资源变化:');
    console.log('   GEM: ', data.beforeUpdate.gem, '->', data.afterUpdate.gem, '(+' + (data.afterUpdate.gem - data.beforeUpdate.gem) + ')');
    console.log('   牛奶: ', data.beforeUpdate.pendingMilk, '->', data.afterUpdate.pendingMilk, '(' + (data.afterUpdate.pendingMilk - data.beforeUpdate.pendingMilk) + ')');
    
    // 分析修复效果
    if (changes.strictValidationDetails) {
      const gemValidation = changes.strictValidationDetails.validationDetails.gemConversion;
      console.log('\n🔍 宝石验证逻辑分析:');
      console.log('   请求宝石:', gemValidation.requested);
      console.log('   允许宝石:', gemValidation.maxAllowed);
      console.log('   基础计算:', gemValidation.calculatedFromMilk);
      console.log('   验证通过:', gemValidation.valid ? '✅' : '❌');
      
      if (gemValidation.maxAllowed > 0) {
        console.log('✅ 修复成功！宝石验证现在基于理论产出能力，不再是0');
      } else {
        console.log('❌ 仍有问题：宝石验证上限仍然是0');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testGemValidationWithDelay();

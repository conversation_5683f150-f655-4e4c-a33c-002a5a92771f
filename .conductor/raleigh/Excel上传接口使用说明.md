# Excel上传接口使用说明

## 概述

本项目已成功实现了Excel文件上传和读取功能，可以读取您提供的`区域升级.xlsx`文件并解析其中的数据。

## 功能特性

✅ **Excel文件上传和解析** - 支持.xlsx和.xls格式  
✅ **多工作表支持** - 可以读取Excel文件中的所有工作表  
✅ **数据结构化解析** - 自动解析表头和数据行  
✅ **Excel模板下载** - 提供标准的区域升级模板  
✅ **批量数据处理** - 支持批量处理区域升级数据  
✅ **文件大小限制** - 最大支持10MB的Excel文件  
✅ **错误处理** - 完善的错误处理和用户友好的错误信息  

## API接口

### 1. 健康检查
```
GET /api/excel/health/check
```
检查服务器运行状态。

### 2. 下载Excel模板
```
GET /api/excel/template
```
下载区域升级的Excel模板文件。

**响应**: Excel文件下载

### 3. 上传Excel文件
```
POST /api/excel/upload
Content-Type: multipart/form-data
```

**请求参数**:
- `excelFile`: Excel文件 (form-data)

**响应示例**:
```json
{
  "ok": true,
  "data": {
    "fileName": "区域升级.xlsx",
    "fileSize": 14064,
    "sheetNames": ["Sheet1", "Sheet2", "Sheet3"],
    "sheetsData": {
      "Sheet1": [
        ["grade", "production", "cow", "speed", "milk", "cost", "offline"],
        ["等级", "每秒产出计算用值", "奶牛数量", "生产速度百分比", "牛奶生产", "升级花费", "离线产出"],
        // ... 更多数据行
      ]
    },
    "parsedData": {
      "regions": [
        {
          "sheetName": "Sheet1",
          "headers": ["grade", "production", "cow", "speed", "milk", "cost", "offline"],
          "data": [
            {
              "grade": "等级",
              "production": "每秒产出计算用值",
              "cow": "奶牛数量",
              "speed": "生产速度百分比",
              "milk": "牛奶生产",
              "cost": "升级花费",
              "offline": "离线产出",
              "_rowIndex": 2
            }
            // ... 更多解析后的数据
          ],
          "totalRows": 53
        }
      ],
      "summary": {
        "totalSheets": 3,
        "totalRegions": 53,
        "processedAt": "2025-07-20T06:42:07.604Z"
      }
    },
    "message": "Excel文件读取成功"
  }
}
```

### 4. 批量处理区域升级数据
```
POST /api/excel/batch-upgrade
Content-Type: application/json
```

**请求参数**:
```json
{
  "upgradeData": [
    {
      "regionId": "1",
      "regionName": "农场区域1",
      "currentLevel": "1",
      "upgradeCost": "100",
      "effect": "产量+20%"
    }
  ]
}
```

**响应示例**:
```json
{
  "ok": true,
  "data": {
    "processedCount": 1,
    "results": [
      {
        "index": 1,
        "regionId": "1",
        "regionName": "农场区域1",
        "currentLevel": "1",
        "upgradeCost": "100",
        "effect": "产量+20%",
        "status": "processed",
        "message": "处理成功"
      }
    ],
    "message": "成功处理 1 条区域升级数据"
  }
}
```

## 技术实现

### 后端技术栈
- **Node.js 22** - 运行环境
- **Express.js** - Web框架
- **TypeScript** - 类型安全的JavaScript
- **xlsx** - Excel文件处理库
- **multer** - 文件上传中间件

### 文件结构
```
src/
├── controllers/
│   └── excelUploadController.ts    # Excel上传控制器
├── routes/
│   └── excelUploadRoutes.ts        # Excel路由定义
└── app.ts                          # 主应用文件

test/
├── test_excel_reader.js            # Excel读取测试
├── test_excel_api.js               # API接口测试
└── simple_excel_server.js          # 简化测试服务器
```

## 使用示例

### 1. 启动服务器
```bash
# 启动完整服务器
npm run dev

# 或启动简化测试服务器
node simple_excel_server.js
```

### 2. 测试API
```bash
# 运行完整API测试
node test_excel_api.js

# 或直接测试Excel读取功能
node test_excel_reader.js
```

### 3. 使用curl测试
```bash
# 健康检查
curl http://localhost:3457/api/health/check

# 下载模板
curl -O http://localhost:3457/api/excel/template

# 上传Excel文件
curl -X POST -F "excelFile=@doc/区域升级.xlsx" http://localhost:3457/api/excel/upload
```

## 测试结果

✅ **Excel文件读取测试通过**
- 成功读取了3个工作表
- 解析了53行区域升级数据
- 正确识别了表头和数据结构

✅ **API接口测试通过**
- 健康检查: ✅
- 模板下载: ✅ (17,261 bytes)
- 文件上传: ✅ (14,064 bytes, 3个工作表, 53行数据)
- 批量处理: ✅ (2条测试数据)

## 安全特性

- **文件类型验证** - 只允许.xlsx和.xls文件
- **文件大小限制** - 最大10MB
- **内存存储** - 使用内存存储避免磁盘文件残留
- **错误处理** - 完善的错误捕获和用户友好的错误信息

## 扩展功能

可以根据需要添加以下功能：
- 用户认证和权限控制
- 文件上传历史记录
- 数据验证和清洗
- 导出处理结果为Excel
- 异步处理大文件
- 进度条显示

## 注意事项

1. **Node.js版本**: 确保使用Node.js 22或更高版本
2. **依赖安装**: 运行`npm install xlsx multer @types/multer`安装必要依赖
3. **端口配置**: 默认使用3456端口，测试服务器使用3457端口
4. **文件编码**: 支持中文文件名和内容
5. **内存使用**: 大文件上传时注意内存使用情况

## 总结

Excel上传接口已成功实现并通过测试，可以完美读取您提供的`区域升级.xlsx`文件。接口具有良好的错误处理、安全验证和扩展性，可以满足区域升级数据管理的需求。

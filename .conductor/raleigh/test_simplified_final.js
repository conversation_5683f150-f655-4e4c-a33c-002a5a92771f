// 测试最终简化的验证逻辑
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJ3YWxsZXRBZGRyZXNzIjoiMFFEaW9QVFNUb2RPdnBKZTVjelk5NjNKcnk0UWlsSDN0TUJ6Wm4tMXZGYmhObUxPIiwibmV0d29yayI6Ii0zIiwiaWF0IjoxNzQ5MzQ5NDg3LCJleHAiOjE3NTQ1MzM0ODd9.eBkEf1ElWnJOGpYM-YZsuKY1SXjq2jPy_OXl41Ogozc';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 格式化数值显示
function formatNumber(num) {
  if (typeof num === 'number') {
    return num.toFixed(3);
  }
  return parseFloat(num || 0).toFixed(3);
}

// 测试简化的验证逻辑
async function testSimplifiedFinalValidation() {
  console.log('🎯 测试最终简化的验证逻辑');
  console.log('='.repeat(60));
  console.log('📐 验证公式:');
  console.log('1. 牛奶产量 ≤ 每秒产量 × 更新间隔 × 1.5');
  console.log('2. 牛奶消耗 ≤ 每秒消耗量 × 更新间隔 × 1.5');
  console.log('3. 宝石增加 ≤ 牛奶消耗 × 牛奶汇率 × 1.5');
  console.log('✅ 全部通过 → 使用前端值 | ❌ 任一失败 → 回退旧方法');
  console.log('='.repeat(60));
  
  const testCases = [
    {
      name: '小量合理请求',
      request: {
        gemRequest: 1.000,
        milkOperations: {
          produce: 1.000,
          consume: 1.000
        }
      },
      description: '请求小量资源，应该通过验证'
    },
    {
      name: '中等合理请求',
      request: {
        gemRequest: 2.000,
        milkOperations: {
          produce: 1.500,
          consume: 2.000
        }
      },
      description: '请求中等资源，测试验证边界'
    },
    {
      name: '只产奶不消耗',
      request: {
        milkOperations: {
          produce: 1.000
        }
      },
      description: '只请求牛奶产量，不消耗不要GEM'
    },
    {
      name: '只消耗不产奶',
      request: {
        gemRequest: 3.000,
        milkOperations: {
          consume: 3.000
        }
      },
      description: '只请求牛奶消耗和GEM，不产奶'
    },
    {
      name: '超出范围请求',
      request: {
        gemRequest: 50.000,
        milkOperations: {
          produce: 50.000,
          consume: 50.000
        }
      },
      description: '请求超大数量，应该触发验证失败并回退'
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📝 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   请求参数:`, JSON.stringify(testCase.request, null, 2));
    
    try {
      const response = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        testCase.request,
        config
      );
      
      if (response.data.ok) {
        const data = response.data.data;
        const changes = data.changes;
        
        console.log('✅ 请求成功');
        console.log(`   消息: ${response.data.message}`);
        
        // 显示验证结果
        console.log('📊 验证结果:');
        console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
        console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
        console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
        console.log(`   时间间隔: ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒`);
        
        // 显示资源变化
        console.log('💰 资源变化:');
        console.log(`   GEM: ${formatNumber(data.beforeUpdate.gem)} → ${formatNumber(data.afterUpdate.gem)} (${changes.details.gem.increased > 0 ? '+' : ''}${formatNumber(changes.details.gem.increased)})`);
        console.log(`   牛奶: ${formatNumber(data.beforeUpdate.pendingMilk)} → ${formatNumber(data.afterUpdate.pendingMilk)} (+${formatNumber(changes.details.milk.increased)} -${formatNumber(changes.details.milk.decreased)})`);
        
        // 显示简化验证的详细计算
        if (changes.strictValidationDetails && changes.strictValidationDetails.validationDetails) {
          console.log('🔍 简化验证详情:');
          const details = changes.strictValidationDetails.validationDetails;
          const timeElapsed = changes.productionRates.timeElapsedSeconds;
          
          // 验证1：牛奶产量
          console.log(`   验证1 - 牛奶产量: ${details.milkProduction.valid ? '✅' : '❌'}`);
          console.log(`     公式: ${formatNumber(details.milkProduction.requested)} ≤ 每秒产量 × ${formatNumber(timeElapsed)} × 1.5`);
          console.log(`     计算: ${formatNumber(details.milkProduction.requested)} ≤ ${formatNumber(details.milkProduction.calculated)} × 1.5 = ${formatNumber(details.milkProduction.maxAllowed)}`);
          
          // 验证2：牛奶消耗
          console.log(`   验证2 - 牛奶消耗: ${details.milkConsumption.valid ? '✅' : '❌'}`);
          console.log(`     公式: ${formatNumber(details.milkConsumption.requested)} ≤ 每秒消耗 × ${formatNumber(timeElapsed)} × 1.5`);
          console.log(`     计算: ${formatNumber(details.milkConsumption.requested)} ≤ ${formatNumber(details.milkConsumption.calculated)} × 1.5 = ${formatNumber(details.milkConsumption.maxAllowed)}`);
          
          // 验证3：宝石转换
          console.log(`   验证3 - 宝石增加: ${details.gemConversion.valid ? '✅' : '❌'}`);
          console.log(`     公式: ${formatNumber(details.gemConversion.requested)} ≤ ${formatNumber(details.milkConsumption.requested)} × ${formatNumber(details.gemConversion.conversionRate)} × 1.5`);
          console.log(`     计算: ${formatNumber(details.gemConversion.requested)} ≤ ${formatNumber(details.gemConversion.calculatedFromMilk)} × 1.5 = ${formatNumber(details.gemConversion.maxAllowed)}`);
          
          // 显示每秒速率
          console.log('⚡ 每秒速率:');
          const milkPerSecond = details.milkProduction.calculated / timeElapsed;
          const consumePerSecond = details.milkConsumption.calculated / timeElapsed;
          console.log(`     牛奶产量: ${formatNumber(milkPerSecond)} 牛奶/秒`);
          console.log(`     牛奶消耗: ${formatNumber(consumePerSecond)} 牛奶/秒`);
          console.log(`     转换汇率: ${formatNumber(details.gemConversion.conversionRate)} GEM/牛奶`);
          
          if (!changes.validationPassed && changes.strictValidationDetails.reason) {
            console.log(`   失败原因: ${changes.strictValidationDetails.reason}`);
          }
        }
        
        // 分析结果
        console.log('🎯 结果分析:');
        if (changes.validationPassed) {
          console.log('   🎉 简化验证通过！使用前端请求数值');
          console.log('   ✅ 所有三项验证都在1.5倍容错范围内');
        } else if (changes.fallbackToOldMethod) {
          console.log('   ⚠️  简化验证失败，已回退到旧方法计算');
          console.log('   📊 至少一项验证超出1.5倍容错范围');
        } else if (changes.timeWindowValid === false) {
          console.log('   ⏰ 时间窗口无效，已更新lastActiveTime');
        }
        
      } else {
        console.log('❌ 请求失败');
        console.log(`   错误: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log('❌ 请求异常');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试间隔
    if (i < testCases.length - 1) {
      console.log('   ⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
}

// 主测试函数
async function runTest() {
  console.log('🚀 测试最终简化的严格验证批量资源更新接口');
  console.log('='.repeat(80));
  console.log('🎯 验证逻辑：');
  console.log('1. 直接计算：每秒产量 × 更新间隔 × 1.5');
  console.log('2. 直接计算：每秒消耗量 × 更新间隔 × 1.5');
  console.log('3. 直接计算：牛奶消耗 × 牛奶汇率 × 1.5');
  console.log('4. 全部通过 → 使用前端值，任一失败 → 回退旧方法');
  console.log('5. 不考虑复杂的可用牛奶量、生产周期等');
  console.log('='.repeat(80));
  
  await testSimplifiedFinalValidation();
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 最终简化验证总结:');
  console.log('1. ✅ 牛奶产量验证：直接基于每秒产量 × 时间 × 1.5');
  console.log('2. ✅ 牛奶消耗验证：直接基于每秒消耗 × 时间 × 1.5');
  console.log('3. ✅ 宝石增加验证：直接基于前端消耗 × 汇率 × 1.5');
  console.log('4. ✅ 逻辑简单直接，易于理解和维护');
  console.log('5. ✅ 性能优秀，计算快速');
  
  console.log('\n💡 优势:');
  console.log('- 验证逻辑极其简单，只需要三个公式');
  console.log('- 不需要考虑复杂的游戏状态');
  console.log('- 计算速度快，性能优秀');
  console.log('- 容易调试和维护');
  console.log('- 符合您的直接验证需求');
}

// 运行测试
runTest().catch(console.error);

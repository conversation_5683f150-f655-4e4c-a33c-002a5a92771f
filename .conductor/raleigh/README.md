# Wolf Fun Game Backend

Wolf Fun is a Telegram-based farming and delivery game with integrated PHRS token payment system on the Pharos blockchain.

## 🆕 PHRS Token Payment System

This project now includes a comprehensive PHRS (Pharos) token payment system that replaces the previous DappPortal integration. Users can deposit PHRS tokens and use them to purchase in-game items.

### Key Features

- **PHRS Token Deposits**: Smart contract-based token deposits on Pharos network
- **Real-time Balance Tracking**: Automatic balance updates via blockchain event monitoring  
- **Secure Payments**: BigNumber.js precision handling for large token amounts
- **Purchase Limits**: Daily and account-based purchase restrictions
- **Comprehensive Testing**: Full test coverage for contracts and APIs

## 🚀 Quick Start

### Prerequisites

- Node.js 22.x
- PostgreSQL 13+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wolf_fun
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run database migrations**
   ```bash
   npm run migrate
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
wolf_fun/
├── contracts/                 # PHRS deposit smart contracts
│   ├── contracts/            # Solidity contracts
│   ├── test/                # Contract tests
│   └── scripts/             # Deployment scripts
├── src/
│   ├── controllers/         # API controllers
│   ├── models/             # Database models
│   ├── services/           # Business logic services
│   ├── routes/             # API routes
│   ├── jobs/               # Background jobs
│   ├── tests/              # API and integration tests
│   └── migrations/         # Database migrations
├── docs/                   # Documentation
└── scripts/               # Utility scripts
```

## 🔧 PHRS System Components

### Smart Contracts

- **PHRSDepositContract**: Handles PHRS token deposits with security features
- **MockERC20**: Test token contract for development

### Backend Services

- **PhrsDepositService**: Monitors blockchain events and updates user balances
- **PhrsPaymentController**: Handles PHRS-based purchases
- **PhrsDepositMonitor**: Background job for deposit processing

### API Endpoints

#### PHRS Deposit APIs
- `POST /api/phrs-deposit/bind-wallet` - Bind PHRS wallet address
- `GET /api/phrs-deposit/deposits` - Get deposit history
- `POST /api/phrs-deposit/sync-balance` - Manual balance sync

#### PHRS Payment APIs  
- `POST /api/phrs-payment/purchase` - Purchase items with PHRS
- `GET /api/phrs-payment/balance` - Get PHRS balance and history
- `GET /api/phrs-payment/products` - Get PHRS-compatible products

## 🧪 Testing

### Run All Tests
```bash
npm run test:phrs
```

### Individual Test Suites
```bash
# Smart contract tests
npm run test:contracts

# API tests
npm run test:api

# Integration tests  
npm run test:integration

# Test coverage
npm run test:coverage
```

## 🚀 Deployment

### Smart Contract Deployment

1. **Configure contract environment**
   ```bash
   cd contracts
   cp .env.example .env
   # Configure PHRS_TOKEN_ADDRESS, PRIVATE_KEY, etc.
   ```

2. **Deploy to testnet**
   ```bash
   npm run deploy:testnet
   ```

3. **Deploy to mainnet**
   ```bash
   npm run deploy:mainnet
   ```

### Backend Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start production server**
   ```bash
   npm start
   ```

3. **Using PM2 (recommended)**
   ```bash
   pm2 start ecosystem.config.js
   ```

## 📚 Documentation

- [PHRS API Documentation](docs/PHRS_API_DOCUMENTATION.md)
- [Deployment Guide](docs/PHRS_DEPLOYMENT_GUIDE.md)
- [Smart Contract Documentation](contracts/README.md)

## 🔒 Security Features

- **Smart Contract Security**: OpenZeppelin libraries, reentrancy protection
- **API Security**: JWT authentication, parameter validation
- **Transaction Safety**: Database transactions, BigNumber precision
- **Access Control**: Role-based permissions, rate limiting

## 🛠 Development

### Environment Setup

1. **Node.js Version**
   ```bash
   node --version  # Should be 22.x
   ```

2. **Database Setup**
   ```bash
   # Create database
   createdb wolf_fun
   
   # Run migrations
   npm run migrate
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

### Code Quality

- **TypeScript**: Full type safety
- **ESLint**: Code linting
- **Jest**: Testing framework
- **BigNumber.js**: Precision arithmetic

## 📊 Monitoring

### Health Checks
- `GET /api/health` - General health status
- `GET /api/phrs-deposit/health` - PHRS deposit service status
- `GET /api/phrs-payment/health` - PHRS payment service status

### Logging
- Structured logging with different levels
- Error tracking and monitoring
- Performance metrics

## 🔄 Migration from DappPortal

The PHRS system replaces the previous DappPortal payment integration:

- **Old**: `POST /api/iap/payment/create` (now returns 410 Gone)
- **New**: `POST /api/phrs-payment/purchase`

See [API Documentation](docs/PHRS_API_DOCUMENTATION.md) for migration details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the ISC License.

## 🆘 Support

For technical support or questions:

1. Check the [documentation](docs/)
2. Review [API documentation](docs/PHRS_API_DOCUMENTATION.md)
3. Run health checks to diagnose issues
4. Check application logs

## 🔮 Roadmap

- [ ] Multi-token support
- [ ] Advanced analytics dashboard
- [ ] Mobile app integration
- [ ] Cross-chain compatibility
- [ ] Enhanced security features

---

**Note**: This system requires Node.js 22 and uses BigNumber.js for precision handling of large token amounts. Ensure all environment variables are properly configured before deployment.

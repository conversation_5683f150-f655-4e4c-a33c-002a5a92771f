# Docker Compose 覆盖文件 - 用于开发环境
# 使用方法: docker compose -f docker-compose.pharos-test.yml -f docker-compose.pharos-test.override.yml up

services:
  app:
    # 开发环境配置
    environment:
      NODE_ENV: development
      DEBUG: "true"
      LOG_LEVEL: debug
    
    # 开发环境卷挂载
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    
    # 开发环境端口映射
    ports:
      - "3456:3456"
      - "9229:9229"  # Node.js 调试端口
    
    # 开发环境启动命令
    command: ["npm", "run", "dev"]
    
    # 开发环境不需要健康检查
    healthcheck:
      disable: true

  mysql:
    # 开发环境数据库配置
    environment:
      MYSQL_ROOT_PASSWORD: 00321zixun
      MYSQL_DATABASE: pharos_test_db
      MYSQL_USER: pharos_test
      MYSQL_PASSWORD: 00321zixunadmin
      TZ: Asia/Shanghai
    
    # 开发环境卷挂载
    volumes:
      - ./mysql-data-pharos-test-dev:/var/lib/mysql
      - ./mysqld.cnf:/etc/mysql/conf.d/mysqld.cnf
      - ./logs/mysql:/var/log/mysql
    
    # 开发环境端口映射
    ports:
      - "3671:3306"

  redis:
    # 开发环境 Redis 配置
    command: redis-server --requirepass joetest1123 --appendonly yes
    
    # 开发环境卷挂载
    volumes:
      - ./redis-data-pharos-test-dev:/data
      - ./logs/redis:/var/log/redis
    
    # 开发环境端口映射
    ports:
      - "6258:6379"

  # 开发环境额外服务
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander-pharos-test
    environment:
      REDIS_HOSTS: local:redis:6379:0:joetest1123
    ports:
      - "8272:8081"
    depends_on:
      - redis
    networks:
      - pharos_test

  # 数据库管理工具
  adminer:
    image: adminer:4.8.1
    container_name: adminer-pharos-test
    environment:
      ADMINER_DEFAULT_SERVER: mysql
    ports:
      - "8273:8080"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - pharos_test

我有一个新的需求：

Jackpot宝箱 - 设计文档

1. 玩法概述
Jackpot宝箱是一种基于 "每日宝箱累积" 的签到奖励系统，结合推广裂变机制，增强用户粘性并提升社交互动。用户每天可解锁一个随机宝箱，累计TON奖励，并推动长期目标。

Jackpot宝箱 會分成兩個階段 lvl1 和 lvl2

第一阶段：
1.新用户加入累积 0.01 ton 放入 Jackpot宝箱里累积，也会有一个新用户宝箱的池子，这个池子也会累积0.01ton，这主要是给前端展示用的，所以第一阶段会展示新用户宝箱的池子的进度 是否满10 ton。

Jackpot宝箱累积 = 新用户宝箱的池子的累积。

如果 Jackpot宝箱 满10 ton, 下一个开启宝箱的人就会奖励10 ton，

第一阶段累积满10ton之后停止累积，
奖励10ton之後直接進入lvl2 階段，会并进行全服公告。

第二阶段：

1.新用户加入累积 0.01 ton 到 新用户宝箱累积 ， 也会放入Jackpot宝箱累积。

2.所有用户打开宝箱会累积0.0001 ton 到 “打开宝箱池子“，也会累积到 Jackpot宝箱 里，

所以第二阶段会展示 新用户宝箱的池子 的进度 和 開啟宝箱池子的进度

Jackpot宝箱累积 = 新用户宝箱的池子的累积+“打开宝箱池子”的累积

如果Jackpot宝箱累积满20ton了，下一个开启宝箱的人会获得。

第二阶段累积满20ton之后也停止累积，

奖励20ton之後直接進入lvl3 階段，会并进行全服公告。

宝箱倒计时

倒计时：宝箱页面有一个24小时倒计时，当倒计时结束后，用户可领取一个宝箱，并重置24小时倒计时。

如果是第一次注册的时候， 註冊之後初始状态为等待領取的狀態，可以领取一次，領取之後開始該用户第一次倒計時

助力加速：玩家可通过 “分享助力” 或 “推广助力” 获得倒计时的加速。

自动领取：玩家可通過 star（telegram star）购买自動領取功能，开通功能后当倒计时结束会自动进行领取的动作。

4.社交裂变机制

 4.1 分享助力
用户打开宝箱并获得3/4级宝箱后，可分享助力鏈接至社群邀请好友"助力解锁"，每1人助力，雙方獲得額外加速+GEM。

助力链接有效时间为打开宝箱后的24小时。（超过时间将视为无效，并显示通知）

助力链接有效领取次数为12次。（超过领取次数将视为无效，并显示通知）


| 宝箱等级 | 被助力獎勵         | 助力獎勵         |
| -------- | ------------------ | ---------------- |
| 3        | 1小时 + GEM x 50   | 1小时 + GEM x 50 |
| 4        | 2小时 + GEM x 100  | 2小时 + GEM x 100|


4.2 推廣助力
用户打开宝箱后，自动加速上线宝箱倒计时。

| 宝箱等级 | 第一層   | 第二層  |
| -------- | -------- | ------- |
| 1        | 10分钟   | 5分钟   |
| 2        | 30分钟   | 15分钟  |
| 3        | 1小时    | 30分钟  |
| 4        | 12小时   | 6小时   |

我们还需要考虑数据库字段的并发问题，优先考虑使用原子操作，然后如果倒计时为0了，不会累积加速。

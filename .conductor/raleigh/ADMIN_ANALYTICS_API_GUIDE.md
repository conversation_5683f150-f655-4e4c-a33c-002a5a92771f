# 管理后台数据分析API使用指南

## 概述

本文档介绍如何使用管理后台数据分析API来获取游戏运营所需的各种统计数据。

## 认证

所有管理后台API都需要管理员权限认证。请确保：

1. 用户已通过Web3Auth登录
2. 用户钱包地址在管理员列表中（配置在环境变量`ADMIN_WALLETS`中）
3. 请求头包含有效的认证token

## API端点列表

### 基础统计

#### 1. 每日游戏启动次数
```http
GET /api/admin/analytics/game-starts?date=2024-01-01
```
**参数:**
- `date` (可选): 指定日期，格式YYYY-MM-DD，默认为今天

**响应示例:**
```json
{
  "success": true,
  "data": {
    "dailyGameStarts": 1250,
    "date": "2024-01-01"
  }
}
```

#### 2. 解锁区域统计
```http
GET /api/admin/analytics/unlocked-areas
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "unlockedAreasStats": [
      {"area": 2, "count": 500},
      {"area": 3, "count": 300},
      // ...
    ],
    "summary": {
      "totalUnlockedCount": 2000,
      "areasWithData": 19
    }
  }
}
```

#### 3. 配送线升级统计
```http
GET /api/admin/analytics/delivery-line-upgrades?levels=10,20,25,30,35,40,45,50
```
**参数:**
- `levels` (可选): 逗号分隔的等级列表，默认为10,20,25,30,35,40,45,50

#### 4. 道具使用统计
```http
GET /api/admin/analytics/booster-usage
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "boosterUsageStats": {
      "totalUsers": [
        {"type": "speed_boost", "count": 120},
        {"type": "time_warp", "count": 85}
      ],
      "totalUsage": [
        {"type": "speed_boost", "count": 450},
        {"type": "time_warp", "count": 200}
      ]
    }
  }
}
```

#### 5. 充值数据统计
```http
GET /api/admin/analytics/revenue
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "revenueStats": {
      "totalRevenue": 15420.50,
      "totalPurchases": 1250,
      "uniquePayers": 380,
      "revenueByMethod": [
        {"method": "kaia", "amount": 8500.20, "count": 650},
        {"method": "stripe", "amount": 6920.30, "count": 600}
      ]
    }
  }
}
```

### 玩家行为统计

#### 6. 每日APP开启统计
```http
GET /api/admin/analytics/daily-app-opens?days=7
```
**参数:**
- `days` (可选): 查询天数，默认7天

#### 7. 牧场区升级统计
```http
GET /api/admin/analytics/farm-plot-upgrades?levels=10,20,25,30,35,40,45,50
```

#### 8. 任务完成统计
```http
GET /api/admin/analytics/task-completion
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "taskCompletionStats": {
      "totalCompletions": 5420,
      "taskStats": [
        {"taskId": 1, "taskName": "每日签到", "completions": 1200},
        {"taskId": 2, "taskName": "加入TG频道", "completions": 800}
      ]
    }
  }
}
```

#### 9. 邀请好友统计
```http
GET /api/admin/analytics/referral
```

#### 10. 宝箱统计
```http
GET /api/admin/analytics/chest
```

### 玩家留存和增长

#### 11. 玩家增长和留存统计
```http
GET /api/admin/analytics/player-growth-retention
```
**响应示例:**
```json
{
  "success": true,
  "data": {
    "playerGrowthRetentionStats": {
      "dailyNewUsers": [
        {"date": "2024-01-01", "count": 150},
        {"date": "2024-01-02", "count": 180}
      ],
      "totalUsers7Days": 1250,
      "dayOneRetention": 45.5,
      "day7Retention": 23.2
    }
  }
}
```

#### 12. 玩家资源统计
```http
GET /api/admin/analytics/player-resources
```

#### 13. 玩家游玩时长统计
```http
GET /api/admin/analytics/play-time
```

### 综合统计

#### 14. 综合统计面板
```http
GET /api/admin/analytics/dashboard
```
返回所有主要统计数据的汇总，适合用于管理后台首页展示。

#### 15. 自定义查询
```http
POST /api/admin/analytics/custom-query
```
**请求体:**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-12-31",
  "metrics": ["dailyGameStarts", "revenue", "unlockedAreas"],
  "filters": {
    "userType": "all"
  }
}
```

## 使用示例

### JavaScript/Node.js

```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3456',
  headers: {
    'Authorization': `Bearer ${your_admin_token}`,
    'Content-Type': 'application/json'
  }
});

// 获取今日游戏启动数据
async function getTodayGameStarts() {
  try {
    const response = await api.get('/api/admin/analytics/game-starts');
    console.log('今日游戏启动次数:', response.data.data.dailyGameStarts);
  } catch (error) {
    console.error('获取数据失败:', error.response?.data?.message);
  }
}

// 获取综合面板数据
async function getDashboard() {
  try {
    const response = await api.get('/api/admin/analytics/dashboard');
    const stats = response.data.data.dashboardStats;
    
    console.log('游戏统计:', stats.gameStats);
    console.log('玩家进度:', stats.playerProgress);
    console.log('收入统计:', stats.revenue);
    console.log('留存数据:', stats.retention);
  } catch (error) {
    console.error('获取面板数据失败:', error.response?.data?.message);
  }
}
```

### cURL

```bash
# 获取每日游戏启动统计
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3456/api/admin/analytics/game-starts"

# 获取充值统计
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "http://localhost:3456/api/admin/analytics/revenue"

# 自定义查询
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"metrics":["revenue","dailyGameStarts"],"startDate":"2024-01-01"}' \
     "http://localhost:3456/api/admin/analytics/custom-query"
```

## 测试工具

项目包含一个测试脚本 `test_admin_analytics_api.js`，可以用来验证所有API端点：

```bash
# 安装依赖（如果需要）
npm install axios

# 运行测试
node test_admin_analytics_api.js
```

## 权限配置

### 环境变量配置

在 `.env` 文件中添加管理员钱包地址：

```env
ADMIN_WALLETS=0x1234...,0x5678...,0xabcd...
```

### 开发环境

开发环境下，可以使用预设的测试管理员地址：
- `test_admin_wallet`
- `dev_admin_wallet`

## 性能优化建议

1. **缓存**: 对于计算量大的统计数据，建议使用Redis缓存
2. **分页**: 对于大量数据的端点，考虑添加分页参数
3. **异步处理**: 复杂统计可以考虑使用后台任务处理
4. **索引优化**: 确保数据库相关字段有适当的索引

## 错误处理

常见错误代码：
- `401`: 未认证，需要有效的登录token
- `403`: 权限不足，需要管理员权限
- `500`: 服务器内部错误，检查日志获取详细信息

## 注意事项

1. 所有统计数据基于当前数据库状态实时计算
2. 大量数据查询可能影响性能，建议在低峰期使用
3. 留存率计算需要足够的历史数据才能准确
4. 游玩时长统计基于活跃时间估算，可能需要更精确的埋点数据

## 更新日志

- v1.0.0: 初始版本，包含所有基础统计功能
- 支持的统计维度：游戏启动、区域解锁、设施升级、道具使用、充值、任务完成、邀请好友、宝箱获取、玩家留存、资源统计
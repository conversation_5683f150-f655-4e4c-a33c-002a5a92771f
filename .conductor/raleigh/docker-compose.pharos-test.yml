name: moofun_pharos
services:
  redis:
    image: redis:7.2.4-alpine
    container_name: redis-7.2.4-pharos-test
    command: redis-server --requirepass joetest1123
    ports:
      - "6258:6379"
    networks:
      - pharos_test_network2

  mysql:
    image: mysql:8.3.0
    container_name: mysql-8.3.0-pharos-test
    environment:
      MYSQL_ROOT_PASSWORD: 00321zixun
      MYSQL_DATABASE: pharos_test_db
      MYSQL_USER: pharos_test
      MYSQL_PASSWORD: 00321zixunadmin
      TZ: Asia/Shanghai
      MYSQL_ROOT_HOST: '%'
    volumes:
      - ./mysql-data-pharos-test:/var/lib/mysql
      - ./mysqld.pharos-test.cnf:/etc/mysql/conf.d/mysqld.cnf
      - ./logs/mysql:/var/log/mysql
    ports:
      - "3671:3306"
    networks:
      - pharos_test_network2
    healthcheck:
      test: ["C<PERSON>", "mysqladmin", "ping", "-h", "localhost", "-u$$MYSQL_USER", "-p$$MYSQL_PASSWORD"]
      interval: 10s
      timeout: 5s
      retries: 5
  
  phpmyadmin:
    image: phpmyadmin:5.2.1
    container_name: phpmyadmin5.2.1-pharos-test
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      PMA_ARBITRARY: 1
      PMA_HOST: mysql
    ports:
      - "8271:80"
    volumes:
      - ./uploads.ini:/usr/local/etc/php/conf.d/uploads.ini:ro
    networks:
      - pharos_test_network2

networks:
  pharos_test_network2:
    name: pharos_test_network2
    driver: bridge

// 测试时间窗口修复（从5秒改为1秒）
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.NJ3RM_PzHkmU5BPkqmSTPweMnjqhegFqeCko6lyH2Fg';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

function sleep(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function testTimeWindowFix() {
  console.log('🧪 测试时间窗口修复（从5秒改为1秒）...');
  console.log('='.repeat(60));
  
  try {
    // 第一次调用：更新lastActiveTime
    console.log('📋 第一步：更新lastActiveTime...');
    const firstResponse = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 1,
      milkOperations: {
        produce: 1,
        consume: 1
      }
    }, config);
    
    console.log('第一次调用结果:', firstResponse.data.data.changes.usedStrictValidation ? '使用严格验证' : '未使用严格验证');
    if (firstResponse.data.data.changes.timeWindowReason) {
      console.log('时间窗口原因:', firstResponse.data.data.changes.timeWindowReason);
    }
    
    // 测试1：立即调用（应该被拒绝，显示"最少1秒"）
    console.log('\n⏰ 测试1：立即调用（应该被拒绝）...');
    const immediateResponse = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 1,
      milkOperations: {
        produce: 1,
        consume: 1
      }
    }, config);
    
    console.log('立即调用结果:');
    console.log('   使用严格验证:', immediateResponse.data.data.changes.usedStrictValidation);
    console.log('   时间窗口有效:', immediateResponse.data.data.changes.timeWindowValid);
    console.log('   时间窗口原因:', immediateResponse.data.data.changes.timeWindowReason || '无');
    console.log('   消息:', immediateResponse.data.message);
    
    // 测试2：等待1.5秒后调用（应该成功）
    console.log('\n⏰ 测试2：等待1.5秒后调用（应该成功）...');
    await sleep(1.5);
    
    const delayedResponse = await axios.post(`${BASE_URL}/api/wallet/strict-batch-update-resources`, {
      gemRequest: 1,
      milkOperations: {
        produce: 1,
        consume: 1
      }
    }, config);
    
    console.log('延迟调用结果:');
    console.log('   使用严格验证:', delayedResponse.data.data.changes.usedStrictValidation);
    console.log('   验证通过:', delayedResponse.data.data.changes.validationPassed);
    console.log('   时间窗口有效:', delayedResponse.data.data.changes.timeWindowValid);
    console.log('   时间窗口原因:', delayedResponse.data.data.changes.timeWindowReason || '无');
    console.log('   消息:', delayedResponse.data.message);
    
    // 分析修复效果
    console.log('\n🔍 修复效果分析:');
    if (immediateResponse.data.data.changes.timeWindowReason && 
        immediateResponse.data.data.changes.timeWindowReason.includes('最少1秒')) {
      console.log('✅ 修复成功！错误消息已更新为"最少1秒"');
    } else {
      console.log('❌ 修复可能有问题，错误消息未按预期更新');
    }
    
    if (delayedResponse.data.data.changes.usedStrictValidation) {
      console.log('✅ 修复成功！1.5秒后可以正常使用严格验证');
    } else {
      console.log('❌ 修复可能有问题，1.5秒后仍无法使用严格验证');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testTimeWindowFix();

// 测试简化后的严格验证逻辑
const axios = require('axios');

const BASE_URL = 'http://localhost:3456';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsIndhbGxldElkIjoxLCJ3YWxsZXRBZGRyZXNzIjoiMFFEaW9QVFNUb2RPdnBKZTVjelk5NjNKcnk0UWlsSDN0TUJ6Wm4tMXZGYmhObUxPIiwibmV0d29yayI6Ii0zIiwiaWF0IjoxNzQ5MzQ5NDg3LCJleHAiOjE3NTQ1MzM0ODd9.eBkEf1ElWnJOGpYM-YZsuKY1SXjq2jPy_OXl41Ogozc';

const config = {
  headers: {
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// 格式化数值显示
function formatNumber(num) {
  if (typeof num === 'number') {
    return num.toFixed(3);
  }
  return parseFloat(num || 0).toFixed(3);
}

// 测试简化验证逻辑
async function testSimplifiedValidation() {
  console.log('🧪 测试简化后的严格验证逻辑');
  console.log('='.repeat(60));
  console.log('📋 验证公式:');
  console.log('1. 牛奶产量 < 平均每秒产量 × 更新间隔 × 1.5');
  console.log('2. 牛奶消耗量 < 平均每秒消耗量 × 更新间隔 × 1.5');
  console.log('3. 宝石增加 < 牛奶消耗 × 牛奶汇率 × 1.5');
  console.log('='.repeat(60));
  
  const testCases = [
    {
      name: '基础验证测试',
      request: {
        gemRequest: 1.000,
        milkOperations: {
          produce: 2.000,
          consume: 1.000
        }
      },
      description: '测试基础的每秒速率计算'
    },
    {
      name: '边界值测试',
      request: {
        gemRequest: 0.500,
        milkOperations: {
          produce: 1.500,
          consume: 0.500
        }
      },
      description: '测试接近1.5倍上限的数值'
    },
    {
      name: '宝石转换测试',
      request: {
        gemRequest: 2.000,
        milkOperations: {
          produce: 2.000,
          consume: 2.000
        }
      },
      description: '测试宝石转换汇率验证'
    },
    {
      name: '超出范围测试',
      request: {
        gemRequest: 10.000,
        milkOperations: {
          produce: 10.000,
          consume: 10.000
        }
      },
      description: '测试超出1.5倍范围的请求（应该失败）'
    },
    {
      name: '只产奶测试',
      request: {
        milkOperations: {
          produce: 2.000
        }
      },
      description: '只请求牛奶产量，不消耗不要GEM'
    }
  ];
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n📝 测试 ${i + 1}/${testCases.length}: ${testCase.name}`);
    console.log(`   描述: ${testCase.description}`);
    console.log(`   请求参数:`, JSON.stringify(testCase.request, null, 2));
    
    try {
      const response = await axios.post(
        `${BASE_URL}/api/wallet/strict-batch-update-resources`,
        testCase.request,
        config
      );
      
      if (response.data.ok) {
        const data = response.data.data;
        const changes = data.changes;
        
        console.log('✅ 请求成功');
        console.log(`   消息: ${response.data.message}`);
        
        // 显示验证结果
        console.log('📊 验证结果:');
        console.log(`   使用严格验证: ${changes.usedStrictValidation ? '是' : '否'}`);
        console.log(`   验证通过: ${changes.validationPassed ? '是' : '否'}`);
        console.log(`   回退到旧方法: ${changes.fallbackToOldMethod ? '是' : '否'}`);
        console.log(`   时间窗口有效: ${changes.timeWindowValid !== false ? '是' : '否'}`);
        console.log(`   时间间隔: ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒`);
        
        // 显示资源变化
        console.log('💰 资源变化:');
        console.log(`   GEM: ${formatNumber(data.beforeUpdate.gem)} → ${formatNumber(data.afterUpdate.gem)} (${changes.details.gem.increased > 0 ? '+' : ''}${formatNumber(changes.details.gem.increased)})`);
        console.log(`   牛奶: ${formatNumber(data.beforeUpdate.pendingMilk)} → ${formatNumber(data.afterUpdate.pendingMilk)} (+${formatNumber(changes.details.milk.increased)} -${formatNumber(changes.details.milk.decreased)})`);
        
        // 显示简化验证的详细计算
        if (changes.strictValidationDetails && changes.strictValidationDetails.validationDetails) {
          console.log('🔍 简化验证详情:');
          const details = changes.strictValidationDetails.validationDetails;
          
          // 验证1：牛奶产量
          console.log(`   牛奶产量验证: ${details.milkProduction.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.milkProduction.requested)}`);
          console.log(`     理论: ${formatNumber(details.milkProduction.calculated)} (每秒速率 × ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒)`);
          console.log(`     允许: ${formatNumber(details.milkProduction.maxAllowed)} (理论值 × 1.5)`);
          console.log(`     公式: ${formatNumber(details.milkProduction.requested)} ≤ ${formatNumber(details.milkProduction.calculated)} × 1.5 = ${formatNumber(details.milkProduction.maxAllowed)}`);
          
          // 验证2：牛奶消耗
          console.log(`   牛奶消耗验证: ${details.milkConsumption.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.milkConsumption.requested)}`);
          console.log(`     理论: ${formatNumber(details.milkConsumption.calculated)} (每秒速率 × ${formatNumber(changes.productionRates.timeElapsedSeconds)}秒)`);
          console.log(`     允许: ${formatNumber(details.milkConsumption.maxAllowed)} (理论值 × 1.5)`);
          console.log(`     公式: ${formatNumber(details.milkConsumption.requested)} ≤ ${formatNumber(details.milkConsumption.calculated)} × 1.5 = ${formatNumber(details.milkConsumption.maxAllowed)}`);
          
          // 验证3：宝石转换
          console.log(`   宝石转换验证: ${details.gemConversion.valid ? '✅' : '❌'}`);
          console.log(`     请求: ${formatNumber(details.gemConversion.requested)}`);
          console.log(`     基于消耗: ${formatNumber(details.gemConversion.calculatedFromMilk)} (${formatNumber(details.milkConsumption.requested)} × ${formatNumber(details.gemConversion.conversionRate)})`);
          console.log(`     允许: ${formatNumber(details.gemConversion.maxAllowed)} (基于消耗 × 1.5)`);
          console.log(`     公式: ${formatNumber(details.gemConversion.requested)} ≤ ${formatNumber(details.milkConsumption.requested)} × ${formatNumber(details.gemConversion.conversionRate)} × 1.5 = ${formatNumber(details.gemConversion.maxAllowed)}`);
          
          if (!changes.validationPassed && changes.strictValidationDetails.reason) {
            console.log(`   失败原因: ${changes.strictValidationDetails.reason}`);
          }
        }
        
        // 分析结果
        if (changes.validationPassed) {
          console.log('🎉 简化验证通过！直接基于每秒速率计算成功');
        } else if (changes.fallbackToOldMethod) {
          console.log('⚠️  简化验证失败，已回退到旧方法');
        } else if (changes.timeWindowValid === false) {
          console.log('⏰ 时间窗口无效，已更新lastActiveTime');
        }
        
      } else {
        console.log('❌ 请求失败');
        console.log(`   错误: ${response.data.message}`);
      }
      
    } catch (error) {
      console.log('❌ 请求异常');
      if (error.response) {
        console.log(`   状态码: ${error.response.status}`);
        console.log(`   错误信息: ${error.response.data.message || error.response.data.error}`);
      } else {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试间隔
    if (i < testCases.length - 1) {
      console.log('   ⏳ 等待6秒避免频率限制...');
      await new Promise(resolve => setTimeout(resolve, 6000));
    }
  }
}

// 主测试函数
async function runTest() {
  console.log('🚀 测试简化后的严格验证批量资源更新接口');
  console.log('='.repeat(80));
  console.log('🎯 目标：验证简化的每秒速率计算逻辑');
  console.log('📐 不再使用复杂的事件队列和生产步骤');
  console.log('⚡ 直接基于每秒速率 × 时间间隔 × 1.5倍容错');
  console.log('='.repeat(80));
  
  await testSimplifiedValidation();
  
  console.log('\n' + '='.repeat(80));
  console.log('🎉 测试完成！');
  
  console.log('\n📋 简化验证逻辑总结:');
  console.log('1. ✅ 牛奶产量 = 农场每秒产量 × 时间间隔 × 1.5');
  console.log('2. ✅ 牛奶消耗 = 出货线每秒消耗 × 时间间隔 × 1.5');
  console.log('3. ✅ 宝石增加 = 前端牛奶消耗 × 转换汇率 × 1.5');
  console.log('4. ✅ 不再考虑复杂的生产周期和事件队列');
  console.log('5. ✅ 计算更简单、更直接、更容易理解');
  
  console.log('\n💡 优势:');
  console.log('- 计算逻辑简化，减少复杂性');
  console.log('- 基于每秒速率，更直观');
  console.log('- 性能更好，计算更快');
  console.log('- 更容易调试和维护');
}

// 运行测试
runTest().catch(console.error);

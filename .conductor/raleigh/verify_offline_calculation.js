// 验证离线奖励计算逻辑的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:3457/api';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************.lXoRXISFZ1PoPkGOOaDzgTBD8fR856jsi5_CAiFnmqY';

const createAuthHeaders = () => {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${JWT_TOKEN}`,
    'Accept-Language': 'zh'
  };
};

// 获取农场配置信息
async function getFarmConfigs() {
  try {
    const response = await axios.get(`${BASE_URL}/admin/farm-config`, {
      headers: createAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.log('获取农场配置失败:', error.response?.data || error.message);
    return null;
  }
}

// 获取用户农场区域信息
async function getUserFarmPlots() {
  try {
    const response = await axios.get(`${BASE_URL}/farm/plots`, {
      headers: createAuthHeaders()
    });
    return response.data;
  } catch (error) {
    console.log('获取用户农场区域失败:', error.response?.data || error.message);
    return null;
  }
}

// 手动计算离线奖励
async function manualCalculateOfflineReward(offlineTimeSeconds) {
  console.log('\n=== 手动计算离线奖励 ===');
  
  // 获取农场配置
  const farmConfigs = await getFarmConfigs();
  if (!farmConfigs || !farmConfigs.ok) {
    console.log('❌ 无法获取农场配置');
    return;
  }
  
  // 获取用户农场区域
  const farmPlots = await getUserFarmPlots();
  if (!farmPlots || !farmPlots.ok) {
    console.log('❌ 无法获取用户农场区域');
    return;
  }
  
  console.log(`离线时间: ${offlineTimeSeconds} 秒`);
  console.log(`已解锁农场区域数量: ${farmPlots.data.length}`);
  
  // 创建配置映射
  const configMap = new Map();
  farmConfigs.data.forEach(config => {
    configMap.set(config.grade, config);
  });
  
  let totalReward = 0;
  
  console.log('\n各农场区域离线收益明细:');
  farmPlots.data.forEach(plot => {
    if (plot.isUnlocked) {
      const config = configMap.get(plot.level);
      if (config) {
        const plotReward = parseFloat(config.offline) * offlineTimeSeconds;
        totalReward += plotReward;
        console.log(`区域 ${plot.plotNumber} (等级 ${plot.level}): ${config.offline}/秒 × ${offlineTimeSeconds}秒 = ${plotReward.toFixed(3)} GEM`);
      }
    }
  });
  
  console.log(`\n手动计算总离线奖励: ${totalReward.toFixed(3)} GEM`);
  return totalReward;
}

// 主验证函数
async function verifyCalculation() {
  console.log('🔍 开始验证离线奖励计算逻辑...');
  
  // 获取API计算的离线奖励
  try {
    const response = await axios.get(`${BASE_URL}/wallet/offline-reward`, {
      headers: createAuthHeaders()
    });
    
    if (response.data.ok && response.data.data.isOffline) {
      const apiReward = response.data.data.offlineReward.gem;
      const offlineTime = response.data.data.offlineTime;
      
      console.log(`\nAPI计算结果:`);
      console.log(`离线时间: ${offlineTime} 秒`);
      console.log(`离线奖励: ${apiReward} GEM`);
      
      // 手动计算验证
      const manualReward = await manualCalculateOfflineReward(offlineTime);
      
      if (manualReward !== null) {
        const difference = Math.abs(apiReward - manualReward);
        const tolerance = 0.001; // 允许的误差范围
        
        console.log(`\n📊 计算结果对比:`);
        console.log(`API计算: ${apiReward} GEM`);
        console.log(`手动计算: ${manualReward.toFixed(3)} GEM`);
        console.log(`差异: ${difference.toFixed(6)} GEM`);
        
        if (difference <= tolerance) {
          console.log('✅ 计算逻辑验证通过！');
        } else {
          console.log('❌ 计算逻辑存在差异！');
        }
      }
    } else {
      console.log('用户当前没有离线奖励');
    }
    
  } catch (error) {
    console.log('❌ 获取API离线奖励失败:', error.response?.data || error.message);
  }
}

// 运行验证
verifyCalculation().catch(console.error);

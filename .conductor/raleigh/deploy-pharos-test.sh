#!/bin/bash

# Pharos Test 环境独立部署脚本
# 不依赖 docker-compose，直接使用 Docker 命令

# 设置脚本在遇到错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
IMAGE_NAME="wolf-fun-pharos-test"
CONTAINER_NAME="wolf-fun-pharos-test-container"
APP_PORT="3456"
HOST_PORT="3457"
NETWORK_NAME="pharos_network"

echo -e "${BLUE}🚀 开始部署 Pharos Test 环境...${NC}"

# 1. 构建 Docker 镜像
echo -e "${YELLOW}🔨 构建 Docker 镜像...${NC}"
docker build \
    --no-cache \
    --pull \
    --rm \
    -f Dockerfile.pharos-test \
    -t ${IMAGE_NAME} \
    .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Docker 镜像构建成功${NC}"
else
    echo -e "${RED}❌ Docker 镜像构建失败${NC}"
    exit 1
fi

# 2. 检查并使用现有网络
echo -e "${YELLOW}🌐 检查 Docker 网络...${NC}"
if ! docker network ls | grep -q ${NETWORK_NAME}; then
    echo -e "${YELLOW}🌐 创建 Docker 网络...${NC}"
    docker network create ${NETWORK_NAME}
else
    echo -e "${GREEN}✅ 网络 ${NETWORK_NAME} 已存在${NC}"
fi

# 3. 停止并删除旧的应用容器（如果存在）
echo -e "${YELLOW}🛑 停止并删除旧容器...${NC}"
docker stop ${CONTAINER_NAME} 2>/dev/null || true
docker rm ${CONTAINER_NAME} 2>/dev/null || true

# 4. 清理未使用的镜像
echo -e "${YELLOW}🧹 清理未使用的镜像...${NC}"
docker image prune -f

# 5. 启动数据库和 Redis（如果没有运行）
echo -e "${YELLOW}🗄️ 确保数据库和 Redis 运行...${NC}"

# 检查 MySQL 容器
if ! docker ps | grep -q "mysql-pharos-test"; then
    echo -e "${YELLOW}🚀 启动 MySQL...${NC}"
    docker run -d \
        --name mysql-pharos-test \
        --network ${NETWORK_NAME} \
        -p 3671:3306 \
        -e MYSQL_ROOT_PASSWORD=00321zixun \
        -e MYSQL_DATABASE=pharos_test_db \
        -e MYSQL_USER=pharos_test \
        -e MYSQL_PASSWORD=00321zixunadmin \
        -e TZ=Asia/Shanghai \
        -e MYSQL_ROOT_HOST=% \
        -v $(pwd)/mysql-data-pharos-test:/var/lib/mysql \
        -v $(pwd)/mysqld.pharos-test.cnf:/etc/mysql/conf.d/mysqld.cnf \
        --health-cmd="mysqladmin ping -h localhost -u pharos_test -p00321zixunadmin" \
        --health-interval=10s \
        --health-timeout=5s \
        --health-retries=5 \
        mysql:8.3.0

    # 等待 MySQL 启动
    echo -e "${YELLOW}⏳ 等待 MySQL 启动...${NC}"
    sleep 30
else
    echo -e "${GREEN}✅ MySQL 已在运行${NC}"
    # 确保 MySQL 容器在正确的网络中
    docker network connect ${NETWORK_NAME} mysql-pharos-test 2>/dev/null || true
fi

# 检查 Redis 容器
if ! docker ps | grep -q "redis-pharos-test"; then
    echo -e "${YELLOW}🚀 启动 Redis...${NC}"
    docker run -d \
        --name redis-pharos-test \
        --network ${NETWORK_NAME} \
        -p 6258:6379 \
        redis:7.2.4-alpine \
        redis-server --requirepass joetest1123

    sleep 5
else
    echo -e "${GREEN}✅ Redis 已在运行${NC}"
    # 确保 Redis 容器在正确的网络中
    docker network connect ${NETWORK_NAME} redis-pharos-test 2>/dev/null || true
fi

# 6. 运行新的应用容器实例
echo -e "${YELLOW}🚀 启动应用容器...${NC}"
docker run -d \
    -p ${HOST_PORT}:${APP_PORT} \
    --name ${CONTAINER_NAME} \
    --network ${NETWORK_NAME} \
    --env-file .env.pharos-test \
    -e DB_HOST=mysql-pharos-test \
    -e REDIS_HOST=redis-pharos-test \
    --restart unless-stopped \
    ${IMAGE_NAME}

# 7. 等待应用启动
echo -e "${YELLOW}⏳ 等待应用启动...${NC}"
sleep 15

# 8. 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
max_attempts=10
attempt=1

while [ $attempt -le $max_attempts ]; do
    echo "健康检查尝试 $attempt/$max_attempts..."
    
    if curl -f http://localhost:${HOST_PORT}/api/health/ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 应用健康检查通过！${NC}"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        echo -e "${RED}❌ 应用健康检查失败${NC}"
        echo -e "${YELLOW}📝 查看应用日志:${NC}"
        docker logs ${CONTAINER_NAME} --tail=20
        exit 1
    fi
    
    sleep 5
    ((attempt++))
done

# 9. 初始化数据库（如果需要）
echo -e "${YELLOW}🗄️ 初始化数据库...${NC}"
docker exec ${CONTAINER_NAME} node scripts/init-pharos-test-db.js || echo -e "${YELLOW}⚠️ 数据库可能已初始化${NC}"

# 10. 运行种子数据（如果需要）
echo -e "${YELLOW}🌱 运行种子数据...${NC}"
docker exec ${CONTAINER_NAME} npm run seed:tasks 2>/dev/null || echo -e "${YELLOW}⚠️ 种子数据可能已存在或命令不存在${NC}"

# 11. 显示部署结果
echo -e "${GREEN}🎉 Pharos Test 环境部署成功！${NC}"
echo -e "${BLUE}📊 部署信息:${NC}"
echo -e "   - 应用容器: ${CONTAINER_NAME}"
echo -e "   - 镜像: ${IMAGE_NAME}"
echo -e "   - 网络: ${NETWORK_NAME}"
echo -e "   - 部署时间: $(date)"

echo -e "${BLUE}📋 访问信息:${NC}"
echo -e "   - API: http://localhost:${HOST_PORT}"
echo -e "   - 健康检查: http://localhost:${HOST_PORT}/api/health/ping"
echo -e "   - MySQL: localhost:3671"
echo -e "   - Redis: localhost:6258"

# 12. 显示容器状态
echo -e "${BLUE}📊 容器状态:${NC}"
docker ps | grep -E "(pharos-test|${CONTAINER_NAME})"

# 13. 显示最近日志
echo -e "${BLUE}📝 最近应用日志:${NC}"
docker logs ${CONTAINER_NAME} --tail=10

echo -e "${YELLOW}💡 管理命令:${NC}"
echo -e "   - 查看日志: docker logs ${CONTAINER_NAME} -f"
echo -e "   - 重启应用: docker restart ${CONTAINER_NAME}"
echo -e "   - 停止应用: docker stop ${CONTAINER_NAME}"
echo -e "   - 进入容器: docker exec -it ${CONTAINER_NAME} bash"
echo -e "   - 测试连接: curl http://localhost:${HOST_PORT}/api/health/ping"

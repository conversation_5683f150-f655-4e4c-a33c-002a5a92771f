// test_admin_analytics_api.js
// 管理后台数据分析API测试脚本

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:3456'; // 根据实际端口调整
const ADMIN_TOKEN = 'your_admin_token_here'; // 需要替换为真实的管理员令牌

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// 测试用的API端点列表
const API_ENDPOINTS = [
  {
    name: '每日游戏启动统计',
    url: '/api/admin/analytics/game-starts',
    method: 'GET'
  },
  {
    name: '解锁区域统计', 
    url: '/api/admin/analytics/unlocked-areas',
    method: 'GET'
  },
  {
    name: '配送线升级统计',
    url: '/api/admin/analytics/delivery-line-upgrades',
    method: 'GET'
  },
  {
    name: '道具使用统计',
    url: '/api/admin/analytics/booster-usage', 
    method: 'GET'
  },
  {
    name: '充值数据统计',
    url: '/api/admin/analytics/revenue',
    method: 'GET'
  },
  {
    name: '每日APP开启统计',
    url: '/api/admin/analytics/daily-app-opens?days=7',
    method: 'GET'
  },
  {
    name: '牧场区升级统计',
    url: '/api/admin/analytics/farm-plot-upgrades',
    method: 'GET'
  },
  {
    name: '任务完成统计',
    url: '/api/admin/analytics/task-completion',
    method: 'GET'
  },
  {
    name: '邀请好友统计',
    url: '/api/admin/analytics/referral',
    method: 'GET'
  },
  {
    name: '宝箱统计',
    url: '/api/admin/analytics/chest',
    method: 'GET'
  },
  {
    name: '玩家增长和留存统计',
    url: '/api/admin/analytics/player-growth-retention',
    method: 'GET'
  },
  {
    name: '玩家资源统计',
    url: '/api/admin/analytics/player-resources',
    method: 'GET'
  },
  {
    name: '玩家游玩时长统计',
    url: '/api/admin/analytics/play-time',
    method: 'GET'
  },
  {
    name: '综合统计面板',
    url: '/api/admin/analytics/dashboard',
    method: 'GET'
  }
];

// 测试单个API端点
async function testEndpoint(endpoint) {
  try {
    console.log(`\n🧪 测试: ${endpoint.name}`);
    console.log(`📍 URL: ${endpoint.method} ${endpoint.url}`);
    
    const startTime = Date.now();
    const response = await api.request({
      method: endpoint.method,
      url: endpoint.url
    });
    const endTime = Date.now();
    
    console.log(`✅ 状态: ${response.status}`);
    console.log(`⏱️  响应时间: ${endTime - startTime}ms`);
    console.log(`📊 数据样例:`, JSON.stringify(response.data, null, 2).substring(0, 200) + '...');
    
    return {
      success: true,
      status: response.status,
      responseTime: endTime - startTime,
      dataSize: JSON.stringify(response.data).length
    };
  } catch (error) {
    console.log(`❌ 错误: ${error.response?.status || 'NETWORK_ERROR'}`);
    console.log(`📝 错误信息:`, error.response?.data?.message || error.message);
    
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data?.message || error.message
    };
  }
}

// 测试自定义查询API
async function testCustomQuery() {
  try {
    console.log(`\n🧪 测试: 自定义查询API`);
    console.log(`📍 URL: POST /api/admin/analytics/custom-query`);
    
    const payload = {
      startDate: '2024-01-01',
      endDate: '2024-12-31', 
      metrics: ['dailyGameStarts', 'revenue', 'unlockedAreas'],
      filters: {
        userType: 'all'
      }
    };
    
    const startTime = Date.now();
    const response = await api.post('/api/admin/analytics/custom-query', payload);
    const endTime = Date.now();
    
    console.log(`✅ 状态: ${response.status}`);
    console.log(`⏱️  响应时间: ${endTime - startTime}ms`);
    console.log(`📊 数据样例:`, JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    
    return {
      success: true,
      status: response.status,
      responseTime: endTime - startTime
    };
  } catch (error) {
    console.log(`❌ 错误: ${error.response?.status || 'NETWORK_ERROR'}`);
    console.log(`📝 错误信息:`, error.response?.data?.message || error.message);
    
    return {
      success: false,
      status: error.response?.status || 0,
      error: error.response?.data?.message || error.message
    };
  }
}

// 执行所有测试
async function runAllTests() {
  console.log('🚀 开始管理后台数据分析API测试');
  console.log(`🌐 基础URL: ${BASE_URL}`);
  console.log(`🔑 认证: ${ADMIN_TOKEN ? '已配置' : '未配置（可能导致401错误）'}`);
  console.log('='.repeat(60));
  
  const results = [];
  
  // 测试所有GET端点
  for (const endpoint of API_ENDPOINTS) {
    const result = await testEndpoint(endpoint);
    results.push({
      name: endpoint.name,
      ...result
    });
    
    // 避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // 测试自定义查询
  const customQueryResult = await testCustomQuery();
  results.push({
    name: '自定义查询API',
    ...customQueryResult
  });
  
  // 输出测试总结
  console.log('\n' + '='.repeat(60));
  console.log('📋 测试总结:');
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount > 0) {
    const avgResponseTime = results
      .filter(r => r.success && r.responseTime)
      .reduce((sum, r) => sum + r.responseTime, 0) / successCount;
    console.log(`⏱️  平均响应时间: ${Math.round(avgResponseTime)}ms`);
  }
  
  // 显示失败的测试
  const failures = results.filter(r => !r.success);
  if (failures.length > 0) {
    console.log('\n❌ 失败的测试:');
    failures.forEach(failure => {
      console.log(`   - ${failure.name}: ${failure.error}`);
    });
  }
  
  // 性能建议
  if (successCount > 0) {
    const slowTests = results.filter(r => r.success && r.responseTime > 1000);
    if (slowTests.length > 0) {
      console.log('\n⚠️  性能警告（响应时间>1秒）:');
      slowTests.forEach(test => {
        console.log(`   - ${test.name}: ${test.responseTime}ms`);
      });
    }
  }
  
  console.log('\n🎉 测试完成！');
}

// 健康检查
async function healthCheck() {
  try {
    console.log('🏥 执行健康检查...');
    const response = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 服务器健康状态正常');
    return true;
  } catch (error) {
    console.log('❌ 服务器健康检查失败:', error.message);
    console.log('💡 请确保服务器正在运行且端口正确');
    return false;
  }
}

// 主执行函数
async function main() {
  console.log('管理后台数据分析API测试工具');
  console.log('=' * 40);
  
  // 执行健康检查
  const isHealthy = await healthCheck();
  if (!isHealthy) {
    console.log('🛑 由于健康检查失败，跳过API测试');
    return;
  }
  
  // 执行所有测试
  await runAllTests();
}

// 检查是否直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  runAllTests,
  testEndpoint,
  healthCheck
};
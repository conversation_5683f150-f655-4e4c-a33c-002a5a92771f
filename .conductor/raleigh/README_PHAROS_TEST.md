# Pharos Test 分支 - 独立数据库环境

## 🎯 快速开始

### 1. 启动数据库
```bash
npm run db:pharos-test:start
```

### 2. 初始化表结构
```bash
npm run db:pharos-test:init
```

### 3. 测试连接
```bash
npm run db:pharos-test:test
```

### 4. 启动应用
```bash
npm run dev
```

## 📊 数据库信息

- **数据库**: pharos_test_db
- **端口**: 3671
- **Redis**: 6258
- **phpMyAdmin**: http://localhost:8271

## 🛠️ 常用命令

```bash
# 数据库管理
npm run db:pharos-test:start    # 启动
npm run db:pharos-test:stop     # 停止
npm run db:pharos-test:restart  # 重启
npm run db:pharos-test:reset    # 重置
npm run db:pharos-test:test     # 测试

# 查看状态
docker ps | grep pharos-test
```

## 📁 重要文件

- `docker-compose.pharos-test.yml` - Docker 配置
- `.env` - 环境变量
- `docs/PHAROS_TEST_DATABASE.md` - 详细文档

## ✅ 环境验证

运行以下命令确保环境正常：

```bash
# 1. 检查数据库容器
docker ps | grep pharos-test

# 2. 测试数据库连接
npm run db:pharos-test:test

# 3. 查看表结构
docker compose -f docker-compose.pharos-test.yml exec mysql mysql -u pharos_test -p00321zixunadmin pharos_test_db -e "SHOW TABLES;"
```

应该看到以下表：
- users
- user_wallets  
- farm_plots
- delivery_lines
- phrs_deposits

## 🔧 故障排除

如果遇到问题，尝试：

```bash
# 完全重置环境
npm run db:pharos-test:reset

# 查看日志
docker compose -f docker-compose.pharos-test.yml logs
```

---

📖 **详细文档**: 查看 `docs/PHAROS_TEST_DATABASE.md` 获取完整说明

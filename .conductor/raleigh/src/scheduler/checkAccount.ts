import { JettonMaster, JettonWallet, TonClient, Transaction } from '@ton/ton';
import { AccountSubscriptionService } from './account-subscription.service';
import { Address, beginCell, Cell, OpenedContract } from '@ton/core';
import { connectDB, sequelize } from '../config/db';
import { UsdtTransaction } from '../types/usdt-transaction';
import { recordWalletHistory } from '../services/walletHistoryService';
import { isUUID, calculateUsdFromUsdt } from '../helpers/common-helpers';
import { safeAwait, safeAwaitWithCleanup, withTransaction } from '../helpers/error-handler';
import * as fs from 'fs';
import * as path from 'path';
import { UsdtDepositHistory, UserWallet, MonitorWalletAddress, JettonConfig, AccountSubscriptionState } from '../models/index';

// 配置
const CONFIG = {
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
  LOG_DIR: path.join(__dirname, '../logs'),
  MONITOR_INTERVAL: 10000,
  MAX_CONSECUTIVE_ERRORS: 5,
  INITIAL_RETRY_DELAY: 3000,
  MAX_RETRY_DELAY: 30000,
  API_ENDPOINT: "https://testnet.toncenter.com/api/v2/jsonRPC",
  API_KEY: "fe34fcaf400473152f6ec07afc29d73a6c94bdc787c2878a837b1b3550c5d6f3",
};

// 日志记录函数
class Logger {
  private static ensureLogDir(): void {
    // 确保日志目录存在
    if (!fs.existsSync(CONFIG.LOG_DIR)) {
      fs.mkdirSync(CONFIG.LOG_DIR, { recursive: true });
    }
  }

  public static transaction(txHash: string, fromAddress: string | undefined, type: string = 'general'): void {
    this.ensureLogDir();
    const logFile = path.join(CONFIG.LOG_DIR, `${type}-transaction.log`);
    const logMessage = `[${new Date().toISOString()}] Transaction Hash: ${txHash}, From Address: ${fromAddress}\n`;
    
    try {
      fs.appendFileSync(logFile, logMessage);
      console.log(`[LOG] 交易记录已写入 ${type} 日志: ${txHash}`);
    } catch (error) {
      console.error('[ERROR] 写入日志文件失败:', error);
    }
  }

  public static info(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = data 
      ? `[INFO][${timestamp}] ${message} ${JSON.stringify(data)}`
      : `[INFO][${timestamp}] ${message}`;
    console.log(logMessage);
  }

  public static error(message: string, error?: any): void {
    const timestamp = new Date().toISOString();
    const errorMessage = error instanceof Error ? error.message : error;
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    console.error(`[ERROR][${timestamp}] ${message}`, errorMessage);
    if (errorStack) {
      console.error(`[STACK][${timestamp}]`, errorStack);
    }
  }

  public static warn(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = data 
      ? `[WARN][${timestamp}] ${message} ${JSON.stringify(data)}`
      : `[WARN][${timestamp}] ${message}`;
    console.warn(logMessage);
  }

  public static debug(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = data 
      ? `[DEBUG][${timestamp}] ${message} ${JSON.stringify(data)}`
      : `[DEBUG][${timestamp}] ${message}`;
    console.debug(logMessage);
  }
}


export async function retry<T>(fn: () => Promise<T>, options: { retries: number, delay: number }): Promise<T> {
    let lastError: Error | undefined;
    for (let i = 0; i <= options.retries; i++) {
        try {
            return await fn();
        } catch (e) {
            if (e instanceof Error) {
                lastError = e;
                Logger.error(`尝试第${i + 1}次失败:`, e);
            } else {
                lastError = new Error('未知错误');
                Logger.error(`尝试第${i + 1}次失败: 未知错误`);
            }
            if (i < options.retries) {
                await new Promise(resolve => setTimeout(resolve, options.delay));
            }
        }
    }
    throw lastError || new Error('所有重试都失败了');
}

// 添加新的接口定义，用于返回更完整的交易信息
interface JettonTransaction {
  txHash: string;
  amount: string;
  from: string;
  comment: string;
  jettonName: string;
  timestamp: any;
}

export async function tryProcessJetton(orderId: string): Promise<JettonTransaction[]> {
    try {
        // 连接数据库
        Logger.info(`开始处理Jetton交易，订单ID: ${orderId}`);
        await safeAwait(
            () => connectDB(),
            '连接数据库失败'
        );
        
        const client = new TonClient({
            endpoint: CONFIG.API_ENDPOINT,
            apiKey: CONFIG.API_KEY,
        });

        interface JettonInfo {
            address: string;
            decimals: number;
        }

        interface Jettons {
            jettonMinter: OpenedContract<JettonMaster>,
            jettonWalletAddress: Address,
            jettonWallet: OpenedContract<JettonWallet>
        }
        
        // 从数据库获取监控钱包地址
        const monitorAddress = await MonitorWalletAddress.findOne({
            where: { isActive: true }
        });
        
        if (!monitorAddress) {
            throw new Error('未找到有效的监控钱包地址');
        }
        
        const MY_WALLET_ADDRESS = monitorAddress.walletAddress;
        Logger.info(`使用监控钱包地址: ${MY_WALLET_ADDRESS}`);
        
        // 从数据库获取Jetton配置
        const jettonConfigs = await JettonConfig.findAll({
            where: { isActive: true }
        });
        
        if (jettonConfigs.length === 0) {
            throw new Error('未找到有效的Jetton配置');
        }
        
        const JETTONS_INFO: Record<string, JettonInfo> = {};
        
        jettonConfigs.forEach(config => {
            JETTONS_INFO[config.name] = {
                address: config.address,
                decimals: config.decimals
            };
        });
        Logger.info(`加载了 ${Object.keys(JETTONS_INFO).length} 个Jetton配置`);
        
        const jettons: Record<string, Jettons> = {};

        const prepare = async () => {
            try {
                for (const name in JETTONS_INFO) {
                    try {
                        const info = JETTONS_INFO[name];
                        const jettonMaster = client.open(JettonMaster.create(Address.parse(info.address)));
                        const userAddress = Address.parse(MY_WALLET_ADDRESS);
                        const jettonUserAddress = await jettonMaster.getWalletAddress(userAddress);
                        Logger.debug(`Jetton钱包地址 ${name}: ${jettonUserAddress.toString()}`);

                        const jettonWallet = client.open(JettonWallet.create(jettonUserAddress));

                        try {
                            const jettonData = await client.runMethod(jettonUserAddress, "get_wallet_data");
                            jettonData.stack.pop(); //skip balance
                            jettonData.stack.pop(); //skip owner address
                            const adminAddress = jettonData.stack.readAddress();

                            if (adminAddress.toString() !== (Address.parse(info.address)).toString()) {
                                Logger.warn(`Jetton铸造者地址不匹配配置: ${name}`);
                                continue;
                            }

                            jettons[name] = {
                                jettonMinter: jettonMaster,
                                jettonWalletAddress: jettonUserAddress,
                                jettonWallet: jettonWallet
                            };
                        } catch (runMethodError) {
                            Logger.error(`获取${name}钱包数据失败:`, runMethodError);
                            continue;
                        }
                    } catch (jettonError) {
                        Logger.error(`处理${name} Jetton时出错:`, jettonError);
                        continue;
                    }
                }
            } catch (prepareError) {
                Logger.error(`准备Jetton钱包失败:`, prepareError);
                return {};
            }
            Logger.info(`成功准备了 ${Object.keys(jettons).length} 个Jetton钱包`);
            return jettons;
        }

        const jettonWalletAddressToJettonName = (jettonWalletAddress: Address) => {
            try {
                const jettonWalletAddressString = jettonWalletAddress.toString();
                for (const name in jettons) {
                    const jetton = jettons[name];
                    if (jetton.jettonWallet.address.toString() === jettonWalletAddressString) {
                        return name;
                    }
                }
                return null;
            } catch (error) {
                Logger.error(`Jetton地址转换为名称失败:`, error);
                return null;
            }
        }

        /**
         * 从数据库获取上次处理的lastIndexedLt
         */
        const getLastIndexedLt = async (): Promise<string | undefined> => {
            try {
                const state = await AccountSubscriptionState.findOne({
                    where: { accountAddress: MY_WALLET_ADDRESS }
                });
                return state?.lastIndexedLt;
            } catch (error) {
                Logger.error(`获取lastIndexedLt失败:`, error);
                throw error;
            }
        }

        /**
         * 将lastIndexedLt保存到数据库
         */
        const saveLastIndexedLt = async (lastIndexedLt: string): Promise<void> => {
            try {
                await AccountSubscriptionState.upsert({
                    accountAddress: MY_WALLET_ADDRESS,
                    lastIndexedLt
                });
                Logger.info(`保存lastIndexedLt成功: ${lastIndexedLt}`);
            } catch (error) {
                Logger.error(`保存lastIndexedLt失败:`, error);
                throw error;
            }
        }

        /**
         * 获取交易批次
         */
        const getTransactionsBatch = async (toLt?: string, lt?: string, hash?: string) => {
            const client = new TonClient({
                endpoint: CONFIG.API_ENDPOINT,
                apiKey: CONFIG.API_KEY,
            });
            
            const myAddress = Address.parse(MY_WALLET_ADDRESS);
            Logger.debug(`获取交易批次: toLt=${toLt}, lt=${lt}`);
            
            const transactions = await retry(() => client.getTransactions(myAddress, {
                lt,
                limit: 100,
                hash,
                to_lt: toLt,
                inclusive: false,
                archival: true,
            }), { retries: CONFIG.RETRY_COUNT, delay: CONFIG.RETRY_DELAY });
            
            if (transactions.length === 0) {
                Logger.debug('获取交易批次: 无交易');
                return { hasMore: false, transactions };
            }

            const lastTransaction = transactions.at(-1)!;
            Logger.info(`获取交易批次: 获取到 ${transactions.length} 笔交易`);

            return {
                hasMore: true,
                transactions,
                lt: lastTransaction.lt.toString(),
                hash: lastTransaction.hash().toString('base64'),
            };
        }

        // 订阅 - 循环查询交易
        const Subscription = async (): Promise<Transaction[]> => {
            try {
                let iterationStartLt: string = '';
                let hasMore = true;
                let lt: string | undefined;
                let hash: string | undefined;
                let allTransactions: Transaction[] = [];
                
                // 从数据库获取lastIndexedLt
                const lastIndexedLt = await safeAwait(
                    () => getLastIndexedLt(),
                    '获取lastIndexedLt失败'
                );
                Logger.info(`开始获取交易，lastIndexedLt: ${lastIndexedLt || '无'}`);

                // 从最新的交易开始获取，直到lastIndexedLt（如果有的话）
                while (hasMore) {
                    const res = await safeAwait(
                        () => getTransactionsBatch(lastIndexedLt, lt, hash),
                        '获取交易批次失败',
                        { hasMore: false, transactions: [], lt: undefined, hash: undefined }
                    );
                    
                    if (!res) {
                        hasMore = false;
                        continue;
                    }
                    
                    hasMore = res.hasMore;
                    lt = res.lt;
                    hash = res.hash;

                    if (res.transactions.length > 0) {
                        if (!iterationStartLt) {
                            // 存储第一个获取的交易的lt，在迭代结束时保存到数据库以防止重复获取交易
                            iterationStartLt = res.transactions[0].lt.toString();
                        }

                        // 将获取的交易添加到结果数组中
                        allTransactions = [...allTransactions, ...res.transactions];
                    }
                }

                Logger.info(`总共获取到 ${allTransactions.length} 条交易`);
                
                // 如果有新交易，更新lastIndexedLt
                if (iterationStartLt) {
                    await safeAwait(
                        () => saveLastIndexedLt(iterationStartLt),
                        '保存lastIndexedLt失败'
                    );
                }

                return allTransactions;
            } catch (error) {
                Logger.error(`获取交易失败:`, error);
                return [];
            }
        }

        //@ts-ignore
        return retry(async () => {
            try {
                const preparedJettons = await prepare();
                const Transactions = await Subscription();
                const validTransactions: JettonTransaction[] = [];

                for (const tx of Transactions) {
                    try {
                        const sourceAddress = tx.inMessage?.info.src;
                        if (!sourceAddress) {
                            // external message - not related to jettons
                            continue;
                        }

                        if (!(sourceAddress instanceof Address)) {
                            continue;
                        }

                        const in_msg = tx.inMessage;

                        if (in_msg?.info.type !== 'internal') {
                            // external message - not related to jettons
                            continue;
                        }
                        
                        // jetton master contract address check
                        const jettonName = jettonWalletAddressToJettonName(sourceAddress);
                        if (!jettonName) {
                            Logger.debug(`未知Jetton: ${tx.hash().toString('hex')}`);
                            continue;
                        }

                        if (tx.inMessage === undefined || tx.inMessage?.body.hash().equals(new Cell().hash())) {
                            // no in_msg or in_msg body
                            continue;
                        }

                        const msgBody = tx.inMessage;
                        const sender = tx.inMessage?.info.src;
                        const originalBody = tx.inMessage?.body.beginParse();
                        let body = originalBody?.clone();
                        const op = body?.loadUint(32);
                        if (!(op == 0x7362d09c)) {
                            continue; // op != transfer_notification
                        }

                        Logger.debug(`操作码检查通过: ${tx.hash().toString('hex')}`);

                        const queryId = body?.skip(64);
                        const amount = body?.loadCoins();
                        const from = body?.loadAddress();
                        const maybeRef = body?.loadBit();
                        const payload = maybeRef ? body?.loadRef().beginParse() : body;
                        const payloadOp = payload?.loadUint(32);
                        if (!(payloadOp == 0)) {
                            Logger.debug('没有文本评论: transfer_notification');
                            continue;
                        }

                        const comment = payload?.loadStringTail();
                        if (!comment || !isUUID(comment)) {
                            continue;
                        }

                        Logger.info(`发现 ${jettonName} Jetton 充值 ${amount?.toString()} 单位，备注: "${comment}"`);
                        const txHash = tx.hash().toString('hex');
                        
                        // 添加完整的交易信息到结果中
                        validTransactions.push({
                            txHash,
                            amount: amount?.toString() || '0',
                            from: from?.toString() || '',
                            comment: comment || '',
                            jettonName,
                            // @ts-ignore
                            timestamp: tx?.inMessage?.info?.createdAt,
                        });
                    } catch (txError) {
                        Logger.error(`处理交易时出错:`, txError);
                        // 继续处理下一个交易
                        continue;
                    }
                }
                
                if (validTransactions.length === 0) {
                    Logger.info('未找到符合条件的交易');
                } else {
                    Logger.info(`找到 ${validTransactions.length} 条符合条件的交易`, validTransactions.map(tx => tx.txHash));
                }
                
                return validTransactions;
            } catch (error) {
                Logger.error(`处理交易批次时出错:`, error);
                throw error; // 重新抛出错误以便retry函数可以捕获
            }
        }, { retries: CONFIG.RETRY_COUNT, delay: CONFIG.RETRY_DELAY });
    } catch (outerError) {
        Logger.error(`tryProcessJetton函数执行失败:`, outerError);
        throw outerError; // 重新抛出错误以便调用者可以处理
    }
}

export class JettonMonitorService {
    private client: TonClient;
    private isProcessing: boolean = false;
    private isRunning: boolean = false;
    private intervalId: NodeJS.Timeout | null = null;
    private retryDelay: number = CONFIG.INITIAL_RETRY_DELAY;
    private maxRetryDelay: number = CONFIG.MAX_RETRY_DELAY;
    private consecutiveErrors: number = 0;
    private maxConsecutiveErrors: number = CONFIG.MAX_CONSECUTIVE_ERRORS;
    private transactionCache: Set<string> = new Set(); // 交易缓存，防止重复处理

    constructor(apiKey: string, endpoint: string) {
        this.client = new TonClient({
            endpoint: endpoint,
            apiKey: apiKey,
        });
        Logger.info(`初始化 JettonMonitorService: ${endpoint}`);
    }

    /**
     * 启动Jetton监控服务
     * @param intervalMs 监控间隔，默认10秒
     * @returns 监控服务实例，用于链式调用
     */
    start(intervalMs: number = CONFIG.MONITOR_INTERVAL): JettonMonitorService {
        if (this.isRunning) {
            Logger.warn('监控服务已在运行中');
            return this;
        }

        this.isRunning = true;
        
        // 初始化时执行一次
        this.processJettonTransactions();
        
        // 设置定时器，定期执行
        this.intervalId = setInterval(() => this.processJettonTransactions(), intervalMs);
        Logger.info(`Jetton监控服务已启动，监控间隔: ${intervalMs}ms`);
        
        return this;
    }

    /**
     * 停止Jetton监控服务
     */
    stop(): void {
        if (!this.isRunning || !this.intervalId) {
            Logger.warn('监控服务未运行');
            return;
        }

        clearInterval(this.intervalId);
        this.intervalId = null;
        this.isRunning = false;
        Logger.info('Jetton监控服务已停止');
    }

    /**
     * 处理有效的交易数据，更新用户余额并记录充值历史
     * @param senderAddress 发送方地址
     * @param amount USDT金额
     * @param jettonName Jetton类型
     * @param comment 交易备注（通常是order ID）
     * @returns 处理是否成功
     */
    private async processValidTransaction(
        senderAddress: string,
        amount: string,
        jettonName: string,
        comment: string,
        txHash: string = '', // 可选参数，仅用于日志记录，不是必须的
        timestamp: any = null // 可选参数，仅用于日志记录，不是必须的
    ): Promise<boolean> {
        try {
            Logger.info(`处理交易: ${txHash}`, { senderAddress, amount, jettonName, comment });
            
            // 检查交易是否已处理
            if (this.transactionCache.has(txHash)) {
                Logger.info(`交易已处理，跳过: ${txHash}`);
                return true;
            }
            
            // 连接数据库（如果还没连接）
            await connectDB();

            // 在数据库中检查交易是否已处理
            const existingTransaction = await UsdtDepositHistory.findOne({
                where: { transactionHash: txHash }
            });
            
            if (existingTransaction) {
                Logger.info(`交易在数据库中已存在，跳过: ${txHash}`);
                this.transactionCache.add(txHash);
                return true;
            }
            
            // 在UserWallet表中查找对应的钱包地址
            const userWallet = await UserWallet.findOne({
                where: { parsedWalletAddress: senderAddress }
            });
            
            if (!userWallet) {
                Logger.warn(`未找到匹配的用户钱包地址: ${senderAddress}`);
                return false;
            }
            
            Logger.info(`找到匹配的用户钱包, 用户ID: ${userWallet.userId}`, {walletAddress: senderAddress});
            
            const amountInNano = BigInt(amount);
            const amountInUsd = calculateUsdFromUsdt(amountInNano);
           
            // 使用事务确保数据一致性
            await sequelize.transaction(async (transaction) => {
                // 创建充值记录
                const depositHistory = await UsdtDepositHistory.create({
                    walletId: userWallet.id,
                    userId: userWallet.userId,
                    amount: amountInUsd,
                    transactionHash: txHash,
                    walletAddress: senderAddress,
                    status: 'completed',
                    comment: comment,
                    timestamp: timestamp,
                    jettonType: jettonName
                }, { transaction });
                
                Logger.info(`创建充值记录成功, ID: ${depositHistory.id}`);
                
                // 更新用户钱包余额
                await userWallet.increment('usd', { by: amountInUsd, transaction });
                
                // 记录钱包历史
                await recordWalletHistory(
                    userWallet.userId,
                    userWallet.id,
                    "usd",
                    amountInUsd,
                    "Deposit",
                    "in",
                    "deposit",
                    "usd",
                    `充值`,
                    `充值`,
                    transaction
                );
                
                Logger.info(`用户钱包余额已更新, 增加: ${amountInUsd} USD`);
            });
            
            // 将交易添加到缓存中
            this.transactionCache.add(txHash);
            
            // 如果缓存过大，清理旧数据
            if (this.transactionCache.size > 1000) {
                this.cleanTransactionCache();
            }
            
            return true;
        } catch (error) {
            Logger.error(`处理交易时出错: ${txHash}`, error);
            return false;
        }
    }
    
    /**
     * 清理交易缓存
     */
    private cleanTransactionCache(): void {
        // 只保留最新的500个交易记录
        if (this.transactionCache.size > 500) {
            const transactions = Array.from(this.transactionCache);
            const keepTransactions = transactions.slice(transactions.length - 500);
            this.transactionCache = new Set(keepTransactions);
            Logger.info(`清理交易缓存，当前缓存大小: ${this.transactionCache.size}`);
        }
    }

    /**
     * 处理Jetton交易，增加了自动重试和错误恢复机制
     */
    private async processJettonTransactions(): Promise<void> {
        // 防止多个实例同时运行
        if (this.isProcessing) {
            Logger.debug('上一次处理尚未完成，跳过本次执行');
            return;
        }
        
        this.isProcessing = true;
        Logger.info(`开始执行Jetton交易检查: ${new Date().toISOString()}`);
        
        try {
            // tryProcessJetton 现在会返回更完整的交易信息
            const transactions = await tryProcessJetton('monitor');
            Logger.info(`本次检查完成，发现${transactions.length}笔有效交易`);
            
            if (transactions.length > 0) {
                // 连接到数据库
                await connectDB();
                
                // 处理每个交易，直接使用返回的信息
                for (const tx of transactions) {
                    try {
                        // 直接使用tx中的信息，不需要再查询交易详情
                        const success = await this.processValidTransaction(
                            tx.from,
                            tx.amount,
                            tx.jettonName,
                            tx.comment,
                            tx.txHash,
                            tx.timestamp
                        );
                        
                        if (success) {
                            Logger.info(`交易处理成功: ${tx.txHash}`);
                        } else {
                            Logger.warn(`交易处理失败: ${tx.txHash}`);
                        }
                    } catch (txError) {
                        Logger.error(`处理交易 ${tx.txHash} 时出错:`, txError);
                        // 继续处理下一个交易
                        continue;
                    }
                }
            }
            
            // 成功完成，重置错误计数和重试延迟
            this.consecutiveErrors = 0;
            this.retryDelay = CONFIG.INITIAL_RETRY_DELAY;
        } catch (error) {
            this.consecutiveErrors++;
            Logger.error(`执行Jetton交易检查时出错(错误次数: ${this.consecutiveErrors}):`, error);
            
            // 检查是否需要实施指数退避重试策略
            if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
                Logger.warn(`连续错误次数达到${this.maxConsecutiveErrors}次，暂时停止监控并尝试重新启动...`);
                
                // 临时停止当前监控周期
                if (this.intervalId) {
                    clearInterval(this.intervalId);
                    this.intervalId = null;
                }
                
                // 指数退避策略计算新的延迟时间
                this.retryDelay = Math.min(this.retryDelay * 2, this.maxRetryDelay);
                Logger.info(`将在${this.retryDelay}ms后尝试重新启动监控...`);
                
                // 延迟后重新启动监控
                setTimeout(() => {
                    if (!this.isRunning) {
                        Logger.info('重新启动监控服务...');
                        this.start();
                    }
                }, this.retryDelay);
            }
        } finally {
            this.isProcessing = false;
            Logger.info(`完成Jetton交易检查: ${new Date().toISOString()}`);
        }
    }

    /**
     * 设置最大重试延迟时间
     * @param maxDelay 最大延迟时间(毫秒)
     * @returns 监控服务实例，用于链式调用
     */
    setMaxRetryDelay(maxDelay: number): JettonMonitorService {
        this.maxRetryDelay = maxDelay;
        Logger.info(`设置最大重试延迟: ${maxDelay}ms`);
        return this;
    }

    /**
     * 设置最大连续错误次数
     * @param maxErrors 最大连续错误次数
     * @returns 监控服务实例，用于链式调用
     */
    setMaxConsecutiveErrors(maxErrors: number): JettonMonitorService {
        this.maxConsecutiveErrors = maxErrors;
        Logger.info(`设置最大连续错误次数: ${maxErrors}`);
        return this;
    }

    /**
     * 检查监控服务是否正在运行
     * @returns 如果监控服务正在运行，则返回true
     */
    isActive(): boolean {
        return this.isRunning;
    }
}

// 修改最后的调用部分，使用新的监控服务
if (require.main === module) {
    Logger.info('正在启动Jetton监控服务...');
    
    // 创建并启动监控服务
    const monitor = new JettonMonitorService(
        CONFIG.API_KEY,
        CONFIG.API_ENDPOINT
    )
    .setMaxConsecutiveErrors(CONFIG.MAX_CONSECUTIVE_ERRORS)
    .setMaxRetryDelay(60000) // 最大重试延迟1分钟
    .start(CONFIG.MONITOR_INTERVAL);
    
    // 处理进程退出信号，确保优雅关闭
    process.on('SIGINT', () => {
        Logger.info('接收到中断信号，正在停止监控服务...');
        monitor.stop();
        process.exit(0);
    });
    
    process.on('SIGTERM', () => {
        Logger.info('接收到终止信号，正在停止监控服务...');
        monitor.stop();
        process.exit(0);
    });
    
    // 添加未捕获异常处理，防止进程崩溃
    process.on('uncaughtException', (error) => {
        Logger.error('未捕获的异常:', error);
        Logger.info('监控服务将继续运行');
    });
    
    // 添加未处理的Promise拒绝处理
    process.on('unhandledRejection', (reason, promise) => {
        Logger.error('未处理的Promise拒绝:', reason);
        Logger.info('监控服务将继续运行');
    });
    
    Logger.info('Jetton充值监控服务已启动，按Ctrl+C停止');
} else {
    // 如果作为模块导入，则不自动启动监控
    Logger.info('Jetton模块已加载，但未启动监控服务');
}
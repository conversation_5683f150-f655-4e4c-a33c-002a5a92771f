import { Request, Response } from 'express';
import * as XLSX from 'xlsx';
import { MyRequest } from '../types/customRequest';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { tFromRequest } from '../i18n';
import { FarmConfigService } from '../services/farmConfigService';
import { FarmConfig } from '../models/FarmConfig';

class ExcelUploadController {
  /**
   * 上传并读取Excel文件
   */
  public async uploadAndReadExcel(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse(tFromRequest(req, 'errors.noFileUploaded') || '请上传Excel文件'));
        return;
      }

      // 检查文件类型
      const allowedMimeTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'application/vnd.ms-excel', // .xls
      ];

      if (!allowedMimeTypes.includes(req.file.mimetype)) {
        res.status(400).json(errorResponse('请上传有效的Excel文件 (.xlsx 或 .xls)'));
        return;
      }

      // 读取Excel文件
      const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
      
      // 获取所有工作表名称
      const sheetNames = workbook.SheetNames;
      
      // 读取所有工作表的数据
      const sheetsData: { [key: string]: any[] } = {};
      
      sheetNames.forEach(sheetName => {
        const worksheet = workbook.Sheets[sheetName];
        // 将工作表转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
          header: 1, // 使用数组格式，第一行作为索引0
          defval: '' // 空单元格的默认值
        });
        sheetsData[sheetName] = jsonData;
      });

      // 特殊处理：如果是区域升级相关的Excel文件，进行数据解析
      const parsedData = this.parseRegionUpgradeData(sheetsData);

      res.json(successResponse({
        fileName: req.file.originalname,
        fileSize: req.file.size,
        sheetNames: sheetNames,
        sheetsData: sheetsData,
        parsedData: parsedData,
        message: 'Excel文件读取成功'
      }));

    } catch (error: any) {
      console.error('Excel文件处理失败:', error);
      res.status(500).json(errorResponse(error.message || '文件处理失败'));
    }
  }

  /**
   * 解析区域升级数据
   */
  private parseRegionUpgradeData(sheetsData: { [key: string]: any[] }): any {
    const result: any = {
      regions: [],
      summary: {}
    };

    // 遍历所有工作表
    Object.keys(sheetsData).forEach(sheetName => {
      const sheetData = sheetsData[sheetName];
      
      if (sheetData.length === 0) return;

      // 假设第一行是标题行
      const headers = sheetData[0] as string[];
      const dataRows = sheetData.slice(1);

      // 解析数据行
      const regionData = dataRows.map((row: any[], index: number) => {
        const rowData: any = {};
        headers.forEach((header, colIndex) => {
          if (header && header.trim) {
            rowData[header.trim()] = row[colIndex] || '';
          }
        });
        rowData._rowIndex = index + 2; // Excel行号（从2开始，因为第1行是标题）
        return rowData;
      }).filter(row => {
        // 过滤掉完全空的行
        return Object.values(row).some(value => value !== '' && value !== null && value !== undefined);
      });

      result.regions.push({
        sheetName: sheetName,
        headers: headers.filter(h => h && h.trim),
        data: regionData,
        totalRows: regionData.length
      });
    });

    // 生成汇总信息
    result.summary = {
      totalSheets: Object.keys(sheetsData).length,
      totalRegions: result.regions.reduce((sum: number, region: any) => sum + region.totalRows, 0),
      processedAt: new Date().toISOString()
    };

    return result;
  }

  /**
   * 获取Excel文件模板
   */
  public async getExcelTemplate(req: MyRequest, res: Response): Promise<void> {
    try {
      // 创建一个示例Excel模板
      const workbook = XLSX.utils.book_new();
      
      // 创建区域升级模板数据
      const templateData = [
        ['区域ID', '区域名称', '当前等级', '升级费用', '升级后效果', '备注'],
        ['1', '农场区域1', '1', '100', '产量+20%', '初始区域'],
        ['2', '农场区域2', '1', '200', '产量+25%', ''],
        ['3', '配送区域1', '1', '150', '速度+15%', ''],
        ['4', '配送区域2', '1', '300', '速度+20%', '']
      ];

      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(templateData);
      
      // 设置列宽
      worksheet['!cols'] = [
        { width: 10 }, // 区域ID
        { width: 15 }, // 区域名称
        { width: 10 }, // 当前等级
        { width: 12 }, // 升级费用
        { width: 15 }, // 升级后效果
        { width: 20 }  // 备注
      ];

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '区域升级配置');

      // 生成Excel文件缓冲区
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename="region_upgrade_template.xlsx"');
      
      // 发送文件
      res.send(excelBuffer);

    } catch (error: any) {
      console.error('生成Excel模板失败:', error);
      res.status(500).json(errorResponse(error.message || '模板生成失败'));
    }
  }

  /**
   * 批量处理区域升级数据
   */
  public async processBatchUpgrade(req: MyRequest, res: Response): Promise<void> {
    try {
      const { upgradeData } = req.body;
      
      if (!upgradeData || !Array.isArray(upgradeData)) {
        res.status(400).json(errorResponse('请提供有效的升级数据'));
        return;
      }

      // 这里可以添加具体的业务逻辑
      // 例如：验证数据、更新数据库、计算升级效果等
      
      const processedResults = upgradeData.map((item: any, index: number) => {
        return {
          index: index + 1,
          regionId: item.regionId || item['区域ID'],
          regionName: item.regionName || item['区域名称'],
          currentLevel: item.currentLevel || item['当前等级'],
          upgradeCost: item.upgradeCost || item['升级费用'],
          effect: item.effect || item['升级后效果'],
          status: 'processed', // 可以是 'processed', 'error', 'skipped'
          message: '处理成功'
        };
      });

      res.json(successResponse({
        processedCount: processedResults.length,
        results: processedResults,
        message: `成功处理 ${processedResults.length} 条区域升级数据`
      }));

    } catch (error: any) {
      console.error('批量处理失败:', error);
      res.status(500).json(errorResponse(error.message || '批量处理失败'));
    }
  }

  /**
   * 上传农场配置Excel文件
   */
  public async uploadFarmConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse('请上传Excel文件'));
        return;
      }

      const { versionName, description } = req.body;
      const createdBy = req.user?.walletAddress || 'system';

      if (!versionName) {
        res.status(400).json(errorResponse('请提供版本名称'));
        return;
      }

      // 上传配置
      const version = await FarmConfigService.uploadNewConfig(
        req.file.buffer,
        versionName,
        description,
        createdBy
      );

      res.json(successResponse({
        version,
        versionName,
        description,
        uploadedBy: createdBy,
        message: '农场配置上传成功'
      }));

    } catch (error: any) {
      console.error('农场配置上传失败:', error);
      res.status(500).json(errorResponse(error.message || '农场配置上传失败'));
    }
  }

  /**
   * 获取农场配置模板
   */
  public async getFarmConfigTemplate(req: MyRequest, res: Response): Promise<void> {
    try {
      // 创建农场配置模板
      const workbook = XLSX.utils.book_new();

      // 创建模板数据（基于农场升级配置表的结构）
      const templateData = [
        ['等级', '每秒产出计算用值', '奶牛数量', '生产速度百分比', '牛奶生产', '升级花费', '离线产出'],
        [0, 0, 0, 0, 0, 13096, 0],
        [1, 182, 1, 100, 60.63, 20043, 90.94],
        [2, 232, 1, 100, 77.33, 28583, 115.99],
        [3, 276, 2, 110, 95.06, 39214, 137.84],
        [4, 315, 2, 110, 108.68, 52496, 157.59],
        // 添加更多示例数据...
      ];

      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(templateData);

      // 设置列宽
      worksheet['!cols'] = [
        { width: 8 },  // 等级
        { width: 18 }, // 每秒产出计算用值
        { width: 12 }, // 奶牛数量
        { width: 16 }, // 生产速度百分比
        { width: 12 }, // 牛奶生产
        { width: 12 }, // 升级花费
        { width: 12 }  // 离线产出
      ];

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '农场配置');

      // 生成Excel文件缓冲区
      const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      // 设置响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename="farm_config_template.xlsx"');

      // 发送文件
      res.send(excelBuffer);

    } catch (error: any) {
      console.error('生成农场配置模板失败:', error);
      res.status(500).json(errorResponse(error.message || '模板生成失败'));
    }
  }

  /**
   * 验证农场配置Excel文件
   */
  public async validateFarmConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      // 检查是否有文件上传
      if (!req.file) {
        res.status(400).json(errorResponse('请上传Excel文件'));
        return;
      }

      // 读取Excel文件
      const workbook = XLSX.read(req.file.buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // 转换为JSON格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: ''
      });

      // 解析农场配置数据
      const parsedData = this.parseFarmConfigData(jsonData);

      // 验证数据
      const validation = FarmConfig.validateConfigData(parsedData.configs);

      res.json(successResponse({
        fileName: req.file.originalname,
        fileSize: req.file.size,
        isValid: validation.isValid,
        errors: validation.errors,
        configCount: parsedData.configs.length,
        sampleConfigs: parsedData.configs.slice(0, 5),
        summary: parsedData.summary,
        message: validation.isValid ? '农场配置验证通过' : '农场配置验证失败'
      }));

    } catch (error: any) {
      console.error('农场配置验证失败:', error);
      res.status(500).json(errorResponse(error.message || '配置验证失败'));
    }
  }

  /**
   * 解析农场配置数据
   */
  private parseFarmConfigData(jsonData: any[]): { configs: any[], summary: any } {
    if (jsonData.length === 0) {
      return { configs: [], summary: {} };
    }

    // 假设第一行是标题行
    const headers = jsonData[0] as string[];
    const dataRows = jsonData.slice(1);

    // 解析数据行
    const configs = (dataRows as any[][]).map((row: any[], index: number) => {
      const config: any = {};

      headers.forEach((header, colIndex) => {
        const value = row[colIndex];

        switch (header.toLowerCase().trim()) {
          case 'grade':
          case '等级':
            config.grade = parseInt(value) || 0;
            break;
          case 'production':
          case '每秒产出计算用值':
            config.production = parseInt(value) || 0;
            break;
          case 'cow':
          case '奶牛数量':
            config.cow = parseInt(value) || 0;
            break;
          case 'speed':
          case '生产速度百分比':
            config.speed = parseInt(value) || 0;
            break;
          case 'milk':
          case '牛奶生产':
            config.milk = parseFloat(value) || 0;
            break;
          case 'cost':
          case '升级花费':
            config.cost = parseInt(value) || 0;
            break;
          case 'offline':
          case '离线产出':
            config.offline = parseFloat(value) || 0;
            break;
        }
      });

      config._rowIndex = index + 2; // Excel行号（从2开始，因为第1行是标题）
      return config;
    }).filter(config => config.grade !== undefined);

    // 生成汇总信息
    const summary = {
      totalConfigs: configs.length,
      gradeRange: configs.length > 0 ? {
        min: Math.min(...configs.map(c => c.grade)),
        max: Math.max(...configs.map(c => c.grade))
      } : null,
      processedAt: new Date().toISOString()
    };

    return { configs, summary };
  }
}

export default new ExcelUploadController();

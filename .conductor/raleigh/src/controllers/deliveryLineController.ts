import { Response } from 'express';
import deliveryLineService from '../services/deliveryLineService';
import { DeliveryLineConfig } from '../models/DeliveryLineConfig';
import { MyRequest } from '../types/customRequest';
import { responseUtil } from '../utils/responseUtil';
import { sequelize } from '../config/db';
import * as XLSX from 'xlsx';
import * as fs from 'fs';

class DeliveryLineController {
  // 获取用户的出货线
  public async getUserDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      // 初始化并获取用户的出货线
      const deliveryLine = await deliveryLineService.getUserDeliveryLine(walletId);
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 升级出货线
  public async upgradeDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      // 升级出货线
      const deliveryLine = await deliveryLineService.upgradeDeliveryLine(walletId);
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 添加牛奶到出货线
  public async addMilkToDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { milkAmount } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!milkAmount || isNaN(Number(milkAmount)) || Number(milkAmount) <= 0) {
        responseUtil.error(res, '无效的牛奶数量', 400);
        return;
      }

      // 添加牛奶到出货线
      const deliveryLine = await deliveryLineService.addMilkToDeliveryLine(walletId, Number(milkAmount));
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 计算离线收益
  public async calculateOfflineEarnings(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { offlineTime } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!offlineTime || isNaN(Number(offlineTime)) || Number(offlineTime) < 0) {
        responseUtil.error(res, '无效的离线时间', 400);
        return;
      }

      // 计算离线收益
      const earnedGem = await deliveryLineService.calculateOfflineEarnings(walletId, Number(offlineTime));
      responseUtil.success(res, { earnedGem });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }
  
  // 出售牛奶方块换取宝石
  public async sellMilkBlocks(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { blockCount } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!blockCount || isNaN(Number(blockCount)) || Number(blockCount) <= 0) {
        responseUtil.error(res, '无效的方块数量', 400);
        return;
      }

      // 出售牛奶方块
      const { soldBlocks, earnedGems } = await deliveryLineService.sellMilkBlocks(walletId, Number(blockCount));
      responseUtil.success(res, { 
        soldBlocks,
        earnedGems,
        message: `成功出售 ${soldBlocks} 个牛奶方块，获得 ${earnedGems} 宝石`
      });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 获取所有流水线配置（管理接口）
  public async getAllConfigs(req: MyRequest, res: Response): Promise<void> {
    try {
      const configs = await DeliveryLineConfig.getAllConfigs();

      responseUtil.success(res, {
        configs: configs.map(config => ({
          level: config.grade,
          profit: config.profit,
          capacity: config.capacity,
          productionInterval: config.production_interval,
          deliverySpeedDisplay: config.delivery_speed_display,
          upgradeCost: config.upgrade_cost
        })),
        totalLevels: configs.length
      });
    } catch (error: any) {
      responseUtil.error(res, '获取配置失败: ' + error.message, 500);
    }
  }

  // 测试接口（无需认证）
  public async testGetConfigs(req: MyRequest, res: Response): Promise<void> {
    try {
      console.log('🧪 测试获取配置接口...');
      const configs = await DeliveryLineConfig.getAllConfigs();

      console.log(`✅ 成功查询到 ${configs.length} 个配置`);

      res.json({
        ok: true,
        message: '测试成功',
        data: {
          configs: configs.map(config => ({
            level: config.grade,
            profit: config.profit,
            capacity: config.capacity,
            productionInterval: config.production_interval,
            deliverySpeedDisplay: config.delivery_speed_display,
            upgradeCost: config.upgrade_cost
          })),
          totalLevels: configs.length
        }
      });
    } catch (error: any) {
      console.error('❌ 测试失败:', error);
      res.status(500).json({
        ok: false,
        message: '测试失败: ' + error.message,
        error: error.stack
      });
    }
  }

  // 上传流水线配置Excel表
  public async uploadDeliveryLineConfig(req: MyRequest, res: Response): Promise<void> {
    try {
      if (!req.file) {
        responseUtil.error(res, '未上传文件', 400);
        return;
      }

      const filePath = req.file.path;
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // 验证Excel数据格式
      interface ConfigData {
        grade: number;
        profit: number;
        capacity: number;
        production_interval: number;
        delivery_speed_display: number;
        upgrade_cost: number;
      }

      const configs: ConfigData[] = [];
      for (let i = 0; i < jsonData.length; i++) {
        const row = jsonData[i] as any;

        // 跳过标题行和说明行
        if (!row.grade || isNaN(row.grade) || row.grade < 1 || row.grade > 50) {
          continue;
        }

        configs.push({
          grade: parseInt(row.grade),
          profit: parseInt(row.profit),
          capacity: parseInt(row.capacity),
          production_interval: parseFloat(row.speed1),
          delivery_speed_display: parseInt(row.speed2),
          upgrade_cost: parseInt(row.cost)
        });
      }

      if (configs.length === 0) {
        responseUtil.error(res, 'Excel文件格式错误或无有效数据', 400);
        return;
      }

      // 验证必须有50个等级的完整配置
      if (configs.length !== 50) {
        responseUtil.error(res, `配置数量不正确，需要50个等级，实际得到${configs.length}个`, 400);
        return;
      }

      // 在事务中批量更新配置
      await sequelize.transaction(async (t) => {
        // 清空现有配置
        await DeliveryLineConfig.destroy({ where: {}, transaction: t });

        // 批量插入新配置
        await DeliveryLineConfig.bulkCreate(configs, { transaction: t });
      });

      // 删除上传的临时文件
      fs.unlinkSync(filePath);

      responseUtil.success(res, {
        message: '流水线配置上传成功',
        totalConfigs: configs.length,
        levels: `${configs[0].grade}-${configs[configs.length-1].grade}`,
        updatedAt: new Date()
      });

    } catch (error: any) {
      console.error('上传流水线配置失败:', error);

      // 清理临时文件
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      responseUtil.error(res, '上传失败: ' + error.message, 500);
    }
  }
}

export default new DeliveryLineController();
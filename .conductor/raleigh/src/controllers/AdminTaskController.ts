import { Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { TaskConfigManager } from '../services/TaskConfigManager';
import { ExcelConfigParser } from '../services/ExcelConfigParser';
import { NewTaskService } from '../services/NewTaskService';
import { TaskConfig } from '../models/TaskConfig';
import { TaskConfigVersion } from '../models/TaskConfigVersion';
import { TaskConfigLog } from '../models/TaskConfigLog';
import { successResponse, errorResponse } from '../utils/responseUtil';

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/task-configs');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const timestamp = Date.now();
    const ext = path.extname(file.originalname);
    cb(null, `task-config-${timestamp}${ext}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式 (.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

export class AdminTaskController {
  private configManager: TaskConfigManager;
  private parser: ExcelConfigParser;
  private taskService: NewTaskService;

  constructor() {
    this.configManager = new TaskConfigManager();
    this.parser = new ExcelConfigParser();
    this.taskService = new NewTaskService();
  }

  /**
   * 上传Excel配置文件
   * POST /api/admin/tasks/upload
   */
  public async uploadConfig(req: Request, res: Response): Promise<void> {
    try {
      // 使用multer中间件处理文件上传
      upload.single('configFile')(req, res, async (err) => {
        if (err) {
          res.status(400).json(errorResponse(err.message));
          return;
        }

        if (!req.file) {
          res.status(400).json(errorResponse('请选择要上传的Excel文件'));
          return;
        }

        try {
          const adminId = req.body.adminId || 'admin';
          const description = req.body.description;

          const result = await this.configManager.uploadConfigFromFile(
            req.file.path,
            adminId,
            description
          );

          // 清理上传的临时文件
          fs.unlinkSync(req.file.path);

          res.json(successResponse(result, '配置文件上传成功'));
        } catch (error: any) {
          // 清理上传的临时文件
          if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          res.status(400).json(errorResponse(error.message));
        }
      });
    } catch (error: any) {
      console.error('上传配置文件失败:', error);
      res.status(500).json(errorResponse('上传配置文件失败'));
    }
  }

  /**
   * 验证Excel配置文件
   * POST /api/admin/tasks/validate
   */
  public async validateConfig(req: Request, res: Response): Promise<void> {
    try {
      upload.single('configFile')(req, res, async (err) => {
        if (err) {
          res.status(400).json(errorResponse(err.message));
          return;
        }

        if (!req.file) {
          res.status(400).json(errorResponse('请选择要验证的Excel文件'));
          return;
        }

        try {
          const adminId = req.body.adminId || 'admin';

          const result = await this.configManager.validateConfigFile(
            req.file.path,
            adminId
          );

          // 清理上传的临时文件
          fs.unlinkSync(req.file.path);

          res.json(successResponse(result, '配置文件验证完成'));
        } catch (error: any) {
          // 清理上传的临时文件
          if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
          }
          res.status(400).json(errorResponse(error.message));
        }
      });
    } catch (error: any) {
      console.error('验证配置文件失败:', error);
      res.status(500).json(errorResponse('验证配置文件失败'));
    }
  }

  /**
   * 应用指定版本的配置
   * POST /api/admin/tasks/apply/:versionNumber
   */
  public async applyConfig(req: Request, res: Response): Promise<void> {
    try {
      const versionNumber = req.params.versionNumber;
      const adminId = req.body.adminId || 'admin';

      if (!versionNumber) {
        res.status(400).json(errorResponse('版本号不能为空'));
        return;
      }

      const result = await this.configManager.applyConfig(versionNumber, adminId);

      res.json(successResponse(result, '配置应用成功'));
    } catch (error: any) {
      console.error('应用配置失败:', error);
      res.status(400).json(errorResponse(error.message));
    }
  }

  /**
   * 回滚到指定版本
   * POST /api/admin/tasks/rollback/:versionNumber
   */
  public async rollbackConfig(req: Request, res: Response): Promise<void> {
    try {
      const versionNumber = req.params.versionNumber;
      const adminId = req.body.adminId || 'admin';

      if (!versionNumber) {
        res.status(400).json(errorResponse('版本号不能为空'));
        return;
      }

      const result = await this.configManager.rollbackToVersion(versionNumber, adminId);

      res.json(successResponse(result, '配置回滚成功'));
    } catch (error: any) {
      console.error('回滚配置失败:', error);
      res.status(400).json(errorResponse(error.message));
    }
  }

  /**
   * 获取所有配置版本
   * GET /api/admin/tasks/versions
   */
  public async getVersions(req: Request, res: Response): Promise<void> {
    try {
      const versions = await this.configManager.getAllVersions();
      const versionsData = versions.map(v => v.toVersionJSON());

      res.json(successResponse({
        versions: versionsData,
        total: versions.length
      }, '获取版本列表成功'));
    } catch (error: any) {
      console.error('获取版本列表失败:', error);
      res.status(500).json(errorResponse('获取版本列表失败'));
    }
  }

  /**
   * 获取当前活跃版本
   * GET /api/admin/tasks/active-version
   */
  public async getActiveVersion(req: Request, res: Response): Promise<void> {
    try {
      const activeVersion = await this.configManager.getActiveVersion();

      res.json(successResponse({
        activeVersion: activeVersion ? activeVersion.toVersionJSON() : null
      }, '获取活跃版本成功'));
    } catch (error: any) {
      console.error('获取活跃版本失败:', error);
      res.status(500).json(errorResponse('获取活跃版本失败'));
    }
  }

  /**
   * 获取配置操作日志
   * GET /api/admin/tasks/logs
   */
  public async getConfigLogs(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const logs = await this.configManager.getConfigLogs(limit);
      const logsData = logs.map(log => log.toLogJSON());

      res.json(successResponse({
        logs: logsData,
        total: logs.length
      }, '获取操作日志成功'));
    } catch (error: any) {
      console.error('获取操作日志失败:', error);
      res.status(500).json(errorResponse('获取操作日志失败'));
    }
  }

  /**
   * 获取当前任务配置
   * GET /api/admin/tasks/configs
   */
  public async getCurrentConfigs(req: Request, res: Response): Promise<void> {
    try {
      const configs = await TaskConfig.findAll({
        where: { isActive: true },
        order: [['id', 'ASC']]
      });

      const configsData = configs.map(config => ({
        id: config.id,
        condition: config.condition,
        type: config.type,
        describe: config.describe,
        price1: config.price1,
        price2: config.price2,
        diamond: config.diamond,
        box: config.box,
        coin: config.coin,
        item: config.item,
        typeDescription: config.getTypeDescription(),
        parameterDescription: config.getParameterDescription(),
        rewards: config.getRewards(),
        hasRewards: config.hasRewards(),
        targetProgress: config.getTargetProgress()
      }));

      res.json(successResponse({
        configs: configsData,
        total: configs.length
      }, '获取当前配置成功'));
    } catch (error: any) {
      console.error('获取当前配置失败:', error);
      res.status(500).json(errorResponse('获取当前配置失败'));
    }
  }

  /**
   * 初始化所有用户任务
   * POST /api/admin/tasks/initialize-all-users
   */
  public async initializeAllUserTasks(req: Request, res: Response): Promise<void> {
    try {
      // 异步执行，不阻塞响应
      this.taskService.initializeAllUserTasks().catch(error => {
        console.error('批量初始化用户任务失败:', error);
      });

      res.json(successResponse({}, '开始批量初始化用户任务，请查看服务器日志了解进度'));
    } catch (error: any) {
      console.error('启动批量初始化失败:', error);
      res.status(500).json(errorResponse('启动批量初始化失败'));
    }
  }


}

// 创建控制器实例
const adminTaskController = new AdminTaskController();

// 导出控制器方法
export const uploadConfig = (req: Request, res: Response) => adminTaskController.uploadConfig(req, res);
export const validateConfig = (req: Request, res: Response) => adminTaskController.validateConfig(req, res);
export const applyConfig = (req: Request, res: Response) => adminTaskController.applyConfig(req, res);
export const rollbackConfig = (req: Request, res: Response) => adminTaskController.rollbackConfig(req, res);
export const getVersions = (req: Request, res: Response) => adminTaskController.getVersions(req, res);
export const getActiveVersion = (req: Request, res: Response) => adminTaskController.getActiveVersion(req, res);
export const getConfigLogs = (req: Request, res: Response) => adminTaskController.getConfigLogs(req, res);
export const getCurrentConfigs = (req: Request, res: Response) => adminTaskController.getCurrentConfigs(req, res);
export const initializeAllUserTasks = (req: Request, res: Response) => adminTaskController.initializeAllUserTasks(req, res);

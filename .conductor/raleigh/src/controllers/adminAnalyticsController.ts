// src/controllers/adminAnalyticsController.ts

import { Request, Response } from "express";
import { AnalyticsService } from "../services/analyticsService";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { tFromRequest } from "../i18n";

/**
 * 管理后台数据分析控制器
 * 提供各种运营数据的API接口
 */

/**
 * 获取游戏启动统计
 * GET /api/admin/analytics/game-starts
 */
export const getGameStartStats = async (req: Request, res: Response) => {
  try {
    const { date } = req.query;
    const dailyStarts = await AnalyticsService.getDailyGameStarts(date as string);
    
    res.json(successResponse({
      dailyGameStarts: dailyStarts,
      date: date || new Date().toISOString().split('T')[0]
    }));
  } catch (error) {
    console.error('获取游戏启动统计失败:', error);
    res.status(500).json(errorResponse(
      '获取游戏启动统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取解锁区域统计
 * GET /api/admin/analytics/unlocked-areas
 */
export const getUnlockedAreasStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getUnlockedAreasStats();
    
    res.json(successResponse({
      unlockedAreasStats: stats,
      totalAreas: 20,
      summary: {
        totalUnlockedCount: stats.reduce((sum, item) => sum + item.count, 0),
        areasWithData: stats.length
      }
    }));
  } catch (error) {
    console.error('获取解锁区域统计失败:', error);
    res.status(500).json(errorResponse(
      '获取解锁区域统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取配送线升级统计
 * GET /api/admin/analytics/delivery-line-upgrades
 */
export const getDeliveryLineUpgradeStats = async (req: Request, res: Response) => {
  try {
    const { levels } = req.query;
    const levelArray = levels ? 
      (levels as string).split(',').map(l => parseInt(l.trim())) : 
      [10, 20, 25, 30, 35, 40, 45, 50];
    
    const stats = await AnalyticsService.getDeliveryLineUpgradeStats(levelArray);
    
    res.json(successResponse({
      deliveryLineUpgradeStats: stats,
      queriedLevels: levelArray,
      summary: {
        totalPlayersWithUpgrades: stats.reduce((sum, item) => sum + item.count, 0),
        levelsWithData: stats.length
      }
    }));
  } catch (error) {
    console.error('获取配送线升级统计失败:', error);
    res.status(500).json(errorResponse(
      '获取配送线升级统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取道具使用统计
 * GET /api/admin/analytics/booster-usage
 */
export const getBoosterUsageStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getBoosterUsageStats();
    
    res.json(successResponse({
      boosterUsageStats: stats,
      summary: {
        totalUniqueUsers: stats.totalUsers.reduce((sum, item) => sum + item.count, 0),
        totalUsageCount: stats.totalUsage.reduce((sum, item) => sum + item.count, 0),
        boosterTypes: stats.totalUsers.length
      }
    }));
  } catch (error) {
    console.error('获取道具使用统计失败:', error);
    res.status(500).json(errorResponse(
      '获取道具使用统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取充值数据统计
 * GET /api/admin/analytics/revenue
 */
export const getRevenueStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getRevenueStats();
    
    res.json(successResponse({
      revenueStats: stats,
      summary: {
        averageRevenuePerUser: stats.uniquePayers > 0 ? 
          (stats.totalRevenue / stats.uniquePayers).toFixed(2) : '0',
        averageRevenuePerPurchase: stats.totalPurchases > 0 ? 
          (stats.totalRevenue / stats.totalPurchases).toFixed(2) : '0',
        conversionRate: '未实现' // 需要总用户数来计算
      }
    }));
  } catch (error) {
    console.error('获取充值数据统计失败:', error);
    res.status(500).json(errorResponse(
      '获取充值数据统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取每日APP开启统计
 * GET /api/admin/analytics/daily-app-opens
 */
export const getDailyAppOpensStats = async (req: Request, res: Response) => {
  try {
    const { days } = req.query;
    const daysCount = days ? parseInt(days as string) : 7;
    
    const stats = await AnalyticsService.getDailyAppOpens(daysCount);
    
    res.json(successResponse({
      dailyAppOpensStats: stats,
      period: `${daysCount}天`,
      summary: {
        totalDays: stats.length,
        averageDailyOpens: stats.length > 0 ? 
          Math.round(stats.reduce((sum, item) => sum + item.count, 0) / stats.length) : 0,
        peakDay: stats.length > 0 ? 
          stats.reduce((max, item) => item.count > max.count ? item : max) : null
      }
    }));
  } catch (error) {
    console.error('获取每日APP开启统计失败:', error);
    res.status(500).json(errorResponse(
      '获取每日APP开启统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取牧场区升级统计
 * GET /api/admin/analytics/farm-plot-upgrades
 */
export const getFarmPlotUpgradeStats = async (req: Request, res: Response) => {
  try {
    const { levels } = req.query;
    const levelArray = levels ? 
      (levels as string).split(',').map(l => parseInt(l.trim())) : 
      [10, 20, 25, 30, 35, 40, 45, 50];
    
    const stats = await AnalyticsService.getFarmPlotUpgradeStats(levelArray);
    
    res.json(successResponse({
      farmPlotUpgradeStats: stats,
      queriedLevels: levelArray,
      summary: {
        totalAreas: stats.length,
        totalUpgrades: stats.reduce((sum, area) => 
          sum + area.levelStats.reduce((areaSum, level) => areaSum + level.count, 0), 0
        )
      }
    }));
  } catch (error) {
    console.error('获取牧场区升级统计失败:', error);
    res.status(500).json(errorResponse(
      '获取牧场区升级统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取任务完成统计
 * GET /api/admin/analytics/task-completion
 */
export const getTaskCompletionStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getTaskCompletionStats();
    
    res.json(successResponse({
      taskCompletionStats: stats,
      summary: {
        totalTasks: stats.taskStats.length,
        averageCompletionsPerTask: stats.taskStats.length > 0 ? 
          Math.round(stats.totalCompletions / stats.taskStats.length) : 0,
        mostPopularTask: stats.taskStats.length > 0 ? 
          stats.taskStats.reduce((max, task) => task.completions > max.completions ? task : max) : null
      }
    }));
  } catch (error) {
    console.error('获取任务完成统计失败:', error);
    res.status(500).json(errorResponse(
      '获取任务完成统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取邀请好友统计
 * GET /api/admin/analytics/referral
 */
export const getReferralStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getReferralStats();
    
    res.json(successResponse({
      referralStats: stats,
      summary: {
        totalReferrers: stats.referralDistribution.reduce((sum, range) => sum + range.count, 0),
        averageReferralsPerUser: stats.referralDistribution.length > 0 ? 
          Math.round(stats.totalInvitations / stats.referralDistribution.reduce((sum, range) => sum + range.count, 0)) : 0
      }
    }));
  } catch (error) {
    console.error('获取邀请好友统计失败:', error);
    res.status(500).json(errorResponse(
      '获取邀请好友统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取宝箱统计
 * GET /api/admin/analytics/chest
 */
export const getChestStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getChestStats();
    
    res.json(successResponse({
      chestStats: stats,
      summary: {
        totalChestTypes: stats.chestsByType.length,
        dailyChestPercentage: stats.totalChests > 0 ? 
          ((stats.chestsByType.find(c => c.type === 'daily')?.count || 0) / stats.totalChests * 100).toFixed(2) + '%' : '0%'
      }
    }));
  } catch (error) {
    console.error('获取宝箱统计失败:', error);
    res.status(500).json(errorResponse(
      '获取宝箱统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取玩家增长和留存统计
 * GET /api/admin/analytics/player-growth-retention
 */
export const getPlayerGrowthRetentionStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getPlayerGrowthAndRetention();
    
    res.json(successResponse({
      playerGrowthRetentionStats: stats,
      summary: {
        averageDailyNewUsers: stats.dailyNewUsers.length > 0 ? 
          Math.round(stats.dailyNewUsers.reduce((sum, day) => sum + day.count, 0) / stats.dailyNewUsers.length) : 0,
        retentionHealthy: stats.dayOneRetention >= 30 && stats.day7Retention >= 15
      }
    }));
  } catch (error) {
    console.error('获取玩家增长和留存统计失败:', error);
    res.status(500).json(errorResponse(
      '获取玩家增长和留存统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取玩家资源统计
 * GET /api/admin/analytics/player-resources
 */
export const getPlayerResourceStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getPlayerResourceStats();
    
    res.json(successResponse({
      playerResourceStats: stats,
      summary: {
        economyHealth: {
          gemDistribution: '需要进一步分析',
          inflationRisk: parseFloat(stats.averageGems) > 100000 ? '高' : '正常'
        }
      }
    }));
  } catch (error) {
    console.error('获取玩家资源统计失败:', error);
    res.status(500).json(errorResponse(
      '获取玩家资源统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取玩家游玩时长统计
 * GET /api/admin/analytics/play-time
 */
export const getPlayTimeStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getPlayTimeStats();
    
    res.json(successResponse({
      playTimeStats: stats,
      summary: {
        engagement: stats.averageDailyPlayTime > 30 ? '高' : stats.averageDailyPlayTime > 10 ? '中等' : '低',
        note: '游玩时长统计基于活跃时间估算，可能需要更精确的埋点数据'
      }
    }));
  } catch (error) {
    console.error('获取玩家游玩时长统计失败:', error);
    res.status(500).json(errorResponse(
      '获取玩家游玩时长统计失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取综合统计面板数据
 * GET /api/admin/analytics/dashboard
 */
export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    const stats = await AnalyticsService.getDashboardStats();
    
    res.json(successResponse({
      dashboardStats: stats,
      metadata: {
        generatedAt: new Date().toISOString(),
        dataDescription: '管理后台综合数据面板',
        refreshRecommendation: '建议每小时刷新一次'
      }
    }));
  } catch (error) {
    console.error('获取综合统计面板数据失败:', error);
    res.status(500).json(errorResponse(
      '获取综合统计面板数据失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};

/**
 * 获取自定义查询统计
 * POST /api/admin/analytics/custom-query
 */
export const getCustomQueryStats = async (req: Request, res: Response) => {
  try {
    const { 
      startDate, 
      endDate, 
      metrics = [], 
      filters = {} 
    } = req.body;

    // 这里可以根据请求参数进行自定义查询
    // 为了安全起见，应该限制可执行的查询类型
    const allowedMetrics = [
      'dailyGameStarts',
      'unlockedAreas', 
      'deliveryLineUpgrades',
      'revenue',
      'playerGrowth'
    ];

    const validMetrics = metrics.filter((metric: string) => allowedMetrics.includes(metric));
    
    if (validMetrics.length === 0) {
      res.status(400).json(errorResponse(
        '无效的查询参数',
        '请提供有效的统计指标'
      ));
      return;
    }

    const results: any = {};
    
    // 根据请求的指标获取相应数据
    for (const metric of validMetrics) {
      switch (metric) {
        case 'dailyGameStarts':
          results.dailyGameStarts = await AnalyticsService.getDailyGameStarts(startDate);
          break;
        case 'unlockedAreas':
          results.unlockedAreas = await AnalyticsService.getUnlockedAreasStats();
          break;
        case 'revenue':
          results.revenue = await AnalyticsService.getRevenueStats();
          break;
        // 可以继续添加其他指标
      }
    }
    
    res.json(successResponse({
      customQueryResults: results,
      query: {
        startDate,
        endDate,
        requestedMetrics: metrics,
        validMetrics,
        filters
      }
    }));
  } catch (error) {
    console.error('执行自定义查询失败:', error);
    res.status(500).json(errorResponse(
      '执行自定义查询失败',
      error instanceof Error ? error.message : '未知错误'
    ));
  }
};
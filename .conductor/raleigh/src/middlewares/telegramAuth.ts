// src/middlewares/telegramAuth.ts

import { Request, Response, NextFunction } from "express";
import { verifyTelegramLogin } from "../utils/random";
import { MyRequest } from "../types/customRequest";
import { tFromRequest } from "../i18n";
import { errorResponse } from "../utils/responseUtil";
import { User } from "../models/User";

/**
 * Telegram认证中间件
 * 前端请求时，应在body中带上: { "initData": "<Telegram WebApp initData>" }
 */
export async function telegramAuthMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // 1) 从请求体中获取Telegram initData
    const { initData } = req.body;
    if (!initData) {
      res.status(401).json(errorResponse(tFromRequest(req, "errors.missingInitData")));
      return;
    }

    // 2) 验证Telegram数据
    const isValid = verifyTelegramLogin(initData);
    if (!isValid) {
      res.status(401).json(errorResponse(tFromRequest(req, "errors.invalidTelegramData")));
      return;
    }

    // 3) 解析用户数据
    const parsedData = new URLSearchParams(initData);
    const userJson = parsedData.get("user");
    
    if (!userJson) {
      res.status(401).json(errorResponse(tFromRequest(req, "errors.userDataNotFound")));
      return;
    }

    const telegramUser = JSON.parse(userJson);
    const telegramId = String(telegramUser.id || "");
    
    if (!telegramId) {
      res.status(401).json(errorResponse(tFromRequest(req, "errors.invalidTelegramUserId")));
      return;
    }

    // 4) 查找用户
    const user = await User.findOne({ where: { telegramId } });
    if (!user) {
      // 如果用户不存在，仍然允许继续，因为这是分享助力功能
      // 我们只需要知道这是一个有效的Telegram用户
      const myReq = req as MyRequest;
      myReq.user = {
        telegramId,
        userId: 0, // 使用0表示未注册用户
        isPremium: telegramUser.is_premium || false
      };
      next();
      return;
    }

    // 5) 如果用户存在，设置用户信息
    const myReq = req as MyRequest;
    myReq.user = {
      telegramId,
      userId: user.id,
      isPremium: user.telegram_premium || false
    };

    // 6) 放行
    next();
  } catch (err: any) {
    console.error("Telegram认证失败:", err);
    res.status(401).json(errorResponse(tFromRequest(req, "errors.authenticationFailed")));
    return;
  }
}
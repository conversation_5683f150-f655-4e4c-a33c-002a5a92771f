#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { ethers } from 'ethers';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🔍 检查未注册用户的充值记录');
  console.log('===============================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 1. 查询所有失败的充值记录（未注册用户）
    console.log('\n📊 查询未注册用户的充值记录...');
    
    const unregisteredDeposits = await PhrsDeposit.findAll({
      where: {
        walletId: 0,
        status: 'FAILED',
        errorMessage: 'User wallet not found'
      },
      order: [['blockNumber', 'DESC']]
    });

    console.log(`📝 找到 ${unregisteredDeposits.length} 条未注册用户的充值记录`);

    if (unregisteredDeposits.length === 0) {
      console.log('✅ 没有未注册用户的充值记录');
      return;
    }

    // 2. 统计信息
    let totalAmount = 0;
    const addressStats = new Map<string, { count: number, amount: number }>();

    unregisteredDeposits.forEach(deposit => {
      const amount = parseFloat(deposit.amount.toString());
      totalAmount += amount;

      const address = deposit.userAddress;
      if (addressStats.has(address)) {
        const stats = addressStats.get(address)!;
        stats.count += 1;
        stats.amount += amount;
      } else {
        addressStats.set(address, { count: 1, amount });
      }
    });

    console.log(`\n📊 统计信息:`);
    console.log(`   总记录数: ${unregisteredDeposits.length}`);
    console.log(`   总金额: ${totalAmount.toFixed(3)} PHRS`);
    console.log(`   涉及地址数: ${addressStats.size}`);

    // 3. 显示详细记录
    console.log(`\n📝 详细记录:`);
    console.log('=====================================');

    unregisteredDeposits.forEach((deposit, index) => {
      console.log(`${index + 1}. 区块: ${deposit.blockNumber}`);
      console.log(`   地址: ${deposit.userAddress}`);
      console.log(`   金额: ${deposit.amount} PHRS`);
      console.log(`   交易: ${deposit.transactionHash}`);
      console.log(`   时间: ${deposit.blockTimestamp}`);
      console.log(`   处理时间: ${deposit.processedAt}`);
      console.log('   ---');
    });

    // 4. 按地址分组显示
    console.log(`\n📊 按地址分组统计:`);
    console.log('=====================================');

    const sortedAddresses = Array.from(addressStats.entries())
      .sort((a, b) => b[1].amount - a[1].amount);

    sortedAddresses.forEach(([address, stats], index) => {
      console.log(`${index + 1}. ${address}`);
      console.log(`   充值次数: ${stats.count}`);
      console.log(`   总金额: ${stats.amount.toFixed(3)} PHRS`);
      console.log(`   平均金额: ${(stats.amount / stats.count).toFixed(3)} PHRS`);
      console.log('   ---');
    });

    // 5. 检查是否有特定区块的记录
    const targetBlock = 13805521;
    const targetBlockDeposit = unregisteredDeposits.find(
      deposit => deposit.blockNumber === targetBlock
    );

    if (targetBlockDeposit) {
      console.log(`\n🎯 找到区块 ${targetBlock} 的未注册充值:`);
      console.log(`   地址: ${targetBlockDeposit.userAddress}`);
      console.log(`   金额: ${targetBlockDeposit.amount} PHRS`);
      console.log(`   交易: ${targetBlockDeposit.transactionHash}`);
      console.log(`   时间: ${targetBlockDeposit.blockTimestamp}`);
    } else {
      console.log(`\n⚠️  没有找到区块 ${targetBlock} 的未注册充值记录`);
      console.log(`   这可能意味着:`);
      console.log(`   1. 该区块的充值来自已注册用户`);
      console.log(`   2. 监控服务还未处理该区块`);
      console.log(`   3. 该区块实际上没有充值事件`);
    }

    // 6. 建议处理方案
    console.log(`\n💡 处理建议:`);
    console.log('=====================================');
    console.log(`1. 🔍 调查这些地址是否为合法用户`);
    console.log(`2. 📝 如果是合法用户，可以为他们创建用户钱包记录`);
    console.log(`3. 🔄 重新处理这些充值记录，更新状态为成功`);
    console.log(`4. 🚫 如果是测试或错误充值，可以标记为已处理但不更新余额`);
    console.log(`5. 📧 联系用户确认充值意图`);

    // 7. 提供处理脚本示例
    console.log(`\n🔧 处理脚本示例:`);
    console.log('=====================================');
    console.log(`-- 为特定地址创建用户钱包记录的SQL:`);
    sortedAddresses.slice(0, 3).forEach(([address], index) => {
      console.log(`-- 地址 ${index + 1}: ${address}`);
      console.log(`INSERT INTO user_wallets (phrs_wallet_address, phrs_balance, created_at, updated_at)`);
      console.log(`VALUES ('${address.toLowerCase()}', '0.000', NOW(), NOW());`);
      console.log(``);
    });

    console.log(`-- 更新充值记录状态的SQL:`);
    console.log(`UPDATE phrs_deposits SET`);
    console.log(`  status = 'CONFIRMED',`);
    console.log(`  wallet_id = (SELECT id FROM user_wallets WHERE phrs_wallet_address = phrs_deposits.user_address),`);
    console.log(`  error_message = NULL`);
    console.log(`WHERE wallet_id = 0 AND status = 'FAILED' AND error_message = 'User wallet not found';`);

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

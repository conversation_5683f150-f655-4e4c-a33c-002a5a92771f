#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';

// 加载环境变量
dotenv.config();

/**
 * 测试历史事件处理跳过功能
 * 验证当 PHRS_HISTORICAL_START_BLOCK 为 0 或未设置时，是否正确跳过历史事件处理
 */
async function testHistoricalSkip() {
  console.log('🧪 测试历史事件处理跳过功能');
  console.log('================================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 测试场景1：PHRS_HISTORICAL_START_BLOCK = 0
    console.log('\n📋 测试场景1: PHRS_HISTORICAL_START_BLOCK = 0');
    console.log('-------------------------------------------');
    
    // 临时设置环境变量
    const originalValue = process.env.PHRS_HISTORICAL_START_BLOCK;
    process.env.PHRS_HISTORICAL_START_BLOCK = '0';
    
    // 动态导入服务（在设置环境变量后）
    delete require.cache[require.resolve('../services/phrsDepositService')];
    const { PhrsDepositService } = await import('../services/phrsDepositService');
    const service1 = new PhrsDepositService();
    
    console.log('🔍 启动监控服务...');
    await service1.startListening();
    
    console.log('⏳ 等待5秒观察日志...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    service1.stopListening();
    console.log('⏹️  服务已停止');

    // 测试场景2：PHRS_HISTORICAL_START_BLOCK 未设置
    console.log('\n📋 测试场景2: PHRS_HISTORICAL_START_BLOCK 未设置');
    console.log('---------------------------------------------');
    
    // 删除环境变量
    delete process.env.PHRS_HISTORICAL_START_BLOCK;
    
    // 重新导入服务
    delete require.cache[require.resolve('../services/phrsDepositService')];
    const { PhrsDepositService: PhrsDepositService2 } = await import('../services/phrsDepositService');
    const service2 = new PhrsDepositService2();
    
    console.log('🔍 启动监控服务...');
    await service2.startListening();
    
    console.log('⏳ 等待5秒观察日志...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    service2.stopListening();
    console.log('⏹️  服务已停止');

    // 测试场景3：PHRS_HISTORICAL_START_BLOCK > 0
    console.log('\n📋 测试场景3: PHRS_HISTORICAL_START_BLOCK > 0');
    console.log('--------------------------------------------');
    
    // 设置一个有效的起始区块
    process.env.PHRS_HISTORICAL_START_BLOCK = '13805521';
    
    // 重新导入服务
    delete require.cache[require.resolve('../services/phrsDepositService')];
    const { PhrsDepositService: PhrsDepositService3 } = await import('../services/phrsDepositService');
    const service3 = new PhrsDepositService3();
    
    console.log('🔍 启动监控服务...');
    await service3.startListening();
    
    console.log('⏳ 等待5秒观察日志...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    service3.stopListening();
    console.log('⏹️  服务已停止');

    // 恢复原始环境变量
    if (originalValue !== undefined) {
      process.env.PHRS_HISTORICAL_START_BLOCK = originalValue;
    } else {
      delete process.env.PHRS_HISTORICAL_START_BLOCK;
    }

    console.log('\n✅ 测试完成！');
    console.log('📊 测试结果总结:');
    console.log('   - 场景1 (值=0): 应该跳过历史事件处理');
    console.log('   - 场景2 (未设置): 应该跳过历史事件处理');
    console.log('   - 场景3 (值>0): 应该处理历史事件');
    console.log('\n请检查上面的日志输出，确认每个场景的行为是否符合预期。');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行测试
testHistoricalSkip();

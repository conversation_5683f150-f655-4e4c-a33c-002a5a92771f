#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';
import readline from 'readline';

// 加载环境变量
dotenv.config();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askQuestion(question: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

async function main() {
  console.log('🧪 PHRS充值服务测试工具');
  console.log('========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 显示服务状态
    const status = phrsDepositService.getStatus();
    console.log('\n📊 服务状态:');
    console.log(`   监听状态: ${status.isListening ? '✅ 运行中' : '❌ 已停止'}`);
    console.log(`   合约地址: ${status.contractAddress}`);
    console.log(`   最后处理区块: ${status.lastProcessedBlock}`);
    console.log(`   RPC地址: ${status.providerUrl}`);

    // 显示菜单
    console.log('\n🔧 测试选项:');
    console.log('1. 测试处理指定区块');
    console.log('2. 测试处理区块范围');
    console.log('3. 重置最后处理区块号');
    console.log('4. 查看服务状态');
    console.log('5. 退出');

    const choice = await askQuestion('\n请选择操作 (1-5): ');

    switch (choice) {
      case '1':
        await testSingleBlock();
        break;
      case '2':
        await testBlockRange();
        break;
      case '3':
        await resetLastProcessedBlock();
        break;
      case '4':
        await showServiceStatus();
        break;
      case '5':
        console.log('👋 退出程序');
        break;
      default:
        console.log('❌ 无效选择');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    rl.close();
  }

  process.exit(0);
}

async function testSingleBlock() {
  console.log('\n🎯 测试处理单个区块');
  console.log('===================');

  const blockInput = await askQuestion('请输入要测试的区块号 (默认: 13805521): ');
  const blockNumber = blockInput.trim() === '' ? 13805521 : parseInt(blockInput);

  if (isNaN(blockNumber) || blockNumber < 0) {
    console.log('❌ 无效的区块号');
    return;
  }

  console.log(`\n🔄 开始测试区块 ${blockNumber}...`);
  
  try {
    await phrsDepositService.testProcessBlocks(blockNumber);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

async function testBlockRange() {
  console.log('\n📊 测试处理区块范围');
  console.log('===================');

  const fromBlockInput = await askQuestion('请输入起始区块号: ');
  const fromBlock = parseInt(fromBlockInput);

  if (isNaN(fromBlock) || fromBlock < 0) {
    console.log('❌ 无效的起始区块号');
    return;
  }

  const toBlockInput = await askQuestion('请输入结束区块号 (留空表示只处理起始区块): ');
  let toBlock: number | undefined;

  if (toBlockInput.trim() !== '') {
    toBlock = parseInt(toBlockInput);
    if (isNaN(toBlock) || toBlock < fromBlock) {
      console.log('❌ 无效的结束区块号');
      return;
    }
  }

  console.log(`\n🔄 开始测试区块范围 ${fromBlock} 到 ${toBlock || fromBlock}...`);
  
  try {
    await phrsDepositService.testProcessBlocks(fromBlock, toBlock);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

async function resetLastProcessedBlock() {
  console.log('\n🔄 重置最后处理区块号');
  console.log('=======================');

  const currentStatus = phrsDepositService.getStatus();
  console.log(`当前最后处理区块: ${currentStatus.lastProcessedBlock}`);

  const blockInput = await askQuestion('请输入新的区块号: ');
  const blockNumber = parseInt(blockInput);

  if (isNaN(blockNumber) || blockNumber < 0) {
    console.log('❌ 无效的区块号');
    return;
  }

  const confirm = await askQuestion(`确认重置最后处理区块号为 ${blockNumber}? (y/N): `);
  if (confirm.toLowerCase() !== 'y') {
    console.log('❌ 操作已取消');
    return;
  }

  try {
    await phrsDepositService.testResetLastProcessedBlock(blockNumber);
  } catch (error) {
    console.error('❌ 重置失败:', error);
  }
}

async function showServiceStatus() {
  console.log('\n📊 详细服务状态');
  console.log('================');

  const status = phrsDepositService.getStatus();
  
  console.log(`监听状态: ${status.isListening ? '✅ 运行中' : '❌ 已停止'}`);
  console.log(`合约地址: ${status.contractAddress}`);
  console.log(`最后处理区块: ${status.lastProcessedBlock}`);
  console.log(`RPC地址: ${status.providerUrl}`);

  // 显示一些统计信息
  try {
    const { PhrsDeposit } = await import('../models');
    
    const totalDeposits = await PhrsDeposit.count();
    const confirmedDeposits = await PhrsDeposit.count({ where: { status: 'CONFIRMED' } });
    const failedDeposits = await PhrsDeposit.count({ where: { status: 'FAILED' } });
    
    console.log(`\n📊 数据库统计:`);
    console.log(`   总充值记录: ${totalDeposits}`);
    console.log(`   成功记录: ${confirmedDeposits}`);
    console.log(`   失败记录: ${failedDeposits}`);

    if (totalDeposits > 0) {
      const latestDeposit = await PhrsDeposit.findOne({
        order: [['blockNumber', 'DESC']],
        limit: 1
      });
      
      if (latestDeposit) {
        console.log(`   最新处理区块: ${latestDeposit.blockNumber}`);
        console.log(`   最新处理时间: ${latestDeposit.processedAt}`);
      }
    }

  } catch (error) {
    console.error('获取统计信息失败:', error);
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

// src/scripts/bindPhrsAddressAndReprocess.ts
import { sequelize } from '../config/db';
import { UserWallet, PhrsDeposit } from '../models';
import { Op } from 'sequelize';
import BigNumber from 'bignumber.js';

/**
 * 绑定PHRS地址并重新处理失败的充值记录
 */
async function bindPhrsAddressAndReprocess() {
  const targetAddress = '******************************************';
  
  console.log('🔧 绑定PHRS地址并重新处理充值记录...');
  console.log('================================================');
  console.log(`目标地址: ${targetAddress}`);

  try {
    // 1. 检查数据库连接
    console.log('\n1. 检查数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');

    // 2. 查找失败的充值记录
    console.log('\n2. 查找失败的充值记录...');
    const failedDeposits = await PhrsDeposit.findAll({
      where: {
        userAddress: targetAddress.toLowerCase(),
        status: 'FAILED',
        walletId: null
      },
      order: [['createdAt', 'ASC']]
    });

    console.log(`找到 ${failedDeposits.length} 条失败的充值记录`);
    
    if (failedDeposits.length === 0) {
      console.log('没有需要处理的失败充值记录');
      return;
    }

    // 显示失败记录详情
    let totalFailedAmount = new BigNumber(0);
    failedDeposits.forEach((deposit, index) => {
      console.log(`   记录 ${index + 1}:`);
      console.log(`     金额: ${deposit.amount} PHRS`);
      console.log(`     交易哈希: ${deposit.transactionHash}`);
      console.log(`     时间: ${deposit.createdAt}`);
      totalFailedAmount = totalFailedAmount.plus(new BigNumber(deposit.amount.toString()));
    });
    
    console.log(`   总失败金额: ${totalFailedAmount.toFixed(18)} PHRS`);

    // 3. 查找可能的用户（这里需要手动指定用户ID，或者通过其他方式确定）
    console.log('\n3. 查找用户...');
    console.log('⚠️  需要手动指定用户ID，因为无法自动确定地址属于哪个用户');
    console.log('   请提供用户ID或用户钱包ID来继续操作');
    
    // 显示一些用户钱包供参考
    const recentWallets = await UserWallet.findAll({
      where: {
        phrsWalletAddress: { [Op.is]: null as any } // 找没有绑定PHRS地址的钱包
      },
      limit: 5,
      order: [['createdAt', 'DESC']]
    });

    console.log(`   最近的未绑定PHRS地址的用户钱包（供参考）:`);
    recentWallets.forEach((wallet, index) => {
      console.log(`     钱包 ${index + 1}:`);
      console.log(`       钱包ID: ${wallet.id}`);
      console.log(`       用户ID: ${wallet.userId}`);
      console.log(`       创建时间: ${wallet.createdAt}`);
    });

    // 4. 提供手动绑定的示例代码
    console.log('\n4. 手动绑定示例代码:');
    console.log('   如果确定了用户钱包ID，可以运行以下代码进行绑定和重新处理：');
    console.log('');
    console.log('   ```typescript');
    console.log('   const walletId = YOUR_WALLET_ID; // 替换为实际的钱包ID');
    console.log('   const userWallet = await UserWallet.findByPk(walletId);');
    console.log('   ');
    console.log('   // 绑定PHRS地址');
    console.log('   await userWallet.update({');
    console.log(`     phrsWalletAddress: "${targetAddress.toLowerCase()}",`);
    console.log('     lastPhrsUpdateTime: new Date()');
    console.log('   });');
    console.log('   ');
    console.log('   // 重新处理失败的充值记录');
    console.log('   for (const deposit of failedDeposits) {');
    console.log('     await deposit.update({');
    console.log('       walletId: userWallet.id,');
    console.log('       status: "CONFIRMED"');
    console.log('     });');
    console.log('   }');
    console.log('   ');
    console.log('   // 更新用户PHRS余额');
    console.log('   const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || "0");');
    console.log(`   const newBalance = currentBalance.plus("${totalFailedAmount.toFixed(18)}");`);
    console.log('   await userWallet.update({');
    console.log('     phrsBalance: newBalance.toFixed(18),');
    console.log('     lastPhrsUpdateTime: new Date()');
    console.log('   });');
    console.log('   ```');

    // 5. 创建一个自动绑定函数（需要用户确认）
    console.log('\n5. 自动处理选项:');
    console.log('   如果你知道确切的用户钱包ID，可以调用 bindAndReprocess(walletId) 函数');

    console.log('\n🎉 分析完成！');
    console.log('================================================');
    console.log('📊 总结:');
    console.log(`   - 失败充值记录: ${failedDeposits.length} 条`);
    console.log(`   - 总失败金额: ${totalFailedAmount.toFixed(18)} PHRS`);
    console.log(`   - 需要绑定的地址: ${targetAddress}`);
    console.log('   - 下一步: 确定用户钱包ID并进行绑定');

  } catch (error) {
    console.error('❌ 处理过程中发生错误:', error);
    throw error;
  }
}

/**
 * 自动绑定并重新处理（需要提供钱包ID）
 */
async function bindAndReprocess(walletId: number) {
  const targetAddress = '******************************************';
  
  console.log(`🔄 开始为钱包ID ${walletId} 绑定地址并重新处理充值...`);
  
  try {
    // 1. 获取用户钱包
    const userWallet = await UserWallet.findByPk(walletId);
    if (!userWallet) {
      throw new Error(`未找到钱包ID ${walletId}`);
    }

    console.log(`找到用户钱包: 用户ID ${userWallet.userId}`);

    // 2. 检查地址是否已被其他用户绑定
    const existingWallet = await UserWallet.findOne({
      where: {
        phrsWalletAddress: targetAddress.toLowerCase(),
        id: { [Op.ne]: walletId }
      }
    });

    if (existingWallet) {
      throw new Error(`地址 ${targetAddress} 已被其他用户（钱包ID: ${existingWallet.id}）绑定`);
    }

    // 3. 绑定PHRS地址
    console.log('绑定PHRS地址...');
    await userWallet.update({
      phrsWalletAddress: targetAddress.toLowerCase(),
      lastPhrsUpdateTime: new Date()
    });
    console.log('✅ PHRS地址绑定成功');

    // 4. 查找失败的充值记录
    const failedDeposits = await PhrsDeposit.findAll({
      where: {
        userAddress: targetAddress.toLowerCase(),
        status: 'FAILED',
        walletId: null
      }
    });

    console.log(`找到 ${failedDeposits.length} 条失败的充值记录`);

    // 5. 重新处理充值记录
    let totalAmount = new BigNumber(0);
    
    for (const deposit of failedDeposits) {
      await deposit.update({
        walletId: userWallet.id,
        status: 'CONFIRMED',
        errorMessage: undefined,
        processedAt: new Date()
      });
      
      totalAmount = totalAmount.plus(new BigNumber(deposit.amount.toString()));
      console.log(`✅ 处理充值记录: ${deposit.transactionHash} (${deposit.amount} PHRS)`);
    }

    // 6. 更新用户PHRS余额
    console.log('更新用户PHRS余额...');
    const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');
    const newBalance = currentBalance.plus(totalAmount);
    
    await userWallet.update({
      phrsBalance: newBalance.toFixed(18),
      lastPhrsUpdateTime: new Date()
    });

    console.log('🎉 处理完成！');
    console.log(`   - 处理的充值记录: ${failedDeposits.length} 条`);
    console.log(`   - 充值总金额: ${totalAmount.toFixed(18)} PHRS`);
    console.log(`   - 用户新余额: ${newBalance.toFixed(18)} PHRS`);

  } catch (error) {
    console.error('❌ 绑定和重新处理失败:', error);
    throw error;
  }
}

// 运行分析
if (require.main === module) {
  bindPhrsAddressAndReprocess()
    .then(() => {
      console.log('\n✅ 分析完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 分析失败:', error);
      process.exit(1);
    });
}

export { bindPhrsAddressAndReprocess, bindAndReprocess };

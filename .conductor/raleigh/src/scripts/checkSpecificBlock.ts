#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { ethers } from 'ethers';
import { connectDB } from '../config/db';
import { UserWallet, PhrsDeposit } from '../models';

// 加载环境变量
dotenv.config();

async function main() {
  const targetBlock = 13805521; // 目标区块
  
  console.log(`🔍 检查区块 ${targetBlock} 的充值事件`);
  console.log('=====================================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';

    if (!contractAddress) {
      console.log('❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量');
      return;
    }

    // 连接网络
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const currentBlock = await provider.getBlockNumber();
    
    console.log(`📡 当前区块: ${currentBlock}`);
    console.log(`🎯 目标区块: ${targetBlock}`);

    if (targetBlock > currentBlock) {
      console.log('❌ 目标区块还未产生');
      return;
    }

    // 连接合约
    const contractABI = [
      "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
      "function getUserBalance(address user) external view returns (uint256)",
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, provider);

    // 1. 查询特定区块的事件
    console.log(`\n🔍 查询区块 ${targetBlock} 的Deposit事件...`);
    
    const filter = contract.filters.Deposit();
    const events = await contract.queryFilter(filter, targetBlock, targetBlock);
    
    console.log(`📡 找到 ${events.length} 个Deposit事件`);

    if (events.length === 0) {
      console.log('⚠️  在指定区块中没有找到Deposit事件');
      
      // 尝试查询前后几个区块
      console.log('\n🔍 查询前后区块...');
      const rangeEvents = await contract.queryFilter(filter, targetBlock - 2, targetBlock + 2);
      console.log(`📡 区块 ${targetBlock - 2} 到 ${targetBlock + 2} 中找到 ${rangeEvents.length} 个事件`);
      
      if (rangeEvents.length > 0) {
        rangeEvents.forEach((event, index) => {
          if ('args' in event && event.args) {
            console.log(`   ${index + 1}. 区块: ${event.blockNumber} (${event.blockNumber === targetBlock ? '目标区块' : '邻近区块'})`);
            console.log(`      交易: ${event.transactionHash}`);
            console.log(`      用户: ${event.args[0]}`);
            console.log(`      金额: ${ethers.formatEther(event.args[1])} PHRS`);
          }
        });
      }
      return;
    }

    // 2. 分析找到的事件
    for (let i = 0; i < events.length; i++) {
      const event = events[i];
      if (!('args' in event) || !event.args) continue;

      const userAddress = event.args[0] as string;
      const amount = event.args[1] as bigint;
      const timestamp = event.args[2] as bigint;

      console.log(`\n📝 事件 ${i + 1} 详情:`);
      console.log(`   区块号: ${event.blockNumber}`);
      console.log(`   交易哈希: ${event.transactionHash}`);
      console.log(`   用户地址: ${userAddress}`);
      console.log(`   充值金额: ${ethers.formatEther(amount)} PHRS`);
      console.log(`   时间戳: ${timestamp} (${new Date(Number(timestamp) * 1000).toLocaleString()})`);

      // 3. 检查用户是否在系统中
      console.log(`\n👤 检查用户 ${userAddress} 是否在系统中...`);
      
      const userWallet = await UserWallet.findOne({
        where: { phrsWalletAddress: userAddress.toLowerCase() }
      });

      if (userWallet) {
        console.log(`✅ 找到用户钱包:`);
        console.log(`   钱包ID: ${userWallet.id}`);
        console.log(`   当前PHRS余额: ${userWallet.phrsBalance || '0'}`);
        console.log(`   最后更新时间: ${userWallet.lastPhrsUpdateTime || '未设置'}`);
      } else {
        console.log(`❌ 用户地址 ${userAddress} 不在系统中`);
        console.log(`   这解释了为什么监控服务没有处理这个充值`);
      }

      // 4. 检查是否已有充值记录
      console.log(`\n💾 检查充值记录...`);
      
      const existingDeposit = await PhrsDeposit.findOne({
        where: { transactionHash: event.transactionHash }
      });

      if (existingDeposit) {
        console.log(`✅ 找到充值记录:`);
        console.log(`   记录ID: ${existingDeposit.id}`);
        console.log(`   钱包ID: ${existingDeposit.walletId}`);
        console.log(`   状态: ${existingDeposit.status}`);
        console.log(`   处理时间: ${existingDeposit.processedAt}`);
        if (existingDeposit.errorMessage) {
          console.log(`   错误信息: ${existingDeposit.errorMessage}`);
        }
      } else {
        console.log(`❌ 没有找到对应的充值记录`);
        console.log(`   交易哈希: ${event.transactionHash}`);
      }

      // 5. 获取链上用户余额
      try {
        const onChainBalance = await contract.getUserBalance(userAddress);
        console.log(`\n🔗 链上用户余额: ${ethers.formatEther(onChainBalance)} PHRS`);
      } catch (error) {
        console.log(`⚠️  无法获取链上用户余额: ${error}`);
      }

      // 6. 获取交易详情
      console.log(`\n📋 获取交易详情...`);
      try {
        const tx = await provider.getTransaction(event.transactionHash);
        if (tx) {
          console.log(`   发送者: ${tx.from}`);
          console.log(`   接收者: ${tx.to}`);
          console.log(`   发送金额: ${ethers.formatEther(tx.value || 0)} PHRS`);
          console.log(`   Gas价格: ${tx.gasPrice ? ethers.formatUnits(tx.gasPrice, 'gwei') : '未知'} Gwei`);
          console.log(`   Gas限制: ${tx.gasLimit?.toString() || '未知'}`);
        }
      } catch (error) {
        console.log(`⚠️  无法获取交易详情: ${error}`);
      }
    }

    // 7. 总结
    console.log(`\n📊 总结:`);
    console.log(`=====================================`);
    console.log(`✅ 在区块 ${targetBlock} 中找到 ${events.length} 个充值事件`);
    
    const unregisteredEvents = [];
    for (const event of events) {
      if ('args' in event && event.args) {
        const userAddress = event.args[0] as string;
        const userWallet = await UserWallet.findOne({
          where: { phrsWalletAddress: userAddress.toLowerCase() }
        });
        if (!userWallet) {
          unregisteredEvents.push({
            address: userAddress,
            amount: ethers.formatEther(event.args[1] as bigint),
            txHash: event.transactionHash
          });
        }
      }
    }

    if (unregisteredEvents.length > 0) {
      console.log(`⚠️  其中 ${unregisteredEvents.length} 个来自未注册的地址:`);
      unregisteredEvents.forEach((evt, index) => {
        console.log(`   ${index + 1}. ${evt.address} - ${evt.amount} PHRS (${evt.txHash})`);
      });
      console.log(`\n💡 建议:`);
      console.log(`   1. 这些充值不会被自动处理，因为用户未在系统中注册`);
      console.log(`   2. 如果需要处理，可以手动创建用户钱包记录`);
      console.log(`   3. 或者实现一个机制来处理未注册用户的充值`);
    } else {
      console.log(`✅ 所有充值都来自已注册的用户`);
    }

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

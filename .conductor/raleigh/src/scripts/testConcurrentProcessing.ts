#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试并发处理和错误恢复');
  console.log('==========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';

    // 1. 清理现有记录
    console.log(`\n🗑️  清理现有记录...`);
    await PhrsDeposit.destroy({ where: { transactionHash: targetTx } });

    // 2. 模拟并发处理（快速连续处理同一区块）
    console.log(`\n🔄 模拟并发处理...`);
    const promises = [];
    for (let i = 0; i < 3; i++) {
      promises.push(
        phrsDepositService.testProcessBlocks(13805521).catch(error => {
          console.log(`   并发处理 ${i + 1} 遇到错误:`, error.message);
          return `error-${i}`;
        })
      );
    }

    const results = await Promise.all(promises);
    console.log(`📊 并发处理结果:`, results);

    // 3. 检查最终记录数
    const finalRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 最终记录数: ${finalRecords.length}`);

    if (finalRecords.length === 1) {
      console.log('✅ 并发处理测试通过：只创建了一条记录');
      console.log(`   记录状态: ${finalRecords[0].status}`);
      console.log(`   创建时间: ${finalRecords[0].createdAt}`);
    } else {
      console.log(`❌ 并发处理测试失败：创建了 ${finalRecords.length} 条记录`);
    }

    // 4. 测试错误恢复（再次处理）
    console.log(`\n🔄 测试错误恢复...`);
    for (let i = 0; i < 3; i++) {
      console.log(`   第 ${i + 1} 次恢复处理...`);
      try {
        await phrsDepositService.testProcessBlocks(13805521);
        console.log(`   ✅ 第 ${i + 1} 次处理成功`);
      } catch (error: any) {
        console.log(`   ❌ 第 ${i + 1} 次处理失败:`, error.message);
      }
    }

    // 5. 最终验证
    const recoveryRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 恢复后记录数: ${recoveryRecords.length}`);

    if (recoveryRecords.length === 1) {
      console.log('✅ 错误恢复测试通过：记录数保持不变');
    } else {
      console.log(`❌ 错误恢复测试失败：记录数变为 ${recoveryRecords.length}`);
    }

    // 6. 总结
    console.log(`\n📋 测试总结:`);
    console.log(`   - 并发处理: ${finalRecords.length === 1 ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   - 错误恢复: ${recoveryRecords.length === 1 ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   - 数据一致性: ${finalRecords.length === recoveryRecords.length ? '✅ 通过' : '❌ 失败'}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

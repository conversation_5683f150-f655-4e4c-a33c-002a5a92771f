#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { ethers } from 'ethers';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🔍 PHRS服务状态检查');
  console.log('====================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 检查环境变量
    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL;

    console.log('\n📋 环境变量检查:');
    console.log(`   PHRS_DEPOSIT_CONTRACT_ADDRESS: ${contractAddress || '❌ 未设置'}`);
    console.log(`   PHAROS_RPC_URL: ${rpcUrl || '❌ 未设置'}`);

    if (!contractAddress) {
      console.log('❌ 合约地址未设置，无法继续检查');
      return;
    }

    // 检查网络连接
    console.log('\n📡 网络连接检查:');
    const provider = new ethers.JsonRpcProvider(rpcUrl || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47');
    
    try {
      const network = await provider.getNetwork();
      const blockNumber = await provider.getBlockNumber();
      console.log(`   ✅ 网络: ${network.name} (Chain ID: ${network.chainId})`);
      console.log(`   ✅ 当前区块: ${blockNumber}`);
    } catch (error) {
      console.log(`   ❌ 网络连接失败: ${error}`);
      return;
    }

    // 检查合约
    console.log('\n📋 合约状态检查:');
    const contractABI = [
      "function getContractInfo() external view returns (uint256, uint256, uint256, uint256)",
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, provider);

    try {
      const contractInfo = await contract.getContractInfo();
      console.log(`   ✅ 合约连接成功`);
      console.log(`   📊 合约总余额: ${ethers.formatEther(contractInfo[2])} PHRS`);
      console.log(`   📊 合法充值总额: ${ethers.formatEther(contractInfo[3])} PHRS`);
    } catch (error) {
      console.log(`   ❌ 合约连接失败: ${error}`);
      return;
    }

    // 检查数据库记录
    console.log('\n💾 数据库记录检查:');
    const totalRecords = await PhrsDeposit.count();
    console.log(`   📊 总记录数: ${totalRecords}`);

    if (totalRecords > 0) {
      const latestRecord = await PhrsDeposit.findOne({
        order: [['createdAt', 'DESC']],
        limit: 1
      });

      if (latestRecord) {
        console.log(`   📝 最新记录:`);
        console.log(`      用户: ${latestRecord.userAddress}`);
        console.log(`      金额: ${ethers.formatEther(latestRecord.amount)} PHRS`);
        console.log(`      区块: ${latestRecord.blockNumber}`);
        console.log(`      时间: ${latestRecord.createdAt}`);
      }
    }

    // 检查最近的链上事件
    console.log('\n🔍 链上事件检查:');
    const currentBlock = await provider.getBlockNumber();
    const fromBlock = Math.max(0, currentBlock - 100); // 检查最近100个区块

    try {
      const filter = contract.filters.Deposit();
      const events = await contract.queryFilter(filter, fromBlock, currentBlock);
      
      console.log(`   📊 最近100个区块中的事件数: ${events.length}`);
      
      if (events.length > 0) {
        const latestEvent = events[events.length - 1];
        if ('args' in latestEvent && latestEvent.args) {
          console.log(`   📝 最新链上事件:`);
          console.log(`      区块: ${latestEvent.blockNumber}`);
          console.log(`      交易: ${latestEvent.transactionHash}`);
          console.log(`      用户: ${latestEvent.args[0]}`);
          console.log(`      金额: ${ethers.formatEther(latestEvent.args[1])} PHRS`);
        }
      }
    } catch (error) {
      console.log(`   ❌ 查询链上事件失败: ${error}`);
    }

    // 检查数据一致性
    console.log('\n🔍 数据一致性检查:');
    
    // 获取数据库中的总金额
    const dbTotal = await PhrsDeposit.sum('amount') || '0';
    const dbTotalEther = ethers.formatEther(dbTotal);
    
    // 获取合约中的合法充值总额
    const contractInfo = await contract.getContractInfo();
    const contractTotalEther = ethers.formatEther(contractInfo[3]);
    
    console.log(`   💾 数据库总金额: ${dbTotalEther} PHRS`);
    console.log(`   🔗 合约合法充值: ${contractTotalEther} PHRS`);
    
    const difference = parseFloat(contractTotalEther) - parseFloat(dbTotalEther);
    if (Math.abs(difference) < 0.001) {
      console.log(`   ✅ 数据一致性正常`);
    } else {
      console.log(`   ⚠️  数据不一致，差异: ${difference.toFixed(6)} PHRS`);
    }

    console.log('\n✅ 检查完成');

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

import { ethers } from 'ethers';

async function privateKeyToAddress() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('用法: npm run private-to-address <私钥>');
    console.log('示例: npm run private-to-address 0x1234567890abcdef...');
    process.exit(1);
  }

  const privateKey = args[0];

  try {
    // 验证私钥格式
    if (!privateKey.startsWith('0x')) {
      throw new Error('私钥必须以 0x 开头');
    }

    if (privateKey.length !== 66) {
      throw new Error('私钥长度必须为 64 个字符（不包括 0x 前缀）');
    }

    // 从私钥创建钱包
    const wallet = new ethers.Wallet(privateKey);
    
    console.log('私钥:', privateKey);
    console.log('地址:', wallet.address);
    
  } catch (error) {
    console.error('错误:', error instanceof Error ? error.message : '未知错误');
    process.exit(1);
  }
}

privateKeyToAddress();
// src/scripts/checkUserAddress.ts
import { sequelize } from '../config/db';
import { UserWallet, PhrsDeposit } from '../models';
import { Op } from 'sequelize';

/**
 * 检查特定用户地址的情况
 */
async function checkUserAddress() {
  const targetAddress = '******************************************';
  
  console.log('🔍 检查用户地址情况...');
  console.log('================================================');
  console.log(`目标地址: ${targetAddress}`);

  try {
    // 1. 检查数据库连接
    console.log('\n1. 检查数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');

    // 2. 检查原始地址（不转换大小写）
    console.log('\n2. 检查原始地址（不转换大小写）...');
    const originalWallet = await UserWallet.findOne({
      where: { phrsWalletAddress: targetAddress }
    });
    console.log(`原始地址查询结果: ${originalWallet ? '找到' : '未找到'}`);
    if (originalWallet) {
      console.log(`   钱包ID: ${originalWallet.id}`);
      console.log(`   用户ID: ${originalWallet.userId}`);
      console.log(`   存储的地址: ${originalWallet.phrsWalletAddress}`);
      console.log(`   PHRS余额: ${originalWallet.phrsBalance || '0'}`);
    }

    // 3. 检查小写地址
    console.log('\n3. 检查小写地址...');
    const lowercaseAddress = targetAddress.toLowerCase();
    console.log(`小写地址: ${lowercaseAddress}`);
    
    const lowercaseWallet = await UserWallet.findOne({
      where: { phrsWalletAddress: lowercaseAddress }
    });
    console.log(`小写地址查询结果: ${lowercaseWallet ? '找到' : '未找到'}`);
    if (lowercaseWallet) {
      console.log(`   钱包ID: ${lowercaseWallet.id}`);
      console.log(`   用户ID: ${lowercaseWallet.userId}`);
      console.log(`   存储的地址: ${lowercaseWallet.phrsWalletAddress}`);
      console.log(`   PHRS余额: ${lowercaseWallet.phrsBalance || '0'}`);
    }

    // 4. 检查大写地址
    console.log('\n4. 检查大写地址...');
    const uppercaseAddress = targetAddress.toUpperCase();
    console.log(`大写地址: ${uppercaseAddress}`);
    
    const uppercaseWallet = await UserWallet.findOne({
      where: { phrsWalletAddress: uppercaseAddress }
    });
    console.log(`大写地址查询结果: ${uppercaseWallet ? '找到' : '未找到'}`);

    // 5. 模糊查询（LIKE）
    console.log('\n5. 模糊查询（LIKE）...');
    const likeWallets = await UserWallet.findAll({
      where: {
        phrsWalletAddress: {
          [Op.like]: `%${targetAddress.slice(2)}%` // 去掉0x前缀
        }
      }
    });
    console.log(`模糊查询结果: 找到 ${likeWallets.length} 个匹配`);
    likeWallets.forEach((wallet, index) => {
      console.log(`   匹配 ${index + 1}:`);
      console.log(`     钱包ID: ${wallet.id}`);
      console.log(`     用户ID: ${wallet.userId}`);
      console.log(`     存储的地址: ${wallet.phrsWalletAddress}`);
      console.log(`     PHRS余额: ${wallet.phrsBalance || '0'}`);
    });

    // 6. 检查所有有PHRS地址的钱包
    console.log('\n6. 检查所有有PHRS地址的钱包...');
    const allPhrsWallets = await UserWallet.findAll({
      where: {
        phrsWalletAddress: { [Op.ne]: null as any }
      },
      attributes: ['id', 'userId', 'phrsWalletAddress', 'phrsBalance'],
      limit: 10
    });
    console.log(`总共找到 ${allPhrsWallets.length} 个有PHRS地址的钱包（显示前10个）:`);
    allPhrsWallets.forEach((wallet, index) => {
      console.log(`   钱包 ${index + 1}:`);
      console.log(`     钱包ID: ${wallet.id}`);
      console.log(`     用户ID: ${wallet.userId}`);
      console.log(`     PHRS地址: ${wallet.phrsWalletAddress}`);
      console.log(`     PHRS余额: ${wallet.phrsBalance || '0'}`);
    });

    // 7. 检查充值记录中的地址
    console.log('\n7. 检查充值记录中的地址...');
    const depositRecords = await PhrsDeposit.findAll({
      where: {
        userAddress: [targetAddress, lowercaseAddress, uppercaseAddress]
      },
      attributes: ['id', 'walletId', 'userAddress', 'amount', 'status', 'transactionHash'],
      limit: 5
    });
    console.log(`充值记录查询结果: 找到 ${depositRecords.length} 条记录`);
    depositRecords.forEach((record, index) => {
      console.log(`   记录 ${index + 1}:`);
      console.log(`     记录ID: ${record.id}`);
      console.log(`     钱包ID: ${record.walletId || 'null'}`);
      console.log(`     用户地址: ${record.userAddress}`);
      console.log(`     金额: ${record.amount}`);
      console.log(`     状态: ${record.status}`);
      console.log(`     交易哈希: ${record.transactionHash}`);
    });

    // 8. 检查地址格式和长度
    console.log('\n8. 检查地址格式和长度...');
    console.log(`   原始地址长度: ${targetAddress.length}`);
    console.log(`   是否以0x开头: ${targetAddress.startsWith('0x')}`);
    console.log(`   去掉0x后长度: ${targetAddress.slice(2).length}`);
    console.log(`   是否为有效的十六进制: ${/^0x[a-fA-F0-9]{40}$/.test(targetAddress)}`);

    // 9. 尝试创建测试记录（如果地址确实不存在）
    if (!originalWallet && !lowercaseWallet && !uppercaseWallet) {
      console.log('\n9. 地址确实不存在，这可能是问题所在');
      console.log('   建议检查：');
      console.log('   1. 用户是否已经绑定了PHRS钱包地址');
      console.log('   2. 绑定时是否使用了正确的地址格式');
      console.log('   3. 是否存在大小写不匹配的问题');
    } else {
      console.log('\n9. 地址存在，检查充值服务的查询逻辑');
      const foundWallet = originalWallet || lowercaseWallet || uppercaseWallet;
      console.log(`   找到的钱包信息:`);
      console.log(`     钱包ID: ${foundWallet!.id}`);
      console.log(`     存储的地址: ${foundWallet!.phrsWalletAddress}`);
      console.log(`     目标地址: ${targetAddress}`);
      console.log(`     地址匹配: ${foundWallet!.phrsWalletAddress === targetAddress}`);
      console.log(`     小写匹配: ${foundWallet!.phrsWalletAddress === lowercaseAddress}`);
    }

    console.log('\n🎉 检查完成！');
    console.log('================================================');

  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    throw error;
  }
}

// 运行检查
if (require.main === module) {
  checkUserAddress()
    .then(() => {
      console.log('\n✅ 地址检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 地址检查失败:', error);
      process.exit(1);
    });
}

export { checkUserAddress };

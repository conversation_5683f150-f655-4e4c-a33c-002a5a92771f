// src/scripts/testTransactionFix.ts
import { sequelize } from '../config/db';
import { PhrsDeposit } from '../models';
import { ethers } from 'ethers';

/**
 * 测试事务修复 - 模拟并发创建相同记录的情况
 */
async function testTransactionFix() {
  console.log('🧪 开始测试事务修复...');
  console.log('================================================');

  try {
    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...');
    await sequelize.authenticate();
    console.log('✅ 数据库连接正常');

    // 2. 生成测试数据
    const testTxHash = `0x${Math.random().toString(16).substr(2, 64)}`;
    const testUserAddress = `0x${Math.random().toString(16).substr(2, 40)}`;
    const testAmount = '1.5';
    const testBlockNumber = Math.floor(Math.random() * 1000000);
    
    console.log('\n2. 生成测试数据...');
    console.log(`   交易哈希: ${testTxHash}`);
    console.log(`   用户地址: ${testUserAddress}`);
    console.log(`   金额: ${testAmount}`);
    console.log(`   区块号: ${testBlockNumber}`);

    // 3. 测试单个创建（应该成功）
    console.log('\n3. 测试单个记录创建...');
    
    const createSingleRecord = async () => {
      return await sequelize.transaction(async (t) => {
        // 检查是否已存在
        const existing = await PhrsDeposit.findOne({
          where: { transactionHash: testTxHash },
          transaction: t
        });

        if (existing) {
          console.log('   记录已存在，跳过创建');
          return 'EXISTS';
        }

        // 创建记录
        await PhrsDeposit.create({
          walletId: null,
          userAddress: testUserAddress,
          amount: testAmount,
          transactionHash: testTxHash,
          blockNumber: testBlockNumber,
          blockTimestamp: new Date(),
          contractAddress: '******************************************',
          status: 'FAILED',
          confirmations: 1,
          processedAt: new Date(),
          errorMessage: 'Test record'
        }, { transaction: t });

        return 'CREATED';
      });
    };

    const result1 = await createSingleRecord();
    console.log(`   结果: ${result1}`);

    // 4. 测试重复创建（应该检测到已存在）
    console.log('\n4. 测试重复记录创建...');
    const result2 = await createSingleRecord();
    console.log(`   结果: ${result2}`);

    // 5. 测试并发创建（模拟竞争条件）
    console.log('\n5. 测试并发创建...');
    
    const testTxHash2 = `0x${Math.random().toString(16).substr(2, 64)}`;
    console.log(`   新交易哈希: ${testTxHash2}`);
    
    const createConcurrentRecord = async (id: number) => {
      try {
        return await sequelize.transaction(async (t) => {
          console.log(`   并发任务 ${id}: 开始事务`);
          
          // 模拟一些延迟
          await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
          
          // 检查是否已存在
          const existing = await PhrsDeposit.findOne({
            where: { transactionHash: testTxHash2 },
            transaction: t
          });

          if (existing) {
            console.log(`   并发任务 ${id}: 记录已存在，跳过创建`);
            return `EXISTS_${id}`;
          }

          console.log(`   并发任务 ${id}: 创建记录`);
          
          // 创建记录
          await PhrsDeposit.create({
            walletId: null,
            userAddress: testUserAddress,
            amount: testAmount,
            transactionHash: testTxHash2,
            blockNumber: testBlockNumber + 1,
            blockTimestamp: new Date(),
            contractAddress: '******************************************',
            status: 'FAILED',
            confirmations: 1,
            processedAt: new Date(),
            errorMessage: `Test concurrent record ${id}`
          }, { transaction: t });

          console.log(`   并发任务 ${id}: 记录创建成功`);
          return `CREATED_${id}`;
        });
      } catch (error: any) {
        console.log(`   并发任务 ${id}: 发生错误 - ${error.message}`);
        if (error.name === 'SequelizeUniqueConstraintError') {
          return `CONFLICT_${id}`;
        }
        throw error;
      }
    };

    // 启动多个并发任务
    const concurrentPromises = [];
    for (let i = 1; i <= 5; i++) {
      concurrentPromises.push(createConcurrentRecord(i));
    }

    const concurrentResults = await Promise.all(concurrentPromises);
    console.log('   并发测试结果:', concurrentResults);

    // 6. 验证最终状态
    console.log('\n6. 验证最终状态...');
    
    const finalRecord1 = await PhrsDeposit.findOne({
      where: { transactionHash: testTxHash }
    });
    
    const finalRecord2 = await PhrsDeposit.findOne({
      where: { transactionHash: testTxHash2 }
    });

    console.log(`   测试记录1: ${finalRecord1 ? '存在' : '不存在'}`);
    console.log(`   测试记录2: ${finalRecord2 ? '存在' : '不存在'}`);

    // 7. 清理测试数据
    console.log('\n7. 清理测试数据...');
    
    const deletedCount1 = await PhrsDeposit.destroy({
      where: { transactionHash: testTxHash }
    });
    
    const deletedCount2 = await PhrsDeposit.destroy({
      where: { transactionHash: testTxHash2 }
    });

    console.log(`   删除记录数: ${deletedCount1 + deletedCount2}`);

    console.log('\n🎉 测试完成！');
    console.log('================================================');
    console.log('✅ 事务修复测试通过');
    console.log('📊 测试结果总结:');
    console.log(`   - 单个创建: ${result1}`);
    console.log(`   - 重复创建: ${result2}`);
    console.log(`   - 并发创建: ${concurrentResults.length} 个任务完成`);
    console.log(`   - 唯一约束: 正常工作`);
    console.log(`   - 事务处理: 无回滚错误`);

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  testTransactionFix()
    .then(() => {
      console.log('\n✅ 事务修复测试完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 事务修复测试失败:', error);
      process.exit(1);
    });
}

export { testTransactionFix };

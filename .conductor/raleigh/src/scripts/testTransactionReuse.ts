#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试事务重用错误修复');
  console.log('========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';

    // 1. 清理现有记录以便重新测试
    console.log(`\n🗑️  清理现有记录...`);
    const deletedCount = await PhrsDeposit.destroy({
      where: { transactionHash: targetTx }
    });
    console.log(`✅ 删除了 ${deletedCount} 条记录`);

    // 2. 第一次处理 - 应该创建新记录
    console.log(`\n🔄 第一次处理区块 13805521...`);
    try {
      await phrsDepositService.testProcessBlocks(13805521);
      console.log('✅ 第一次处理成功');
    } catch (error: any) {
      console.log(`❌ 第一次处理失败: ${error.message}`);
    }

    // 3. 检查记录
    const records1 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 第一次处理后记录数: ${records1.length}`);
    if (records1.length > 0) {
      records1.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
        console.log(`      错误信息: ${record.errorMessage || '无'}`);
      });
    }

    // 4. 快速连续处理多次 - 测试事务重用问题
    console.log(`\n🔄 快速连续处理测试...`);
    const rapidPromises = [];
    for (let i = 0; i < 5; i++) {
      rapidPromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `rapid-${i}-success`;
        }).catch((error: any) => {
          return `rapid-${i}-error: ${error.message}`;
        })
      );
    }

    const rapidResults = await Promise.all(rapidPromises);
    console.log('📊 快速连续处理结果:');
    rapidResults.forEach((result, index) => {
      if (typeof result === 'string' && result.includes('error')) {
        console.log(`   ${index + 1}. ❌ ${result}`);
      } else {
        console.log(`   ${index + 1}. ✅ ${result}`);
      }
    });

    // 5. 检查最终记录数
    const records2 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 快速处理后记录数: ${records2.length}`);

    // 6. 极限并发测试
    console.log(`\n🔄 极限并发测试 (20个并发)...`);
    const extremePromises = [];
    for (let i = 0; i < 20; i++) {
      extremePromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `extreme-${i}-success`;
        }).catch((error: any) => {
          return `extreme-${i}-error: ${error.message}`;
        })
      );
    }

    const extremeResults = await Promise.all(extremePromises);
    console.log('📊 极限并发测试结果:');
    let extremeSuccessCount = 0;
    let extremeErrorCount = 0;
    
    extremeResults.forEach((result, index) => {
      if (typeof result === 'string' && result.includes('error')) {
        console.log(`   ${index + 1}. ❌ ${result}`);
        extremeErrorCount++;
      } else {
        console.log(`   ${index + 1}. ✅ ${result}`);
        extremeSuccessCount++;
      }
    });

    console.log(`\n📊 极限并发统计:`);
    console.log(`   - 成功: ${extremeSuccessCount}`);
    console.log(`   - 错误: ${extremeErrorCount}`);

    // 7. 最终验证
    const finalRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 最终记录数: ${finalRecords.length}`);

    if (finalRecords.length > 0) {
      finalRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
        console.log(`      创建时间: ${record.createdAt}`);
        console.log(`      错误信息: ${record.errorMessage || '无'}`);
      });
    }

    // 8. 测试结果评估
    console.log(`\n📋 测试结果评估:`);
    
    const hasTransactionReuseErrors = extremeResults.some(result => 
      typeof result === 'string' && 
      result.includes('rollback has been called on this transaction')
    );

    const hasFinishedTransactionErrors = extremeResults.some(result => 
      typeof result === 'string' && 
      result.includes('Transaction cannot be rolled back because it has been finished')
    );

    if (!hasTransactionReuseErrors && !hasFinishedTransactionErrors) {
      console.log('✅ 事务重用错误修复成功：');
      console.log('   - 没有 "rollback has been called" 错误');
      console.log('   - 没有 "Transaction cannot be rolled back" 错误');
      console.log('   - 并发处理正常工作');
    } else {
      console.log('❌ 仍然存在事务错误：');
      if (hasTransactionReuseErrors) {
        console.log('   - 发现事务重用错误');
      }
      if (hasFinishedTransactionErrors) {
        console.log('   - 发现事务状态错误');
      }
    }

    if (finalRecords.length === 1) {
      console.log('✅ 数据一致性测试通过：只有一条记录');
    } else {
      console.log(`❌ 数据一致性测试失败：有 ${finalRecords.length} 条记录`);
    }

    console.log('\n🎉 事务重用错误测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { ethers } from 'ethers';

// 加载环境变量
dotenv.config();

async function main() {
  const targetBlock = 13805521; // 可以修改这个值来测试其他区块
  
  console.log(`🔍 快速检查区块 ${targetBlock}`);
  console.log('=========================');

  try {
    const contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS;
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://api.zan.top/node/v1/pharos/testnet/d42f5024864e431388c182f9e5052d47';

    if (!contractAddress) {
      console.log('❌ 请设置 PHRS_DEPOSIT_CONTRACT_ADDRESS 环境变量');
      return;
    }

    // 连接网络
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const currentBlock = await provider.getBlockNumber();
    
    console.log(`📡 当前区块: ${currentBlock}`);
    console.log(`🎯 检查区块: ${targetBlock}`);

    if (targetBlock > currentBlock) {
      console.log('❌ 目标区块还未产生');
      return;
    }

    // 连接合约
    const contractABI = [
      "event Deposit(address indexed user, uint256 amount, uint256 timestamp)"
    ];

    const contract = new ethers.Contract(contractAddress, contractABI, provider);

    // 查询事件
    console.log(`\n🔍 查询事件...`);
    const filter = contract.filters.Deposit();
    const events = await contract.queryFilter(filter, targetBlock, targetBlock);
    
    console.log(`📡 找到 ${events.length} 个Deposit事件`);

    if (events.length === 0) {
      console.log('⚠️  该区块中没有Deposit事件');
      
      // 检查前后几个区块
      console.log('\n🔍 检查邻近区块...');
      const rangeEvents = await contract.queryFilter(filter, targetBlock - 2, targetBlock + 2);
      console.log(`📡 区块 ${targetBlock - 2} 到 ${targetBlock + 2} 中找到 ${rangeEvents.length} 个事件`);
      
      if (rangeEvents.length > 0) {
        rangeEvents.forEach((event, index) => {
          if ('args' in event && event.args) {
            const isTarget = event.blockNumber === targetBlock;
            console.log(`   ${index + 1}. 区块: ${event.blockNumber} ${isTarget ? '🎯' : ''}`);
            console.log(`      用户: ${event.args[0]}`);
            console.log(`      金额: ${ethers.formatEther(event.args[1])} PHRS`);
            console.log(`      交易: ${event.transactionHash}`);
          }
        });
      }
      return;
    }

    // 显示事件详情
    console.log(`\n📝 事件详情:`);
    events.forEach((event, index) => {
      if ('args' in event && event.args) {
        console.log(`\n事件 ${index + 1}:`);
        console.log(`  👤 用户: ${event.args[0]}`);
        console.log(`  💰 金额: ${ethers.formatEther(event.args[1])} PHRS`);
        console.log(`  ⏰ 时间: ${new Date(Number(event.args[2]) * 1000).toLocaleString()}`);
        console.log(`  📦 区块: ${event.blockNumber}`);
        console.log(`  🔗 交易: ${event.transactionHash}`);
      }
    });

    console.log(`\n✅ 监控服务测试数据准备就绪！`);
    console.log(`💡 现在可以检查监控服务是否能检测到这些事件`);

  } catch (error) {
    console.error('❌ 检查失败:', error);
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试未注册用户事务隔离修复');
  console.log('==============================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';

    // 1. 清理现有记录
    console.log(`\n🗑️  清理现有记录...`);
    const deletedCount = await PhrsDeposit.destroy({
      where: { transactionHash: targetTx }
    });
    console.log(`✅ 删除了 ${deletedCount} 条记录`);

    // 2. 第一次处理 - 应该创建未注册用户记录
    console.log(`\n🔄 第一次处理区块 13805521...`);
    try {
      await phrsDepositService.testProcessBlocks(13805521);
      console.log('✅ 第一次处理成功');
    } catch (error: any) {
      console.log(`❌ 第一次处理失败: ${error.message}`);
      
      // 检查是否是我们要修复的错误
      if (error.message.includes('rollback has been called on this transaction')) {
        console.log('🚨 发现事务重用错误！修复未完全生效');
      }
    }

    // 3. 检查记录
    const records1 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 第一次处理后记录数: ${records1.length}`);
    if (records1.length > 0) {
      records1.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
        console.log(`      错误信息: ${record.errorMessage || '无'}`);
      });
    }

    // 4. 连续快速处理测试
    console.log(`\n🔄 连续快速处理测试 (10次)...`);
    const rapidPromises = [];
    for (let i = 0; i < 10; i++) {
      rapidPromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `rapid-${i}-success`;
        }).catch((error: any) => {
          // 检查是否是事务重用错误
          if (error.message.includes('rollback has been called on this transaction')) {
            return `rapid-${i}-TRANSACTION-REUSE-ERROR: ${error.message}`;
          }
          return `rapid-${i}-other-error: ${error.message}`;
        })
      );
    }

    const rapidResults = await Promise.all(rapidPromises);
    console.log('📊 快速处理结果:');
    let transactionReuseErrors = 0;
    let otherErrors = 0;
    let successes = 0;

    rapidResults.forEach((result, index) => {
      if (typeof result === 'string') {
        if (result.includes('TRANSACTION-REUSE-ERROR')) {
          console.log(`   ${index + 1}. 🚨 ${result}`);
          transactionReuseErrors++;
        } else if (result.includes('error')) {
          console.log(`   ${index + 1}. ❌ ${result}`);
          otherErrors++;
        } else {
          console.log(`   ${index + 1}. ✅ ${result}`);
          successes++;
        }
      }
    });

    console.log(`\n📊 快速处理统计:`);
    console.log(`   - 成功: ${successes}`);
    console.log(`   - 事务重用错误: ${transactionReuseErrors}`);
    console.log(`   - 其他错误: ${otherErrors}`);

    // 5. 检查最终记录数
    const records2 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 快速处理后记录数: ${records2.length}`);

    // 6. 超高并发测试
    console.log(`\n🔄 超高并发测试 (50个并发)...`);
    const extremePromises = [];
    for (let i = 0; i < 50; i++) {
      extremePromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `extreme-${i}-success`;
        }).catch((error: any) => {
          if (error.message.includes('rollback has been called on this transaction')) {
            return `extreme-${i}-TRANSACTION-REUSE-ERROR`;
          }
          return `extreme-${i}-other-error`;
        })
      );
    }

    const extremeResults = await Promise.all(extremePromises);
    
    let extremeTransactionErrors = 0;
    let extremeOtherErrors = 0;
    let extremeSuccesses = 0;

    extremeResults.forEach((result) => {
      if (typeof result === 'string') {
        if (result.includes('TRANSACTION-REUSE-ERROR')) {
          extremeTransactionErrors++;
        } else if (result.includes('error')) {
          extremeOtherErrors++;
        } else {
          extremeSuccesses++;
        }
      }
    });

    console.log(`📊 超高并发统计:`);
    console.log(`   - 成功: ${extremeSuccesses}`);
    console.log(`   - 事务重用错误: ${extremeTransactionErrors}`);
    console.log(`   - 其他错误: ${extremeOtherErrors}`);

    // 7. 最终验证
    const finalRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 最终记录数: ${finalRecords.length}`);

    // 8. 测试结果评估
    console.log(`\n📋 修复效果评估:`);
    
    const totalTransactionErrors = transactionReuseErrors + extremeTransactionErrors;
    
    if (totalTransactionErrors === 0) {
      console.log('🎉 事务重用错误修复成功！');
      console.log('   ✅ 没有发现任何事务重用错误');
      console.log('   ✅ 未注册用户处理完全隔离');
      console.log('   ✅ 并发处理安全稳定');
    } else {
      console.log('❌ 事务重用错误仍然存在：');
      console.log(`   🚨 发现 ${totalTransactionErrors} 个事务重用错误`);
      console.log('   需要进一步修复');
    }

    if (finalRecords.length === 1) {
      console.log('✅ 数据一致性测试通过：只有一条记录');
    } else {
      console.log(`❌ 数据一致性测试失败：有 ${finalRecords.length} 条记录`);
    }

    console.log('\n🎯 总结:');
    console.log(`   - 总测试次数: ${1 + rapidPromises.length + extremePromises.length}`);
    console.log(`   - 事务重用错误: ${totalTransactionErrors}`);
    console.log(`   - 成功处理: ${1 + successes + extremeSuccesses}`);
    console.log(`   - 最终记录数: ${finalRecords.length}`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

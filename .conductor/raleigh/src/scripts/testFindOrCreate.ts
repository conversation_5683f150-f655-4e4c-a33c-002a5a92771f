#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试 findOrCreate 修复');
  console.log('========================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';

    // 1. 删除现有记录
    console.log(`\n🗑️  删除现有记录: ${targetTx}`);
    const deletedCount = await PhrsDeposit.destroy({
      where: { transactionHash: targetTx }
    });
    console.log(`✅ 删除了 ${deletedCount} 条记录`);

    // 2. 处理区块，应该创建新记录
    console.log(`\n🔄 第一次处理区块 13805521...`);
    await phrsDepositService.testProcessBlocks(13805521);

    // 3. 检查记录
    const records1 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 第一次处理后记录数: ${records1.length}`);
    if (records1.length > 0) {
      console.log(`   记录状态: ${records1[0].status}`);
      console.log(`   错误信息: ${records1[0].errorMessage || '无'}`);
    }

    // 4. 再次处理相同区块，应该跳过
    console.log(`\n🔄 第二次处理区块 13805521...`);
    await phrsDepositService.testProcessBlocks(13805521);

    // 5. 检查记录数量是否保持不变
    const records2 = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 第二次处理后记录数: ${records2.length}`);

    // 6. 验证结果
    if (records1.length === 1 && records2.length === 1) {
      console.log('\n✅ findOrCreate 测试通过：');
      console.log('   - 第一次处理创建了记录');
      console.log('   - 第二次处理正确跳过');
      console.log('   - 没有重复记录');
    } else {
      console.log('\n❌ findOrCreate 测试失败：');
      console.log(`   - 第一次处理记录数: ${records1.length}`);
      console.log(`   - 第二次处理记录数: ${records2.length}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});

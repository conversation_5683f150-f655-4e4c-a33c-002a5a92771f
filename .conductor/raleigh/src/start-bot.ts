// start-bot.ts
import bot from './bot';
import { logger } from './utils/logger';

// 启动机器人
bot.start({
  onStart: () => {
    logger.info('Telegram 机器人已启动');
    console.log('Telegram 机器人已启动');
  },
}).catch(error => {
  logger.error('启动 Telegram 机器人时出错', {
    error: error instanceof Error ? error.message : '未知错误',
    stack: error instanceof Error ? error.stack : undefined
  });
  console.error('启动 Telegram 机器人时出错:', error);
  process.exit(1);
});
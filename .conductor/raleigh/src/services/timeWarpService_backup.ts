import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { VipMembership } from '../models/VipMembership';
import { ActiveBooster } from '../models/ActiveBooster';
import { TimeWarpHistory } from '../models/TimeWarpHistory';
import { createBigNumber, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import { Op } from 'sequelize';
import dayjs from 'dayjs';

export interface TimeWarpResult {
  gemsEarned: number;
  milkProduced: number;
  milkProcessed: number;
  farmProductionPerSecond: number;
  deliveryProcessingPerSecond: number;
  hasVip: boolean;
  hasSpeedBoost: boolean;
  speedBoostMultiplier: number;
}

export class TimeWarpService {
  /**
   * 计算时间跳跃收益（新的配置驱动方式）
   * 使用与离线奖励相同的 farm_configs 表计算逻辑
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 时间跳跃收益详情
   */
  static async calculateTimeWarpRewards(walletId: number, hours: number): Promise<TimeWarpResult> {
    console.log(`🧮 开始计算时间跳跃收益（配置驱动方式）: walletId=${walletId}, hours=${hours}`);

    // 使用配置驱动的计算方式
    return await this.calculateTimeWarpRewardsWithConfig(walletId, hours);
  }

  /**
   * 基于 farm_configs 表的时间跳跃收益计算
   * 与累积离线奖励使用相同的计算逻辑
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 时间跳跃收益详情
   */
  static async calculateTimeWarpRewardsWithConfig(walletId: number, hours: number): Promise<TimeWarpResult> {
    try {
      console.log(`🧮 开始基于配置表的时间跳跃收益计算: walletId=${walletId}, hours=${hours}`);

      const timeInSeconds = hours * 3600;

      // 获取用户所有已解锁的农场区域
      const farmPlots = await FarmPlot.findAll({
        where: {
          walletId,
          isUnlocked: true
        },
        order: [['plotNumber', 'ASC']]
      });

      console.log(`🏡 找到 ${farmPlots.length} 个已解锁的农场区块`);

      if (farmPlots.length === 0) {
        console.log(`⚠️ 用户没有已解锁的农场区块`);
        return this.createEmptyTimeWarpResult();
      }

      // 获取激活的农场配置
      const { FarmConfig } = require('../models/FarmConfig');
      const activeConfigs = await FarmConfig.getActiveConfigs();

      if (activeConfigs.length === 0) {
        console.warn('没有找到激活的农场配置，使用降级方案');
        return this.calculateTimeWarpRewardsLegacy(walletId, hours);
      }

      // 创建配置映射 grade -> config
      const configMap = new Map<number, any>();
      activeConfigs.forEach((config: any) => {
        configMap.set(config.grade, config);
      });

      // 检查VIP状态
      const vipMembership = await VipMembership.findOne({
        where: {
          walletId,
          isActive: true,
          endTime: { [Op.gt]: dayjs().toDate() }
        }
      });
      const hasVip = !!vipMembership;
      console.log(`👑 VIP状态: ${hasVip}`);

      // 检查速度加成状态
      const activeSpeedBoost = await ActiveBooster.findOne({
        where: {
          walletId,
          type: 'speed_boost',
          status: 'active',
          endTime: { [Op.gt]: dayjs().toDate() }
        }
      });
      const hasSpeedBoost = !!activeSpeedBoost;
      const speedBoostMultiplier = hasSpeedBoost ? (activeSpeedBoost?.multiplier || 1) : 1;
      console.log(`⚡ 速度加成状态: ${hasSpeedBoost}, 倍数: ${speedBoostMultiplier}`);

      let totalTimeWarpReward = createBigNumber(0);
      let totalFarmProductionPerSecond = createBigNumber(0);

      // 遍历每个已解锁的农场区域，使用配置表计算
      for (const farmPlot of farmPlots) {
        const config = configMap.get(farmPlot.level);

        if (!config) {
          console.warn(`农场区域 ${farmPlot.plotNumber} 等级 ${farmPlot.level} 没有找到对应配置`);
          continue;
        }

        console.log(`🏡 处理农场区块 ${farmPlot.plotNumber}, 等级 ${farmPlot.level}`);
        console.log(`📊 配置信息: offline=${config.offline} GEM/秒, production=${config.production}, cow=${config.cow}, speed=${config.speed}`);

        // 基于配置的时间跳跃收益计算
        // 使用 offline 字段作为基础每秒GEM收益率
        let plotTimeWarpRate = createBigNumber(config.offline);

        // 应用VIP加成（假设VIP对时间跳跃也有30%加成）
        if (hasVip) {
          plotTimeWarpRate = plotTimeWarpRate.multipliedBy(1.3);
          console.log(`👑 应用VIP加成后: ${plotTimeWarpRate.toFixed(3)} GEM/秒`);
        }

        // 应用速度加成（假设速度加成也影响时间跳跃）
        if (hasSpeedBoost) {
          plotTimeWarpRate = plotTimeWarpRate.multipliedBy(speedBoostMultiplier);
          console.log(`⚡ 应用速度加成后: ${plotTimeWarpRate.toFixed(3)} GEM/秒`);
        }

        // 计算该农场区域的时间跳跃收益
        const plotTimeWarpReward = plotTimeWarpRate.multipliedBy(timeInSeconds);
        totalTimeWarpReward = totalTimeWarpReward.plus(plotTimeWarpReward);

        console.log(`💎 区块 ${farmPlot.plotNumber} 时间跳跃收益: ${plotTimeWarpReward.toFixed(3)} GEM`);

        // 计算农场生产速度（用于兼容性显示）
        const plotProductionPerSecond = createBigNumber(config.production)
          .dividedBy(config.speed)
          .multipliedBy(config.cow);
        totalFarmProductionPerSecond = totalFarmProductionPerSecond.plus(plotProductionPerSecond);
      }

      console.log(`💎 总时间跳跃收益: ${totalTimeWarpReward.toFixed(3)} GEM`);

      // 为了保持API兼容性，我们需要提供一些模拟的出货线信息
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });
      let deliveryProcessingPerSecond = 0;
      let milkProduced = 0;
      let milkProcessed = 0;

      if (deliveryLine) {
        // 计算出货线处理能力（用于显示）
        deliveryProcessingPerSecond = deliveryLine.blockUnit / deliveryLine.deliverySpeed;
        if (hasVip) {
          deliveryProcessingPerSecond *= 1.3;
        }
        if (hasSpeedBoost) {
          deliveryProcessingPerSecond *= speedBoostMultiplier;
        }

        // 模拟牛奶生产和处理（用于显示）
        milkProduced = formatToThreeDecimalsNumber(totalFarmProductionPerSecond.multipliedBy(timeInSeconds));
        const milkCanProcess = deliveryProcessingPerSecond * timeInSeconds;
        milkProcessed = Math.min(milkProduced, milkCanProcess);
      }

      return {
        gemsEarned: formatToThreeDecimalsNumber(totalTimeWarpReward),
        milkProduced: formatToThreeDecimalsNumber(createBigNumber(milkProduced)),
        milkProcessed: formatToThreeDecimalsNumber(createBigNumber(milkProcessed)),
        farmProductionPerSecond: formatToThreeDecimalsNumber(totalFarmProductionPerSecond),
        deliveryProcessingPerSecond: formatToThreeDecimalsNumber(createBigNumber(deliveryProcessingPerSecond)),
        hasVip,
        hasSpeedBoost,
        speedBoostMultiplier
      };

    } catch (error) {
      console.error('配置驱动的时间跳跃计算失败:', error);
      // 降级到旧的计算方式
      console.log('🔄 降级到旧的计算方式');
      return await this.calculateTimeWarpRewardsLegacy(walletId, hours);
    }
  }

  /**
   * 创建空的时间跳跃结果
   */
  private static createEmptyTimeWarpResult(): TimeWarpResult {
    return {
      gemsEarned: 0,
      milkProduced: 0,
      milkProcessed: 0,
      farmProductionPerSecond: 0,
      deliveryProcessingPerSecond: 0,
      hasVip: false,
      hasSpeedBoost: false,
      speedBoostMultiplier: 1
    };
  }

  /**
   * 旧的时间跳跃计算方式（降级方案）
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 时间跳跃收益详情
   */
  static async calculateTimeWarpRewardsLegacy(walletId: number, hours: number): Promise<TimeWarpResult> {
    console.log(`🔄 使用旧的时间跳跃计算方式: walletId=${walletId}, hours=${hours}`);

    // 获取用户的农场区块
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true }
    });
    console.log(`🏡 找到 ${farmPlots.length} 个已解锁的农场区块`);

    // 获取用户的出货线
    const deliveryLine = await DeliveryLine.findOne({
      where: { walletId }
    });

    if (!deliveryLine) {
      console.error(`❌ 用户 ${walletId} 的出货线不存在`);
      throw new Error('用户出货线不存在');
    }

    console.log(`🚚 出货线信息: blockUnit=${deliveryLine.blockUnit}, deliverySpeed=${deliveryLine.deliverySpeed}, blockPrice=${deliveryLine.blockPrice}`);

    // 检查VIP状态
    const vipMembership = await VipMembership.findOne({
      where: {
        walletId,
        isActive: true,
        endTime: { [Op.gt]: dayjs().toDate() }
      }
    });
    const hasVip = !!vipMembership;

    // 检查速度加成状态
    const activeSpeedBoost = await ActiveBooster.findOne({
      where: {
        walletId,
        type: 'speed_boost',
        status: 'active',
        endTime: { [Op.gt]: dayjs().toDate() }
      }
    });
    const hasSpeedBoost = !!activeSpeedBoost;
    const speedBoostMultiplier = hasSpeedBoost ? (activeSpeedBoost?.multiplier || 1) : 1;

    // 计算农场区总产量（每秒）
    let farmProductionPerSecond = 0;
    for (const plot of farmPlots) {
      let plotProductionPerSecond = plot.calculateTotalProductionPerSecond();
      
      // 应用VIP加成（+30%农场生产速度）
      if (hasVip) {
        plotProductionPerSecond *= 1.3;
      }
      
      farmProductionPerSecond += plotProductionPerSecond;
    }

    // 计算出货线处理能力（每秒）
    let deliveryProcessingPerSecond = deliveryLine.blockUnit / deliveryLine.deliverySpeed;
    
    // 应用VIP加成（+30%出货速度）
    if (hasVip) {
      deliveryProcessingPerSecond *= 1.3;
    }
    
    // 应用速度加成
    if (hasSpeedBoost) {
      // 速度加成公式：实际速度 = 基础速度 × 倍数
      deliveryProcessingPerSecond *= speedBoostMultiplier;
    }

    // 计算时间跳跃期间的收益
    const timeInSeconds = hours * 3600;
    
    // 时间内生产的牛奶
    const milkProduced = farmProductionPerSecond * timeInSeconds;
    
    // 时间内可处理的牛奶
    const milkCanProcess = deliveryProcessingPerSecond * timeInSeconds;
    
    // 实际处理的牛奶（取较小值）
    const milkProcessed = Math.min(milkProduced, milkCanProcess);
    
    // 计算获得的GEM
    let gemsEarned = 0;
    if (milkProcessed > 0) {
      // 计算可以制作的方块数量
      const blocksProduced = Math.floor(milkProcessed / deliveryLine.blockUnit);
      console.log(`📦 可制作方块数量: ${blocksProduced} (milkProcessed=${milkProcessed} / blockUnit=${deliveryLine.blockUnit})`);

      // 计算方块价格（应用VIP加成 +20%方块价格）
      let blockPrice = deliveryLine.blockPrice;
      if (hasVip) {
        blockPrice *= 1.2;
        console.log(`👑 VIP加成后方块价格: ${blockPrice} (原价: ${deliveryLine.blockPrice})`);
      } else {
        console.log(`💰 方块价格: ${blockPrice}`);
      }

      // 计算总GEM收益
      gemsEarned = blocksProduced * blockPrice;
      console.log(`💎 总GEM收益: ${gemsEarned} (${blocksProduced} × ${blockPrice})`);
    } else {
      console.log(`⚠️ 没有处理牛奶，GEM收益为0`);
    }

    return {
      gemsEarned: formatToThreeDecimalsNumber(createBigNumber(gemsEarned)),
      milkProduced: formatToThreeDecimalsNumber(createBigNumber(milkProduced)),
      milkProcessed: formatToThreeDecimalsNumber(createBigNumber(milkProcessed)),
      farmProductionPerSecond: formatToThreeDecimalsNumber(createBigNumber(farmProductionPerSecond)),
      deliveryProcessingPerSecond: formatToThreeDecimalsNumber(createBigNumber(deliveryProcessingPerSecond)),
      hasVip,
      hasSpeedBoost,
      speedBoostMultiplier
    };
  }

  /**
   * 执行时间跳跃，直接给用户添加GEM收益
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @param transaction 数据库事务
   * @param productId 商品ID（可选）
   * @param purchaseId 购买记录ID（可选）
   * @returns 时间跳跃结果
   */
  static async executeTimeWarp(walletId: number, hours: number, transaction?: any, productId?: number, purchaseId?: number): Promise<TimeWarpResult> {
    console.log(`⏰ TimeWarpService.executeTimeWarp: walletId=${walletId}, hours=${hours}`);

    // 计算收益
    const rewards = await this.calculateTimeWarpRewards(walletId, hours);
    console.log(`🧮 计算收益结果:`, rewards);

    if (rewards.gemsEarned > 0) {
      console.log(`💎 准备增加GEM: ${rewards.gemsEarned}`);
      // 直接给用户添加GEM (使用正确的increment语法)
      const incrementResult = await UserWallet.increment('gem', {
        by: rewards.gemsEarned,
        where: { id: walletId },
        transaction
      });
      console.log(`📊 UserWallet.increment 结果:`, incrementResult);
    } else {
      console.log(`⚠️ 计算的GEM收益为0，不增加GEM`);
    }

    // 更新农场区的最后生产时间
    const farmPlots = await FarmPlot.findAll({
      where: { walletId, isUnlocked: true },
      transaction
    });

    for (const plot of farmPlots) {
      plot.lastProductionTime = dayjs(plot.lastProductionTime).add(hours, 'hour').toDate();
      await plot.save({ transaction });
    }

    // 更新出货线的最后出货时间
    const deliveryLine = await DeliveryLine.findOne({
      where: { walletId },
      transaction
    });

    if (deliveryLine) {
      deliveryLine.lastDeliveryTime = dayjs(deliveryLine.lastDeliveryTime).add(hours, 'hour').toDate();
      await deliveryLine.save({ transaction });
    }

    // 记录时间跳跃历史
    await TimeWarpHistory.create({
      walletId,
      productId,
      purchaseId,
      hours,
      gemsEarned: rewards.gemsEarned,
      milkProduced: rewards.milkProduced,
      milkProcessed: rewards.milkProcessed,
      farmProductionPerSecond: rewards.farmProductionPerSecond,
      deliveryProcessingPerSecond: rewards.deliveryProcessingPerSecond,
      hasVip: rewards.hasVip,
      hasSpeedBoost: rewards.hasSpeedBoost,
      speedBoostMultiplier: rewards.speedBoostMultiplier,
      usedAt: dayjs().toDate()
    }, { transaction });

    return rewards;
  }

  /**
   * 预览时间跳跃收益（不执行，仅计算）
   * @param walletId 用户钱包ID
   * @param hours 跳跃小时数
   * @returns 预览收益
   */
  static async previewTimeWarpRewards(walletId: number, hours: number): Promise<TimeWarpResult> {
    return await this.calculateTimeWarpRewards(walletId, hours);
  }
}

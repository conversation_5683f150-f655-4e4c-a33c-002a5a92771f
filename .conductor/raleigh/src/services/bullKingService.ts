// src/services/bullKingService.ts
import { redis } from "../config/redis";
import { sequelize } from "../config/db";
import { QueryTypes } from "sequelize";
import { PrizePool, UserWallet, RewardClaim, User, WalletHistory } from "../models";
import { t } from "../i18n";


/**
 * 获取牛王宝座排行榜和奖池金额
 * @param limit 限制返回的记录数量
 * @returns 排行榜数据和奖池金额
 */
// 在现有函数的基础上添加获取用户自己排名和可领取奖励的功能
export async function getBullKingLeaderboard(limit: number = 10, walletId?: number) {
  // 从Redis获取排行榜数据，按照变身次数降序排列
  const leaderboardData = await redis.zrevrange('bull_king_leaderboard', 0, limit - 1, 'WITHSCORES');

  // 获取牛王宝座奖池金额
  const bullKingPool = await PrizePool.findOne({
    where: { type: 'bull_king' }
  });

  // 处理返回的数据格式
  const result = [];
  for (let i = 0; i < leaderboardData.length; i += 2) {
    const currentWalletId = parseInt(leaderboardData[i]);
    const transformCount = parseInt(leaderboardData[i + 1]);

    // 获取钱包和用户信息
    const wallet = await UserWallet.findByPk(currentWalletId);
    if (wallet) {
      const user = await User.findByPk(wallet.userId);

      result.push({
        rank: i / 2 + 1,
        userId: wallet.userId,
        walletId: walletId,
        walletAddress: wallet.walletAddress,
        username: user ? user.username : null,
        telegramId: user ? user.telegramId : null,
        photoUrl: user ? user.photoUrl : null,
        firstName: user ? user.firstName : null,
        lastName: user ? user.lastName : null,
        transformCount: transformCount
      });
    }
  }

  // 获取用户自己的排名和可领取奖励
  let userRank = null;
  let userReward = 0;

  if (walletId) {
    // 获取用户的变身次数
    const userScore = await redis.zscore('bull_king_leaderboard', walletId.toString());
    const userTransformCount = userScore ? parseInt(userScore) : 0;

    // 获取用户排名
    if (userScore) {
      // zrevrank 返回的是从0开始的索引，所以需要+1
      const rankIndex = await redis.zrevrank('bull_king_leaderboard', walletId.toString());
      userRank = rankIndex !== null ? rankIndex + 1 : null;
    }

    // 计算用户可领取的奖励
    if (bullKingPool && bullKingPool.amount > 0) {
      // 根据变身次数计算可领取的奖励
      const BULL_KING_REWARDS = {
        LEVEL_5: 0.5,  // 5次变身获得50%奖池
        LEVEL_4: 0.1,  // 4次变身瓜分10%奖池
        LEVEL_3: 0.1,  // 3次变身瓜分10%奖池
      };

      if (userTransformCount === 5) {
        userReward = bullKingPool.amount * BULL_KING_REWARDS.LEVEL_5;
      } else if (userTransformCount === 4) {
        // 获取所有4次变身的玩家数量
        const players4Count = await redis.zcount('bull_king_leaderboard', '4', '4');
        if (players4Count > 0) {
          userReward = (bullKingPool.amount * BULL_KING_REWARDS.LEVEL_4) / players4Count;
        }
      } else if (userTransformCount === 3) {
        // 获取所有3次变身的玩家数量
        const players3Count = await redis.zcount('bull_king_leaderboard', '3', '3');
        if (players3Count > 0) {
          userReward = (bullKingPool.amount * BULL_KING_REWARDS.LEVEL_3) / players3Count;
        }
      }
    }
  }

  return {
    leaderboard: result,
    poolAmount: bullKingPool ? bullKingPool.amount : 0,
    userRank,
    userReward,
    userTransformCount: walletId ? (await redis.zscore('bull_king_leaderboard', walletId.toString()) || '0') : '0'
  };
}
// 修改获取 MOOF 持有者排行榜的函数
export async function getMoofHoldersLeaderboard(limit: number = 100, walletId?: number) {
  try {
    // 修改查询，添加排名信息
    const leaderboard = await sequelize.query(
      `SELECT 
        uw.id as walletId, 
        uw.userId, 
        uw.moof, 
        uw.walletAddress, 
        u.username, 
        u.photoUrl,
        @rank := @rank + 1 as \`rank\`
       FROM user_wallets uw
       LEFT JOIN users u ON uw.userId = u.id,
       (SELECT @rank := 0) r
       WHERE uw.moof > 0
       ORDER BY uw.moof DESC
       LIMIT :limit`,
      {
        replacements: { limit },
        type: QueryTypes.SELECT,
      }
    );

    // 获取 MOOF 持有者奖金池总金额
    const moofPool = await PrizePool.findOne({
      where: { type: 'moof_holders' }
    });

    const poolAmount = moofPool ? moofPool.amount : 0;
    const distributionAmount = poolAmount * 0.7; // 70% 用于分发

    // 如果提供了用户ID，获取用户的排名和可领取奖励
    let userRank = null;
    let userReward = null;

    if (walletId) {
      // 修复：使用子查询和计数方式获取排名，而不是使用窗口函数
      // 修复：使用反引号包裹 rank 关键字，或者使用不同的别名
      const rankResult = await sequelize.query(
        `SELECT COUNT(*) + 1 as \`rank\`
         FROM user_wallets
         WHERE moof > (SELECT moof FROM user_wallets WHERE id = :walletId) AND moof > 0`,
        {
          replacements: { walletId },
          type: QueryTypes.SELECT,
        }
      );

      //@ts-ignore
      userRank = rankResult.length > 0 ? rankResult[0].rank : null;

      // 获取用户可领取的奖励
      const userWallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (userWallet && userWallet.moof && userWallet.moof > 0) {
        // 获取所有 MOOF 持有者的总量
        const totalMoof = await sequelize.query(
          `SELECT SUM(moof) as totalMoof FROM user_wallets WHERE moof > 0`,
          {
            type: QueryTypes.SELECT,
          }
        );

        //@ts-ignore
        const totalMoofAmount = totalMoof[0].totalMoof || 0;

        if (totalMoofAmount > 0) {
          // 计算用户应得的奖励比例
          const userShare = userWallet.moof / totalMoofAmount;
          userReward = distributionAmount * userShare;
          // 保留5位小数
          userReward = parseFloat(userReward.toFixed(5));
        }
      }

      // 获取用户当前周可领取的奖励
      const pendingReward = await RewardClaim.findOne({
        where: {
          walletId,
          subPool: 'moof_holders_weekly',
          claimed: false
        }
      });

      if (pendingReward) {
        userReward = pendingReward.amount;
      }
    }

    return {
      leaderboard,
      poolInfo: {
        totalAmount: poolAmount,
        distributionAmount
      },
      userInfo: walletId ? {
        rank: userRank,
        reward: userReward
      } : null
    };
  } catch (error) {
    console.error("获取 MOOF 持有者排行榜失败:", error);
    throw error;
  }
}

/**
 * 领取 MOOF 持有者奖励
 */
export async function claimMoofHoldersReward(walletId: number) {
  const transaction = await sequelize.transaction();

  try {
    // 查找未领取的奖励记录
    const pendingReward = await RewardClaim.findOne({
      where: {
        walletId,
        subPool: 'moof_holders_weekly',
        claimed: false
      },
      lock: true,
      transaction
    });

    if (!pendingReward) {
      throw new Error('没有可领取的奖励');
    }

    // 更新用户钱包余额
    await UserWallet.increment(
      'usd',
      {
        by: pendingReward.amount,
        where: { id: walletId },
        transaction
      }
    );

    // 标记奖励已领取
    await pendingReward.update(
      { claimed: true },
      { transaction }
    );

    // 记录钱包历史
    await WalletHistory.create({
      userId: pendingReward.userId,
      walletId: walletId,
      amount: pendingReward.amount,
      currency: 'usd',
      reference: 'MOOF Holders Reward',
      action: 'in',
      category: 'usd',
      credit_type: 'usd',
      fe_display_remark: 'MOOF持有者奖励',
      developer_remark: `MOOF持有者周奖励领取`,
    }, { transaction });

    await transaction.commit();

    return {
      claimed: true,
      amount: pendingReward.amount
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

/**
 * 领取牛王宝座奖励
 * @param walletId 钱包ID
 * @returns 领取结果
 */
export async function claimBullKingReward(walletId: number) {
  const transaction = await sequelize.transaction();

  try {
    // 获取用户钱包
    const userWallet = await UserWallet.findOne({
      where: { id: walletId },
      lock: true,
      transaction
    });

    if (!userWallet) {
      throw new Error(t('errors.userWalletNotFound'));
    }

    // 检查是否有可领取的奖励
    if (!userWallet.bullReward || userWallet.bullReward <= 0) {
      throw new Error(t('errors.noBullKingRewards'));
    }

    const rewardAmount = userWallet.bullReward;

    // 将奖励金额添加到 USD 余额中
    await UserWallet.increment(
      'usd',
      {
        by: rewardAmount,
        where: { id: walletId },
        transaction
      }
    );

    // 将 bullReward 字段清零
    await userWallet.update(
      { bullReward: 0 },
      { transaction }
    );

    // 记录钱包历史
    await WalletHistory.create({
      userId: userWallet.userId,
      walletId: walletId,
      amount: rewardAmount,
      currency: 'usd',
      reference: 'Bull King Reward',
      action: 'in',
      category: 'usd',
      credit_type: 'usd',
      fe_display_remark: '牛王宝座奖励',
      developer_remark: `牛王宝座奖励领取，金额: ${rewardAmount}`,
    }, { transaction });

    await transaction.commit();

    return {
      claimed: true,
      amount: rewardAmount
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

/**
 * 获取个人 KOL 排行榜 - 基于一级直推玩家的每周游戏量
 * @param limit 限制返回的记录数量
 * @param walletId 用户钱包ID
 * @returns 排行榜数据和奖池金额
 */
export async function getPersonalKolLeaderboard(limit: number = 100, walletId?: number) {
  try {
    // 查询个人 KOL 排行榜数据 - 基于一级直推玩家的每周游戏量
    const leaderboard = await sequelize.query(
      `SELECT 
        u.id as userId,
        uw.id as walletId, 
        uw.walletAddress,
        u.username, 
        u.photoUrl,
        COUNT(DISTINCT r.id) as directReferrals,
        SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume,
        @rank := @rank + 1 as \`rank\`
       FROM users u
       JOIN user_wallets uw ON u.id = uw.userId
       LEFT JOIN users r ON r.referrerId = u.id
       LEFT JOIN user_wallets rw ON r.id = rw.userId
       LEFT JOIN game_histories gh ON rw.id = gh.walletId AND gh.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
       JOIN (SELECT @rank := 0) rank_init
       GROUP BY u.id, uw.id, uw.walletAddress, u.username, u.photoUrl
       HAVING COUNT(DISTINCT r.id) > 0
       ORDER BY totalGameVolume DESC
       LIMIT :limit`,
      {
        replacements: { limit },
        type: QueryTypes.SELECT,
      }
    );

    // 获取个人 KOL 奖金池总金额
    const kolPool = await PrizePool.findOne({
      where: { type: 'personal_kol' }
    });

    const poolAmount = kolPool ? kolPool.amount : 0;
    const distributionAmount = poolAmount * 0.7; // 70% 用于分发

    // 如果提供了用户ID，获取用户的排名和可领取奖励
    let userRank = null;
    let userReward = null;
    let userGameVolume = 0;

    if (walletId) {
      // 获取用户钱包信息
      const userWallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (userWallet) {
        // 获取用户的一级直推玩家的游戏量
        const userGameVolumeResult = await sequelize.query(
          `SELECT 
            SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume
           FROM users u
           JOIN users r ON r.referrerId = u.id
           JOIN user_wallets rw ON r.id = rw.userId
           LEFT JOIN game_histories gh ON rw.id = gh.walletId AND gh.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
           WHERE u.id = :userId
           GROUP BY u.id`,
          {
            replacements: { userId: userWallet.userId },
            type: QueryTypes.SELECT,
          }
        );

        if (userGameVolumeResult.length > 0) {
          //@ts-ignore
          userGameVolume = userGameVolumeResult[0].totalGameVolume || 0;
        }

        // 获取用户排名
        const rankResult = await sequelize.query(
          `SELECT COUNT(*) + 1 as \`rank\`
           FROM (
             SELECT 
               u.id,
               SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume
             FROM users u
             JOIN users r ON r.referrerId = u.id
             JOIN user_wallets rw ON r.id = rw.userId
             LEFT JOIN game_histories gh ON rw.id = gh.walletId AND gh.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             GROUP BY u.id
             HAVING totalGameVolume > :userGameVolume AND COUNT(DISTINCT r.id) > 0
           ) as ranked`,
          {
            replacements: { userGameVolume },
            type: QueryTypes.SELECT,
          }
        );

        //@ts-ignore
        userRank = rankResult.length > 0 ? rankResult[0].rank : null;

        // 获取所有 KOL 的总游戏量
        const totalVolumeResult = await sequelize.query(
          `SELECT 
             SUM(total_volume) as grandTotal
           FROM (
             SELECT 
               u.id,
               SUM(IFNULL(gh.betAmount, 0)) as total_volume
             FROM users u
             JOIN users r ON r.referrerId = u.id
             JOIN user_wallets rw ON r.id = rw.userId
             LEFT JOIN game_histories gh ON rw.id = gh.walletId AND gh.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             GROUP BY u.id
             HAVING COUNT(DISTINCT r.id) > 0
           ) as all_kols`,
          {
            type: QueryTypes.SELECT,
          }
        );

        //@ts-ignore
        const totalGameVolume = totalVolumeResult[0].grandTotal || 0;

        if (totalGameVolume > 0 && userGameVolume > 0) {
          // 计算用户应得的奖励比例
          const userShare = userGameVolume / totalGameVolume;
          userReward = distributionAmount * userShare;
        }
      }

      // 获取用户当前周可领取的奖励
      const pendingReward = await RewardClaim.findOne({
        where: {
          walletId,
          subPool: 'personal_kol_weekly',
          claimed: false
        }
      });

      if (pendingReward) {
        userReward = pendingReward.amount;
      }
    }

    return {
      leaderboard,
      poolInfo: {
        totalAmount: poolAmount,
        distributionAmount
      },
      userInfo: walletId ? {
        rank: userRank,
        reward: userReward,
        gameVolume: userGameVolume
      } : null
    };
  } catch (error) {
    console.error("获取个人 KOL 排行榜失败:", error);
    throw error;
  }
}

/**
 * 领取个人 KOL 奖励
 */
export async function claimPersonalKolReward(walletId: number) {
  const transaction = await sequelize.transaction();

  try {
    // 查找未领取的奖励记录
    const pendingReward = await RewardClaim.findOne({
      where: {
        walletId,
        subPool: 'personal_kol_weekly',
        claimed: false
      },
      lock: true,
      transaction
    });

    if (!pendingReward) {
      throw new Error('没有可领取的奖励');
    }

    // 更新用户钱包余额
    await UserWallet.increment(
      'usd',
      {
        by: pendingReward.amount,
        where: { id: walletId },
        transaction
      }
    );

    // 标记奖励已领取
    await pendingReward.update(
      { claimed: true },
      { transaction }
    );

    // 记录钱包历史
    await WalletHistory.create({
      userId: pendingReward.userId,
      walletId: walletId,
      amount: pendingReward.amount,
      currency: 'usd',
      reference: 'Personal KOL Reward',
      action: 'in',
      category: 'usd',
      credit_type: 'usd',
      fe_display_remark: '个人KOL奖励',
      developer_remark: `个人KOL周奖励领取`,
    }, { transaction });

    await transaction.commit();

    return {
      claimed: true,
      amount: pendingReward.amount
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

/**
 * 获取全球团队 KOL 排行榜
 * @param limit 限制返回的记录数量
 * @param walletId 用户钱包ID
 * @returns 排行榜数据和奖池金额
 */
export async function getTeamKolLeaderboard(limit: number = 100, walletId?: number) {
  try {
    // 获取本周的开始和结束时间
    const currentWeekStart = new Date();
    currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay()); // 设置为本周日
    currentWeekStart.setHours(0, 0, 0, 0);

    const currentWeekEnd = new Date();
    currentWeekEnd.setDate(currentWeekEnd.getDate() + (6 - currentWeekEnd.getDay())); // 设置为本周六
    currentWeekEnd.setHours(23, 59, 59, 999);

    // 查询团队 KOL 排行榜数据 - 基于团队玩家的本周游戏量
    const leaderboard = await sequelize.query(
      `WITH RECURSIVE team_hierarchy AS (
        -- 基础查询：获取直接下级
        SELECT 
          u.id as root_id,
          r.id as member_id,
          1 as level
        FROM users u
        JOIN users r ON r.referrerId = u.id
        
        UNION ALL
        
        -- 递归查询：获取更深层级的下级
        SELECT 
          th.root_id,
          u.id as member_id,
          th.level + 1
        FROM team_hierarchy th
        JOIN users u ON u.referrerId = th.member_id
        WHERE th.level < 5
      )
      SELECT 
        u.id as userId,
        uw.id as walletId,
        uw.walletAddress,
        u.username,
        u.photoUrl,
        COUNT(DISTINCT th.member_id) as teamSize,
        SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume,
        @rank := @rank + 1 as \`rank\`
      FROM users u
      JOIN user_wallets uw ON u.id = uw.userId
      LEFT JOIN team_hierarchy th ON th.root_id = u.id
      LEFT JOIN user_wallets mw ON th.member_id = mw.userId
      LEFT JOIN game_histories gh ON mw.id = gh.walletId 
        AND gh.createdAt BETWEEN :weekStart AND :weekEnd
      JOIN (SELECT @rank := 0) rank_init
      GROUP BY u.id, uw.id, uw.walletAddress, u.username, u.photoUrl
      HAVING teamSize > 0
      ORDER BY totalGameVolume DESC
      LIMIT :limit`,
      {
        replacements: {
          limit,
          weekStart: currentWeekStart,
          weekEnd: currentWeekEnd
        },
        type: QueryTypes.SELECT,
      }
    );

    // 获取团队 KOL 奖金池总金额
    const kolPool = await PrizePool.findOne({
      where: { type: 'team_kol' }
    });

    const poolAmount = kolPool ? kolPool.amount : 0;
    const distributionAmount = poolAmount * 0.7; // 70% 用于分发

    // 如果提供了用户ID，获取用户的排名和可领取奖励
    let userRank = null;
    let userReward = null;
    let userGameVolume = 0;

    if (walletId) {
      const userWallet = await UserWallet.findOne({
        where: { id: walletId }
      });

      if (userWallet) {
        // 获取用户团队的游戏量
        const userGameVolumeResult = await sequelize.query(
          `WITH RECURSIVE team_hierarchy AS (
            SELECT 
              u.id as root_id,
              r.id as member_id,
              1 as level
            FROM users u
            JOIN users r ON r.referrerId = u.id
            WHERE u.id = :userId
            
            UNION ALL
            
            SELECT 
              th.root_id,
              u.id as member_id,
              th.level + 1
            FROM team_hierarchy th
            JOIN users u ON u.referrerId = th.member_id
            WHERE th.level < 5
          )
          SELECT 
            SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume
          FROM team_hierarchy th
          LEFT JOIN user_wallets mw ON th.member_id = mw.userId
          LEFT JOIN game_histories gh ON mw.id = gh.walletId 
            AND gh.createdAt BETWEEN :weekStart AND :weekEnd`,
          {
            replacements: { userId: userWallet.userId, weekStart: currentWeekStart, weekEnd: currentWeekEnd },
            type: QueryTypes.SELECT,
          }
        );

        if (userGameVolumeResult.length > 0) {
          //@ts-ignore
          userGameVolume = userGameVolumeResult[0].totalGameVolume || 0;
        }

        // 获取用户排名
        const rankResult = await sequelize.query(
          `WITH RECURSIVE team_hierarchy AS (
            SELECT 
              u.id as root_id,
              r.id as member_id,
              1 as level
            FROM users u
            JOIN users r ON r.referrerId = u.id
            
            UNION ALL
            
            SELECT 
              th.root_id,
              u.id as member_id,
              th.level + 1
            FROM team_hierarchy th
            JOIN users u ON u.referrerId = th.member_id
            WHERE th.level < 5
          )
          SELECT COUNT(*) + 1 as \`rank\`
          FROM (
            SELECT 
              u.id,
              SUM(IFNULL(gh.betAmount, 0)) as totalGameVolume
            FROM users u
            LEFT JOIN team_hierarchy th ON th.root_id = u.id
            LEFT JOIN user_wallets mw ON th.member_id = mw.userId
            LEFT JOIN game_histories gh ON mw.id = gh.walletId 
              AND gh.createdAt BETWEEN :weekStart AND :weekEnd
            GROUP BY u.id
            HAVING totalGameVolume > :userGameVolume AND COUNT(DISTINCT th.member_id) > 0
          ) as ranked`,
          {
            replacements: {
              userGameVolume,
              weekStart: currentWeekStart,
              weekEnd: currentWeekEnd
            },
            type: QueryTypes.SELECT,
          }
        );

        //@ts-ignore
        userRank = rankResult.length > 0 ? rankResult[0].rank : null;

        // 获取用户当前周可领取的奖励
        const pendingReward = await RewardClaim.findOne({
          where: {
            walletId,
            subPool: 'team_kol_weekly',
            claimed: false
          }
        });

        if (pendingReward) {
          userReward = pendingReward.amount;
        }
      }
    }

    return {
      leaderboard,
      poolInfo: {
        totalAmount: poolAmount,
        distributionAmount
      },
      userInfo: walletId ? {
        rank: userRank,
        reward: userReward,
        gameVolume: userGameVolume
      } : null
    };
  } catch (error) {
    console.error("获取团队 KOL 排行榜失败:", error);
    throw error;
  }
}

/**
 * 领取团队 KOL 奖励
 */
export async function claimTeamKolReward(walletId: number) {
  const transaction = await sequelize.transaction();
  
  try {
    // 查找未领取的奖励记录
    const pendingReward = await RewardClaim.findOne({
      where: {
        walletId,
        subPool: 'team_kol_weekly',
        claimed: false
      },
      lock: true,
      transaction
    });

    if (!pendingReward) {
      throw new Error('没有可领取的奖励');
    }

    // 更新用户钱包余额
    await UserWallet.increment(
      'usd',
      {
        by: pendingReward.amount,
        where: { id: walletId },
        transaction
      }
    );

    // 标记奖励已领取
    await pendingReward.update(
      { claimed: true },
      { transaction }
    );

    // 记录钱包历史
    await WalletHistory.create({
      userId: pendingReward.userId,
      walletId: walletId,
      amount: pendingReward.amount,
      currency: 'usd',
      reference: 'Team KOL Reward',
      action: 'in',
      category: 'usd',
      credit_type: 'usd',
      fe_display_remark: '团队KOL奖励',
      developer_remark: `团队KOL周奖励领取`,
    }, { transaction });

    await transaction.commit();
    
    return {
      claimed: true,
      amount: pendingReward.amount
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
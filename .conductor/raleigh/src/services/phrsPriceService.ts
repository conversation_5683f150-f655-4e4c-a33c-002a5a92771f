// src/services/phrsPriceService.ts
import { IapProduct } from '../models';
import { Op } from 'sequelize';
import dotenv from "dotenv";

dotenv.config();

/**
 * PHRS价格服务
 * 处理PHRS价格计算和更新相关的业务逻辑
 */
export class PhrsPriceService {
  // PHRS 兑换 USD 汇率，默认 1 PHRS = 0.0001 USD
  private static readonly DEFAULT_PHRS_TO_USD_RATE = 0.0001;
  
  /**
   * 获取当前PHRS兑USD汇率
   */
  public static getPhrsToUsdRate(): number {
    const rate = parseFloat(process.env.PHRS_TO_USD_RATE || String(this.DEFAULT_PHRS_TO_USD_RATE));
    return rate > 0 ? rate : this.DEFAULT_PHRS_TO_USD_RATE;
  }

  /**
   * 根据USD价格计算PHRS价格
   * @param usdPrice USD价格
   * @returns PHRS价格
   */
  public static calculatePhrsPrice(usdPrice: number): number {
    const rate = this.getPhrsToUsdRate();
    return parseFloat((usdPrice / rate).toFixed(4));
  }

  /**
   * 根据PHRS价格计算USD价格
   * @param phrsPrice PHRS价格
   * @returns USD价格
   */
  public static calculateUsdPrice(phrsPrice: number): number {
    const rate = this.getPhrsToUsdRate();
    return parseFloat((phrsPrice * rate).toFixed(4));
  }

  /**
   * 更新单个产品的PHRS价格
   * @param productId 产品ID
   * @returns 更新后的产品信息
   */
  public static async updateProductPhrsPrice(productId: number): Promise<IapProduct | null> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        throw new Error(`产品不存在: ${productId}`);
      }

      if (product.priceUsd <= 0) {
        throw new Error(`产品USD价格无效: ${product.priceUsd}`);
      }

      const phrsPrice = this.calculatePhrsPrice(product.priceUsd);
      await product.update({ pricePhrs: phrsPrice });

      console.log(`产品 ${product.name} (ID: ${productId}) PHRS价格已更新: ${product.priceUsd} USD = ${phrsPrice} PHRS`);
      return product;
    } catch (error) {
      console.error(`更新产品 ${productId} PHRS价格失败:`, error);
      throw error;
    }
  }

  /**
   * 批量更新所有产品的PHRS价格
   * @returns 更新的产品数量
   */
  public static async updateAllProductsPhrsPrices(): Promise<number> {
    try {
      console.log('开始批量更新所有产品的PHRS价格...');
      
      const products = await IapProduct.findAll({
        where: {
          priceUsd: {
            [Op.gt]: 0
          }
        }
      });

      console.log(`找到 ${products.length} 个需要更新的产品`);
      
      let updatedCount = 0;
      const rate = this.getPhrsToUsdRate();

      for (const product of products) {
        try {
          const phrsPrice = this.calculatePhrsPrice(product.priceUsd);
          await product.update({ pricePhrs: phrsPrice });
          
          console.log(`产品 ${product.name} (ID: ${product.id}) PHRS价格已更新: ${product.priceUsd} USD = ${phrsPrice} PHRS`);
          updatedCount++;
        } catch (error) {
          console.error(`更新产品 ${product.id} PHRS价格失败:`, error);
        }
      }

      console.log(`PHRS价格批量更新完成，共更新了 ${updatedCount} 个产品，使用汇率: 1 PHRS = ${rate} USD`);
      return updatedCount;
    } catch (error) {
      console.error('批量更新PHRS价格失败:', error);
      throw error;
    }
  }

  /**
   * 获取产品的所有价格信息
   * @param productId 产品ID
   * @returns 产品价格信息
   */
  public static async getProductPrices(productId: number): Promise<{
    usdPrice: number;
    kaiaPrice?: number;
    phrsPrice?: number;
    phrsToUsdRate: number;
  } | null> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        return null;
      }

      return {
        usdPrice: product.priceUsd,
        kaiaPrice: product.priceKaia || undefined,
        phrsPrice: product.pricePhrs || undefined,
        phrsToUsdRate: this.getPhrsToUsdRate()
      };
    } catch (error) {
      console.error(`获取产品 ${productId} 价格信息失败:`, error);
      throw error;
    }
  }

  /**
   * 验证PHRS支付金额是否正确
   * @param productId 产品ID
   * @param paidPhrsAmount 支付的PHRS金额
   * @returns 验证结果
   */
  public static async validatePhrsPayment(productId: number, paidPhrsAmount: number): Promise<{
    isValid: boolean;
    expectedAmount: number;
    actualAmount: number;
    tolerance?: number;
  }> {
    try {
      const product = await IapProduct.findByPk(productId);
      if (!product) {
        throw new Error(`产品不存在: ${productId}`);
      }

      const expectedPhrsAmount = product.pricePhrs || this.calculatePhrsPrice(product.priceUsd);
      
      // 允许小数点精度误差，容差为0.0001 PHRS
      const tolerance = 0.0001;
      const difference = Math.abs(paidPhrsAmount - expectedPhrsAmount);
      const isValid = difference <= tolerance;

      return {
        isValid,
        expectedAmount: expectedPhrsAmount,
        actualAmount: paidPhrsAmount,
        tolerance
      };
    } catch (error) {
      console.error(`验证产品 ${productId} PHRS支付失败:`, error);
      throw error;
    }
  }

  /**
   * 获取当前汇率信息
   */
  public static getCurrentRateInfo(): {
    phrsToUsdRate: number;
    usdToPhrsRate: number;
    lastUpdated: string;
    source: string;
  } {
    const rate = this.getPhrsToUsdRate();
    return {
      phrsToUsdRate: rate,
      usdToPhrsRate: parseFloat((1 / rate).toFixed(4)),
      lastUpdated: new Date().toISOString(),
      source: 'fixed_rate' // 目前使用固定汇率
    };
  }
}

import { ethers } from 'ethers';
import { PhrsDeposit } from '../models';
import { sequelize } from '../config/db';
import cron from 'node-cron';

/**
 * PHRS余额监控服务
 * 定期检查链上余额与后端数据库记录的差异
 */
export class PhrsBalanceMonitor {
  private provider: ethers.JsonRpcProvider;
  private contract: ethers.Contract;
  private contractAddress: string;
  private isRunning: boolean = false;
  private cronJob: cron.ScheduledTask | null = null;

  // 合约ABI（只包含需要的函数）
  private readonly contractABI = [
    "function getBalance() external view returns (uint256)",
    "function totalLegitimateDeposits() external view returns (uint256)",
    "function getUserBalance(address user) external view returns (uint256)",
    "function detectForcedDeposits() external view returns (uint256 forcedAmount, bool hasForced)"
  ];

  constructor() {
    const rpcUrl = process.env.PHAROS_RPC_URL || 'https://rpc.pharos.network';
    this.contractAddress = process.env.PHRS_DEPOSIT_CONTRACT_ADDRESS!;
    
    if (!this.contractAddress) {
      throw new Error('PHRS_DEPOSIT_CONTRACT_ADDRESS environment variable is required');
    }

    this.provider = new ethers.JsonRpcProvider(rpcUrl);
    this.contract = new ethers.Contract(this.contractAddress, this.contractABI, this.provider);
    
    console.log(`PHRS余额监控服务初始化完成 - 合约地址: ${this.contractAddress}`);
  }

  /**
   * 启动监控服务
   */
  public start(): void {
    if (this.isRunning) {
      console.log('PHRS余额监控服务已在运行');
      return;
    }

    try {
      // 设置定时任务，每10分钟执行一次
      this.cronJob = cron.schedule('*/10 * * * *', async () => {
        await this.runBalanceCheck();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      this.cronJob.start();
      this.isRunning = true;

      console.log('✅ PHRS余额监控服务启动成功');
      console.log('📅 定时任务: 每10分钟执行一次');
      
      // 立即执行一次检查
      this.runBalanceCheck();

    } catch (error) {
      console.error('❌ PHRS余额监控服务启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止监控服务
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
    }

    this.isRunning = false;
    console.log('PHRS余额监控服务已停止');
  }

  /**
   * 执行余额检查
   */
  public async runBalanceCheck(): Promise<void> {
    try {
      console.log('🔍 开始执行PHRS余额监控检查...');

      // 1. 获取链上数据
      const [contractBalance, legitimateDeposits, forcedInfo] = await Promise.all([
        this.contract.getBalance(),
        this.contract.totalLegitimateDeposits(),
        this.contract.detectForcedDeposits()
      ]);

      // 2. 获取数据库中的总充值金额
      const dbTotalResult = await PhrsDeposit.findOne({
        attributes: [
          [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
        ],
        raw: true
      }) as any;

      const dbTotalAmount = dbTotalResult?.totalAmount || '0';
      const dbTotalCount = dbTotalResult?.totalCount || 0;

      // 3. 转换为可比较的格式
      const contractBalanceEther = ethers.formatEther(contractBalance);
      const legitimateDepositsEther = ethers.formatEther(legitimateDeposits);
      // 数据库金额已经是字符串格式，不需要再转换
      const dbTotalAmountEther = dbTotalAmount || '0';

      // 4. 检查强制发送
      const [forcedAmount, hasForced] = forcedInfo;
      const forcedAmountEther = ethers.formatEther(forcedAmount);

      console.log('📊 PHRS余额监控报告:');
      console.log(`🔗 合约总余额: ${contractBalanceEther} PHRS`);
      console.log(`✅ 合法充值总额: ${legitimateDepositsEther} PHRS`);
      console.log(`💾 数据库记录总额: ${dbTotalAmountEther} PHRS`);
      console.log(`📝 数据库记录笔数: ${dbTotalCount}`);

      if (hasForced) {
        console.log(`⚠️  检测到强制发送: ${forcedAmountEther} PHRS`);
      }

      // 5. 检查差异
      const legitimateVsDb = parseFloat(legitimateDepositsEther) - parseFloat(dbTotalAmountEther);
      const contractVsLegitimate = parseFloat(contractBalanceEther) - parseFloat(legitimateDepositsEther);

      // 6. 报告异常情况
      if (Math.abs(legitimateVsDb) > 0.001) { // 允许0.001 PHRS的精度误差
        console.warn(`⚠️  链上合法充值与数据库记录不匹配!`);
        console.warn(`   差异: ${legitimateVsDb.toFixed(6)} PHRS`);

        // 这里可以发送告警通知
        await this.sendAlert('BALANCE_MISMATCH', {
          chainLegitimate: legitimateDepositsEther,
          dbTotal: dbTotalAmountEther,
          difference: legitimateVsDb.toFixed(6)
        });
      }

      // 检查合约总余额与合法充值的差异（强制发送检测的另一种方式）
      if (Math.abs(contractVsLegitimate) > 0.001) {
        console.warn(`⚠️  合约总余额与合法充值不匹配!`);
        console.warn(`   差异: ${contractVsLegitimate.toFixed(6)} PHRS (可能是强制发送)`);
      }

      if (hasForced && parseFloat(forcedAmountEther) > 0) {
        console.warn(`⚠️  检测到强制发送的资金: ${forcedAmountEther} PHRS`);
        
        // 发送强制发送告警
        await this.sendAlert('FORCED_DEPOSIT_DETECTED', {
          forcedAmount: forcedAmountEther,
          contractBalance: contractBalanceEther,
          legitimateDeposits: legitimateDepositsEther
        });
      }

      if (Math.abs(legitimateVsDb) <= 0.001 && !hasForced) {
        console.log('✅ 余额检查正常，无异常发现');
      }

      console.log('✅ PHRS余额监控检查完成\n');

    } catch (error) {
      console.error('❌ PHRS余额监控检查失败:', error);
      
      // 发送错误告警
      await this.sendAlert('MONITOR_ERROR', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * 发送告警通知
   */
  private async sendAlert(type: string, data: any): Promise<void> {
    try {
      // 这里可以集成各种告警方式：
      // 1. 发送邮件
      // 2. 发送到Slack/Discord
      // 3. 发送到监控系统
      // 4. 记录到数据库
      
      console.log(`🚨 告警: ${type}`, data);
      
      // 示例：记录到数据库或发送到外部系统
      // await notificationService.send({
      //   type,
      //   data,
      //   timestamp: new Date(),
      //   severity: type === 'MONITOR_ERROR' ? 'HIGH' : 'MEDIUM'
      // });
      
    } catch (error) {
      console.error('发送告警失败:', error);
    }
  }

  /**
   * 获取监控状态
   */
  public getStatus() {
    return {
      isRunning: this.isRunning,
      contractAddress: this.contractAddress,
      providerUrl: this.provider._getConnection().url,
      nextCheck: this.cronJob ? 'Every 10 minutes' : 'Not scheduled'
    };
  }

  /**
   * 手动执行一次检查
   */
  public async runManualCheck(): Promise<void> {
    console.log('🔍 手动执行PHRS余额检查...');
    await this.runBalanceCheck();
  }
}

// 创建单例实例
export const phrsBalanceMonitor = new PhrsBalanceMonitor();

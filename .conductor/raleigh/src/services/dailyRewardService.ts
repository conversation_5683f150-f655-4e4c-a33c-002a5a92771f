// src/services/dailyRewardService.ts
import { Op } from "sequelize";
import { User } from "../models/User";
import { Chest } from "../models/Chest";
import { UserDailyClaim } from "../models/UserDailyClaim";
import { invitationRewards } from "../config/consts";
import { UserWallet } from "../models/UserWallet";
import dayjs from "dayjs";
import { t } from "../i18n";
import { processOpenChest } from "./chestService";
import { sequelize } from "../config/db";
/**
 * 给用户发放当日可领取的“神秘宝箱”
 * 根据用户的 referralCount 与 invitationRewards 做匹配。
 */
export async function claimDailyBox(userId: number,walletId:number) {
  // 使用事务确保数据一致性
  const transaction = await sequelize.transaction();

  try {
    // 1) 查找用户
    const user = await User.findByPk(userId);
    if (!user) throw new Error(t("errors.userNotFound"));

  // 2) 确认今天是否已领取
  const today = dayjs().format('YYYY-MM-DD'); // 'YYYY-MM-DD'
  const existingClaim = await UserDailyClaim.findOne({
    where: { walletId: walletId, userId: userId, date: today },
  });
  if (existingClaim) {
    throw new Error(t("errors.alreadyClaimedToday"));
  }

  // 3) 根据 user.referralCount 寻找对应奖励
  const count = user.referralCount!;
  // 找出“我能领多少宝箱”
  let dailyChests = 0;
  // 记录达成的邀请级别
  let invitesNeededLevel = 0;
  // 遍历 config
  for (const r of invitationRewards) {
    // 如果我当前邀请数 >= r.invitesNeeded，说明我已达成这个档位
    if (count >= r.invitesNeeded && r.dailyChests > dailyChests) {
      dailyChests = r.dailyChests;
      invitesNeededLevel = r.invitesNeeded; // 记录当前达成的邀请级别
    }
  }

  if (dailyChests === 0) {
    // 表示没有任何一个档位被满足
    throw new Error(t("errors.notEnoughInvites"));
  }

  // 4) 记录当日已领取
  await UserDailyClaim.create({
    walletId: walletId,
    userId: user.id,
    date: today,
    invitesNeededLevel: invitesNeededLevel, // 记录领取时的邀请级别
  });

  // 5) 发放宝箱
  const chestData = [];
  for (let i = 0; i < dailyChests; i++) {
    chestData.push({
      walletId: walletId,
      userId: user.id,
      isOpened: false,
      type: 'daily'  // 添加类型：每日宝箱
    });
  }
  const createdChests = await Chest.bulkCreate(chestData, { transaction });

  // 6) 直接打开宝箱
  const openResult = await processOpenChest({
    userId: user.id,
    walletId: walletId,
    chestType: 'daily',
    count: dailyChests,
    transaction
  });

  await transaction.commit();

  return {
    dailyChests,
    rewards: openResult
  };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// src/services/ServiceManager.ts
import { Queue } from 'bullmq';
import cron from 'node-cron';
import { redis } from '../config/redis';
import { Server } from 'http';
import { sequelize } from '../config/db';

// Worker imports
import * as lotteryResultWorker from '../jobs/lotteryResultWorker';
import * as moofHoldersRewardWorker from '../jobs/moofHoldersRewardWorker';
import * as personalKolRewardWorker from '../jobs/personalKolRewardWorker';
import * as teamKolRewardWorker from '../jobs/teamKolRewardWorker';
import * as dailyRebateSettlementWorker from '../jobs/dailyRebateSettlementWorker';
import * as jackpotChestWorker from '../jobs/jackpotChestWorker';
import * as withdrawalWorker from '../jobs/withdrawalWorker';
import * as kaiaPriceUpdateWorker from '../jobs/kaiapriceUpdateWorker';
import * as phrsPriceUpdateWorker from '../jobs/phrsPriceUpdateWorker';

// Scheduler imports
// DappPortal payment status updater has been removed

export class ServiceManager {
  private queues: { [key: string]: Queue } = {};
  private cronTasks: cron.ScheduledTask[] = [];
  private serverInstance: Server | null = null;

  public setServerInstance(server: Server) {
    this.serverInstance = server;
  }

  public async initializeQueues() {
    console.log('正在初始化队列和处理器...');

    // 创建所有队列
    this.queues.moofHoldersRewardQueue = new Queue('moof-holders-reward-job', { connection: redis });
    this.queues.personalKolRewardQueue = new Queue('personal-kol-reward-job', { connection: redis });
    this.queues.teamKolRewardQueue = new Queue('team-kol-reward-job', { connection: redis });
    this.queues.dailyRebateSettlementQueue = new Queue('daily-rebate-settlement-job', { connection: redis });
    this.queues.jackpotChestQueue = new Queue('jackpot-chest-queue', { connection: redis });
    this.queues.withdrawalQueue = new Queue('withdrawal-queue', { connection: redis });
    this.queues.kaiaPriceUpdateQueue = new Queue('kaia-price-update-job', { connection: redis });
    this.queues.phrsPriceUpdateQueue = new Queue('phrs-price-update-job', { connection: redis });

    // 初始化处理器
    await this.initializeWorkers();

    return this.queues;
  }

  private async initializeWorkers() {
    console.log('正在初始化各模块处理器...');

    const workerInitializers = [
      { name: '抽奖结果处理器', worker: lotteryResultWorker, param: this.queues },
      { name: 'MOOF 持有者奖励处理器', worker: moofHoldersRewardWorker, param: this.queues.moofHoldersRewardQueue },
      { name: '个人 KOL 奖励处理器', worker: personalKolRewardWorker, param: this.queues.personalKolRewardQueue },
      { name: '团队 KOL 奖励处理器', worker: teamKolRewardWorker, param: this.queues.teamKolRewardQueue },
      { name: '每日返利结算处理器', worker: dailyRebateSettlementWorker, param: this.queues.dailyRebateSettlementQueue },
      { name: 'Jackpot 宝箱处理器', worker: jackpotChestWorker, param: this.queues.jackpotChestQueue },
      { name: '提现处理器', worker: withdrawalWorker, param: this.queues.withdrawalQueue },
      { name: 'KAIA价格更新处理器', worker: kaiaPriceUpdateWorker, param: this.queues.kaiaPriceUpdateQueue },
      { name: 'PHRS价格更新处理器', worker: phrsPriceUpdateWorker, param: this.queues.phrsPriceUpdateQueue }
    ];

    for (const { name, worker, param } of workerInitializers) {
      try {
        if (worker.initializeWorker) {
          await worker.initializeWorker(param);
          console.log(`${name}已初始化`);
        }
      } catch (error) {
        console.error(`初始化${name}失败:`, error);
      }
    }
  }

  public setupCronJobs() {
    // 清空之前的任务
    this.cronTasks = [];

    // DappPortal支付状态更新任务已移除，使用PHRS支付系统

    // MOOF 持有者奖励 - 每周日晚上 23:59
    const moofTask = cron.schedule('59 23 * * 0', async () => {
      await this.addJobSafely(
        this.queues.moofHoldersRewardQueue,
        'weekly-distribution',
        '本周 MOOF 持有者奖励分发任务'
      );
    }, { scheduled: true, timezone: "Asia/Shanghai" });
    this.cronTasks.push(moofTask);

    // 个人 KOL 奖励 - 每周一凌晨 00:30
    const personalKolTask = cron.schedule('30 0 * * 1', async () => {
      await this.addJobSafely(
        this.queues.personalKolRewardQueue,
        'weekly-distribution',
        '本周个人 KOL 奖励分发任务'
      );
    }, { scheduled: true, timezone: "Asia/Shanghai" });
    this.cronTasks.push(personalKolTask);

    // 团队 KOL 奖励 - 每周一凌晨 01:00
    const teamKolTask = cron.schedule('0 1 * * 1', async () => {
      await this.addJobSafely(
        this.queues.teamKolRewardQueue,
        'weekly-distribution',
        '本周团队 KOL 奖励分发任务'
      );
    }, { scheduled: true, timezone: "Asia/Shanghai" });
    this.cronTasks.push(teamKolTask);

    // 每日返利结算 - 每天12:00
    const rebateTask = cron.schedule('0 12 * * *', async () => {
      await this.addJobSafely(
        this.queues.dailyRebateSettlementQueue,
        'daily-settlement',
        '每日返利结算任务'
      );
    }, { scheduled: true, timezone: "Asia/Shanghai" });
    this.cronTasks.push(rebateTask);

    // PHRS 价格更新 - 每小时执行一次
    const phrsPriceTask = cron.schedule('0 * * * *', async () => {
      await this.addJobSafely(
        this.queues.phrsPriceUpdateQueue,
        'phrs-price-update',
        'PHRS价格更新任务'
      );
    }, { scheduled: true, timezone: "Asia/Shanghai" });
    this.cronTasks.push(phrsPriceTask);

    console.log('所有定时任务已设置完成');
  }

  private async addJobSafely(queue: Queue, jobName: string, description: string) {
    try {
      await queue.add(jobName, {}, {
        removeOnComplete: true,
        removeOnFail: 1000,
      });
      console.log(`已添加${description}`);
    } catch (error) {
      console.error(`添加${description}失败:`, error);
    }
  }

  public async initializeJackpotTasks() {
    // 初始化Jackpot奖池任务
    await this.queues.jackpotChestQueue.add('initialize-jackpot', {}, {
      removeOnComplete: true,
      removeOnFail: 1000
    });
    console.log('已添加 Jackpot 奖池初始化任务');

    // 设置 Jackpot 自动领取定时任务
    await this.queues.jackpotChestQueue.add('auto-collect-chests', {}, {
      repeat: {
        pattern: '* * * * *' // 每分钟执行一次
      },
      jobId: 'auto-collect-chests-job',
      removeOnComplete: true,
      removeOnFail: 1000
    });
    console.log('已添加 Jackpot 自动领取定时任务');
  }

  public async gracefulShutdown(reason: string) {
    console.log(`收到 ${reason}，正在关闭服务...`);
    
    try {
      // 1. 停止所有定时任务
      console.log('正在停止所有定时任务...');
      this.cronTasks.forEach(task => {
        try {
          task.stop();
        } catch (err) {
          console.error('停止定时任务失败:', err);
        }
      });

      // DappPortal支付状态更新任务已移除
      console.log('所有定时任务已停止');

      // 2. 移除所有重复任务
      console.log('正在移除所有重复任务...');
      const removeRepeatablePromises = Object.entries(this.queues).map(async ([name, queue]) => {
        try {
          const repeatableJobs = await queue.getJobSchedulers();
          const removePromises = repeatableJobs.map(job => 
            queue.removeJobScheduler(job.name)
              .catch(err => console.error(`移除重复任务 ${job.id} 失败:`, err))
          );
          await Promise.allSettled(removePromises);
          console.log(`${name} 队列的重复任务已移除`);
        } catch (err) {
          console.error(`获取 ${name} 队列的重复任务失败:`, err);
        }
      });
      await Promise.allSettled(removeRepeatablePromises);
      console.log('所有重复任务已移除');

      // 3. 关闭 HTTP 服务器
      if (this.serverInstance) {
        await this.closeServer();
      }

      // 4. 关闭所有 BullMQ 队列
      console.log('正在关闭 BullMQ 队列...');
      const queueClosePromises = Object.entries(this.queues).map(([name, queue]) =>
        queue.close()
          .then(() => console.log(`${name} 队列已关闭`))
          .catch(err => console.error(`关闭 ${name} 队列失败:`, err))
      );
      await Promise.allSettled(queueClosePromises);
      console.log('所有 BullMQ 队列已关闭');

      // 5. 关闭 Redis 连接
      try {
        await redis.quit();
        console.log('Redis 连接已关闭');
      } catch (error) {
        console.error('关闭 Redis 连接失败:', error);
      }

      // 6. 关闭数据库连接
      try {
        await sequelize.close();
        console.log('数据库连接已关闭');
      } catch (error) {
        console.error('关闭数据库连接失败:', error);
      }

      console.log('所有资源已清理完毕，正在退出...');
      
      // 设置最终退出超时
      setTimeout(() => {
        console.warn('进程未能在预期时间内退出，强制退出');
        process.exit(0);
      }, 3000);
      
      process.exit(0);
    } catch (error) {
      console.error('关闭服务时发生严重错误:', error);
      await this.forceCleanup();
      process.exit(1);
    }
  }

  private async closeServer(): Promise<void> {
    return new Promise<void>((resolve) => {
      try {
        if (this.serverInstance?.listening) {
          this.serverInstance.close((err) => {
            if (err) {
              console.error('关闭 HTTP 服务器时出错:', err);
            }
            console.log('HTTP 服务器已关闭');
            resolve();
          });
        } else {
          console.log('HTTP 服务器未在监听状态，无需关闭');
          resolve();
        }
      } catch (err) {
        console.error('尝试关闭 HTTP 服务器时发生异常:', err);
        resolve();
      }
    });
  }

  private async forceCleanup() {
    try {
      await Promise.allSettled([
        sequelize.close().catch(e => console.error("强制关闭数据库失败:", e)),
        redis.quit().catch(e => console.error("强制关闭Redis失败:", e))
      ]);
    } catch (e) {
      console.error("最终清理资源失败:", e);
    }
  }
}

export const serviceManager = new ServiceManager();

// src/services/gameHistoryService.ts
import { GameHistory, Reservation, Room, User, UserWallet } from "../models";
import { Transaction, Op } from "sequelize";
import { GAME_CONFIG } from "../config/consts";
import { t } from "../i18n";
interface CreateGameHistoryParams {
  userId: number;
  walletId: number;
  sessionId: number;
  roundIndex: number;
  roomId: number;
  betAmount: number;
  ticketType?: string; // 添加票类型字段
  transaction?: Transaction;
}

/**
 * 创建游戏历史记录
 */
export async function createGameHistory(params: CreateGameHistoryParams) {
  const {
    userId,
    walletId,
    sessionId,
    roundIndex,
    roomId,
    betAmount,
    ticketType = 'ticket', // 默认使用普通票
    transaction
  } = params;

  return await GameHistory.create({
    userId,
    walletId,
    sessionId: sessionId,
    round: roundIndex,
    roomId: roomId.toString(),
    betAmount,
    game_status: 'pending',
    payout_status: 'pending',
    payout: 0,
    is_moof: false,
    ticketType, // 添加票类型字段
    createdAt: new Date()
  }, { transaction });
}

/**
 * 更新游戏历史记录状态
 */
export async function updateGameHistoryStatus({
  roomId,
  walletId,
  sessionId,
  round,
  game_status,
  payout_status,
  payout,
  is_moof,
  transaction
}: {
  roomId: string;
  walletId: number;
  sessionId: number;
  round: number;
  game_status: string;
  payout_status: string;
  payout: number;
  is_moof: boolean;
  transaction?: Transaction;
}) {
  return await GameHistory.update(
    {
      game_status,
      payout_status,
      payout,
      is_moof
    },
    {
      where: {
        roomId,
        walletId,
        sessionId,
        round
      },
      transaction
    }
  );
}

/**
 * 查询用户游戏历史记录
 */
export async function queryGameHistory({
  userId,
  startDate,
  endDate,
  game_status,
  limit = 10,
  offset = 0
}: {
  userId: number;
  startDate?: Date;
  endDate?: Date;
  game_status?: string;
  limit?: number;
  offset?: number;
}) {
  const whereClause: any = { userId };

  if (startDate && endDate) {
    whereClause.createdAt = {
      [Op.between]: [startDate, endDate]
    };
  }

  if (game_status) {
    whereClause.game_status = game_status;
  }

  return await GameHistory.findAndCountAll({
    where: whereClause,
    limit,
    offset,
    order: [["createdAt", "DESC"]]
  });
}

/**
 * 查询房间详细信息，包括所有玩家和胜利者
 */
export async function getRoomDetailsBySessionAndRound(
  sessionId: number,
  roundIndex: number,
  walletId?: number,
  includeGameStatus: boolean = false
) {
  try {
    // 查找用户所在的房间
    let userRoom = null;
    if (walletId) {
      const reservation = await Reservation.findOne({
        where: {
          sessionId,
          roundIndex,
          walletId
        }
      });
      
      if (reservation) {
        userRoom = reservation.roomId;
      }
    }
    
    // 如果没有指定钱包ID或找不到对应预约，则查找该回合的所有房间
    const roomsQuery = {
      where: {
        sessionId,
        roundIndex,
        ...(userRoom ? { id: userRoom } : {})
      },
      order: [['id', 'ASC']]
    };
    //@ts-ignore
    const rooms = await Room.findAll(roomsQuery);
    
    if (rooms.length === 0) {
      return { success: false, message: t('errors.roomNotFound') };
    }
    
    // 获取每个房间的详细信息
    const roomsDetails = await Promise.all(rooms.map(async (room) => {
      // 获取房间内的所有预约
      const reservations = await Reservation.findAll({
        where: { roomId: room.id, sessionId, roundIndex },
        include: [
          {
            model: UserWallet,
            attributes: ['id', 'userId', 'walletAddress'],
            include: [
              {
                model: User,
                attributes: ['id', 'username', 'photoUrl']
              }
            ]
          }
        ],
        order: [['id', 'ASC']]
      });
      
      // 获取房间的游戏历史记录，找出胜利者
      const gameHistories = await GameHistory.findAll({
        where: {
          roomId: room.id.toString(),
          sessionId,
          round: roundIndex
        }
      });
      
      // 找出胜利者（获得MOOF的玩家）
      const winner = gameHistories.find(history => history.is_moof === true);
      
      // 计算胜利者在房间中的索引
      let winnerIndex = -1;
      if (winner) {
        winnerIndex = reservations.findIndex(r => r.walletId === winner.walletId);
      } else if (room.result_position) {
        // 如果没有找到胜利者但房间有result_position，使用它
        winnerIndex = room.result_position - 1; // 因为索引从0开始，而result_position从1开始
      }
      
      // 格式化玩家信息
      const players = await Promise.all(reservations.map(async (reservation, index) => {
        // 如果需要包含游戏状态，则查询每个玩家的游戏历史
        let gameStatus = null;
        let payoutStatus = null;
        let payout = 0;
        let isMoof = false;
        
        if (includeGameStatus) {
          const playerGameHistory = gameHistories.find(history => 
            history.walletId === reservation.walletId
          );
          
          if (playerGameHistory) {
            gameStatus = playerGameHistory.game_status;
            payoutStatus = playerGameHistory.payout_status;
            payout = playerGameHistory.payout;
            isMoof = playerGameHistory.is_moof;
          }
        }
        
        return {
          index: index,
          walletId: reservation.walletId,
          userId: reservation.userId,
          walletAddress: reservation.UserWallet?.walletAddress || null,
          username: reservation.UserWallet?.User?.username || null,
          photoUrl: reservation.UserWallet?.User?.photoUrl || null,
          status: reservation.status,
          isWinner: index === winnerIndex,
          ...(includeGameStatus && {
            gameStatus,
            payoutStatus,
            payout,
            isMoof
          })
        };
      }));
      
      // 获取房间的整体游戏状态
      let roomGameStatus = 'pending';
      
      // 判断房间人数是否不足导致退款
      const minRequiredPlayers = GAME_CONFIG.MAX_PARTICIPANTS; // 假设最少需要3人才能开始游戏，根据实际需求调整
      
      if (room.lotteryProcessed) {
        roomGameStatus = 'done';
      } else if (reservations.length < minRequiredPlayers) {
        // 人数不足状态
        roomGameStatus = 'refunded_insufficient_players';
      } else if (gameHistories.length > 0 && gameHistories.every(h => h.game_status === 'done')) {
        roomGameStatus = 'done';
      }
      
      // 检查是否所有玩家都获得了退款
      const allRefunded = gameHistories.length > 0 && 
                          gameHistories.every(h => h.payout_status === 'refunded');
      
      if (allRefunded) {
        roomGameStatus = 'refunded_insufficient_players';
      }
      
      return {
        roomId: room.id,
        sessionId,
        roundIndex,
        playerCount: players.length,
        winnerIndex: winnerIndex >= 0 ? winnerIndex : null,
        seqno: room.seqno,
        lotteryProcessed: room.lotteryProcessed,
        gameStatus: roomGameStatus,
        players
      };
    }));
    
    return {
      success: true,
      rooms: roomsDetails
    };
  } catch (error) {
    console.error(t('errors.getRoomDetailsFailed'), error);
    return {
      success: false,
      message: t('errors.getRoomDetailsFailed'),
      //@ts-ignore
      error: error.message
    };
  }
}
// src/services/boosterMutexService.ts

import { ActiveBooster } from '../models/ActiveBooster';
import { Op } from 'sequelize';
import dayjs from 'dayjs';

/**
 * 加速道具互斥检查服务
 * 负责检查和管理加速道具之间的互斥关系
 */
export class BoosterMutexService {

  /**
   * 检查用户是否已有激活的Speed Boost道具
   * @param walletId 钱包ID
   * @returns 激活的Speed Boost道具信息，如果没有则返回null
   */
  static async getActiveSpeedBoost(walletId: number): Promise<ActiveBooster | null> {
    try {
      const activeSpeedBoost = await ActiveBooster.findOne({
        where: {
          walletId,
          type: 'speed_boost',
          status: 'active',
          endTime: { [Op.gt]: dayjs().toDate() }
        },
        order: [['endTime', 'DESC']] // 获取最晚结束的道具
      });

      return activeSpeedBoost;
    } catch (error) {
      console.error('检查激活Speed Boost道具失败:', error);
      return null;
    }
  }

  /**
   * 检查用户是否已有激活的Time Warp道具
   * @param walletId 钱包ID
   * @returns 激活的Time Warp道具信息，如果没有则返回null
   */
  static async getActiveTimeWarp(walletId: number): Promise<ActiveBooster | null> {
    try {
      const activeTimeWarp = await ActiveBooster.findOne({
        where: {
          walletId,
          type: 'time_warp',
          status: 'active',
          endTime: { [Op.gt]: dayjs().toDate() }
        },
        order: [['endTime', 'DESC']] // 获取最晚结束的道具
      });

      return activeTimeWarp;
    } catch (error) {
      console.error('检查激活Time Warp道具失败:', error);
      return null;
    }
  }

  /**
   * 检查是否可以使用指定类型的道具
   * @param walletId 钱包ID
   * @param boosterType 要使用的道具类型
   * @returns 检查结果
   */
  static async canUseBooster(walletId: number, boosterType: 'speed_boost' | 'time_warp'): Promise<{
    canUse: boolean;
    reason?: string;
    conflictingBooster?: ActiveBooster;
  }> {
    try {
      if (boosterType === 'speed_boost') {
        // 检查是否已有激活的Speed Boost道具
        const activeSpeedBoost = await this.getActiveSpeedBoost(walletId);
        
        if (activeSpeedBoost) {
          return {
            canUse: false,
            reason: 'SPEED_BOOST_ALREADY_ACTIVE',
            conflictingBooster: activeSpeedBoost
          };
        }
      } else if (boosterType === 'time_warp') {
        // Time Warp道具不受互斥限制，可以在任何时候使用
        // 但我们仍然检查是否有冲突，以便提供信息
        const activeSpeedBoost = await this.getActiveSpeedBoost(walletId);
        
        // Time Warp可以使用，但我们返回当前激活的Speed Boost信息供参考
        return {
          canUse: true,
          conflictingBooster: activeSpeedBoost || undefined
        };
      }

      return { canUse: true };
    } catch (error) {
      console.error('检查道具使用权限失败:', error);
      return {
        canUse: false,
        reason: 'CHECK_FAILED'
      };
    }
  }

  /**
   * 获取用户所有激活的加速道具
   * @param walletId 钱包ID
   * @returns 激活的加速道具列表
   */
  static async getAllActiveBoosters(walletId: number): Promise<{
    speedBoosts: ActiveBooster[];
    timeWarps: ActiveBooster[];
  }> {
    try {
      const activeBoosters = await ActiveBooster.findAll({
        where: {
          walletId,
          status: 'active',
          endTime: { [Op.gt]: dayjs().toDate() }
        },
        order: [['endTime', 'ASC']]
      });

      const speedBoosts = activeBoosters.filter(booster => booster.type === 'speed_boost');
      const timeWarps = activeBoosters.filter(booster => booster.type === 'time_warp');

      return {
        speedBoosts,
        timeWarps
      };
    } catch (error) {
      console.error('获取激活道具列表失败:', error);
      return {
        speedBoosts: [],
        timeWarps: []
      };
    }
  }

  /**
   * 获取道具互斥状态的详细信息
   * @param walletId 钱包ID
   * @returns 互斥状态详情
   */
  static async getBoosterMutexStatus(walletId: number): Promise<{
    hasActiveSpeedBoost: boolean;
    hasActiveTimeWarp: boolean;
    activeSpeedBoost?: ActiveBooster;
    activeTimeWarp?: ActiveBooster;
    canUseSpeedBoost: boolean;
    canUseTimeWarp: boolean;
  }> {
    try {
      const activeSpeedBoost = await this.getActiveSpeedBoost(walletId);
      const activeTimeWarp = await this.getActiveTimeWarp(walletId);

      return {
        hasActiveSpeedBoost: !!activeSpeedBoost,
        hasActiveTimeWarp: !!activeTimeWarp,
        activeSpeedBoost: activeSpeedBoost || undefined,
        activeTimeWarp: activeTimeWarp || undefined,
        canUseSpeedBoost: !activeSpeedBoost, // 只有没有激活Speed Boost时才能使用新的
        canUseTimeWarp: true // Time Warp总是可以使用
      };
    } catch (error) {
      console.error('获取道具互斥状态失败:', error);
      return {
        hasActiveSpeedBoost: false,
        hasActiveTimeWarp: false,
        canUseSpeedBoost: true,
        canUseTimeWarp: true
      };
    }
  }

  /**
   * 格式化剩余时间为人类可读格式
   * @param endTime 结束时间
   * @returns 格式化的剩余时间字符串
   */
  static formatRemainingTime(endTime: Date): string {
    const now = dayjs();
    const end = dayjs(endTime);
    const diffMinutes = end.diff(now, 'minute');
    
    if (diffMinutes <= 0) {
      return '已过期';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟`;
    } else {
      const hours = Math.floor(diffMinutes / 60);
      const minutes = diffMinutes % 60;
      return minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`;
    }
  }
}

// src/services/analyticsService.ts

import { sequelize } from "../config/db";
import { QueryTypes, Op } from "sequelize";
import { UserWallet, User, FarmPlot, DeliveryLine, Tasks, UserTaskComplete, IapPurchase, Chest, ActiveBooster } from "../models";
import { createBigNumber } from '../utils/bigNumberConfig';

/**
 * 管理后台数据分析服务
 * 提供游戏运营所需的各种统计数据
 */
export class AnalyticsService {

  /**
   * 获取总每日游戏启动次数
   * @param date 指定日期，默认为今天
   */
  public static async getDailyGameStarts(date?: string): Promise<number> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    
    const result = await sequelize.query(
      `SELECT COUNT(*) as count 
       FROM user_wallets 
       WHERE DATE(lastActiveTime) = :date`,
      {
        replacements: { date: targetDate },
        type: QueryTypes.SELECT,
      }
    );
    
    return (result[0] as any)?.count || 0;
  }

  /**
   * 获取解锁区域2-20的人数统计
   */
  public static async getUnlockedAreasStats(): Promise<{ area: number; count: number }[]> {
    const result = await sequelize.query(
      `SELECT plotNumber as area, COUNT(DISTINCT walletId) as count
       FROM farm_plots 
       WHERE isUnlocked = true AND plotNumber BETWEEN 2 AND 20
       GROUP BY plotNumber
       ORDER BY plotNumber`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    return result as { area: number; count: number }[];
  }

  /**
   * 获取升级流水线到指定等级的人数
   * @param levels 等级数组，如 [10, 20, 25, 30, 35, 40, 45, 50]
   */
  public static async getDeliveryLineUpgradeStats(levels: number[] = [10, 20, 25, 30, 35, 40, 45, 50]): Promise<{ level: number; count: number }[]> {
    const result = await sequelize.query(
      `SELECT level, COUNT(*) as count
       FROM delivery_lines 
       WHERE level IN (:levels)
       GROUP BY level
       ORDER BY level`,
      {
        replacements: { levels },
        type: QueryTypes.SELECT,
      }
    );
    
    return result as { level: number; count: number }[];
  }

  /**
   * 获取每种道具的总使用人数和次数
   */
  public static async getBoosterUsageStats(): Promise<{
    totalUsers: { type: string; count: number }[];
    totalUsage: { type: string; count: number }[];
  }> {
    // 总使用人数
    const totalUsers = await sequelize.query(
      `SELECT type, COUNT(DISTINCT walletId) as count
       FROM active_boosters 
       GROUP BY type`,
      {
        type: QueryTypes.SELECT,
      }
    );

    // 总使用次数
    const totalUsage = await sequelize.query(
      `SELECT type, COUNT(*) as count
       FROM active_boosters 
       GROUP BY type`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    return {
      totalUsers: totalUsers as { type: string; count: number }[],
      totalUsage: totalUsage as { type: string; count: number }[]
    };
  }

  /**
   * 获取玩家总充值数据
   */
  public static async getRevenueStats(): Promise<{
    totalRevenue: number;
    totalPurchases: number;
    uniquePayers: number;
    revenueByMethod: { method: string; amount: number; count: number }[];
  }> {
    // 总充值金额和次数
    const totalStats = await sequelize.query(
      `SELECT 
        SUM(amount) as totalRevenue,
        COUNT(*) as totalPurchases,
        COUNT(DISTINCT walletId) as uniquePayers
       FROM iap_purchases 
       WHERE status IN ('CONFIRMED', 'FINALIZED')`,
      {
        type: QueryTypes.SELECT,
      }
    );

    // 按支付方式分组
    const revenueByMethod = await sequelize.query(
      `SELECT 
        paymentMethod as method,
        SUM(amount) as amount,
        COUNT(*) as count
       FROM iap_purchases 
       WHERE status IN ('CONFIRMED', 'FINALIZED')
       GROUP BY paymentMethod`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    const stats = totalStats[0] as any;
    return {
      totalRevenue: parseFloat(stats?.totalRevenue || 0),
      totalPurchases: stats?.totalPurchases || 0,
      uniquePayers: stats?.uniquePayers || 0,
      revenueByMethod: revenueByMethod as { method: string; amount: number; count: number }[]
    };
  }

  /**
   * 获取玩家每日开启APP次数
   * @param days 过去几天，默认7天
   */
  public static async getDailyAppOpens(days: number = 7): Promise<{ date: string; count: number }[]> {
    const result = await sequelize.query(
      `SELECT 
        DATE(lastActiveTime) as date,
        COUNT(DISTINCT id) as count
       FROM user_wallets 
       WHERE lastActiveTime >= DATE_SUB(NOW(), INTERVAL :days DAY)
       GROUP BY DATE(lastActiveTime)
       ORDER BY date DESC`,
      {
        replacements: { days },
        type: QueryTypes.SELECT,
      }
    );
    
    return result as { date: string; count: number }[];
  }

  /**
   * 获取升级牧场区域1-20到指定等级的人数
   * @param levels 等级数组
   */
  public static async getFarmPlotUpgradeStats(levels: number[] = [10, 20, 25, 30, 35, 40, 45, 50]): Promise<{
    area: number;
    levelStats: { level: number; count: number }[];
  }[]> {
    const result = await sequelize.query(
      `SELECT 
        plotNumber as area,
        level,
        COUNT(*) as count
       FROM farm_plots 
       WHERE plotNumber BETWEEN 1 AND 20 
       AND level IN (:levels)
       GROUP BY plotNumber, level
       ORDER BY plotNumber, level`,
      {
        replacements: { levels },
        type: QueryTypes.SELECT,
      }
    );
    
    // 重组数据结构
    const statsMap = new Map<number, { level: number; count: number }[]>();
    (result as any[]).forEach(row => {
      if (!statsMap.has(row.area)) {
        statsMap.set(row.area, []);
      }
      statsMap.get(row.area)!.push({ level: row.level, count: row.count });
    });
    
    return Array.from(statsMap.entries()).map(([area, levelStats]) => ({
      area,
      levelStats
    }));
  }

  /**
   * 获取玩家完成任务统计
   */
  public static async getTaskCompletionStats(): Promise<{
    totalCompletions: number;
    taskStats: { taskId: number; taskName: string; completions: number }[];
  }> {
    // 总完成次数
    const totalResult = await sequelize.query(
      `SELECT COUNT(*) as count FROM user_task_complete`,
      { type: QueryTypes.SELECT }
    );

    // 每个任务的完成人数
    const taskStats = await sequelize.query(
      `SELECT 
        utc.taskId,
        t.name as taskName,
        COUNT(DISTINCT utc.walletId) as completions
       FROM user_task_complete utc
       LEFT JOIN tasks t ON utc.taskId = t.id
       GROUP BY utc.taskId, t.name
       ORDER BY utc.taskId`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    return {
      totalCompletions: (totalResult[0] as any)?.count || 0,
      taskStats: taskStats as { taskId: number; taskName: string; completions: number }[]
    };
  }

  /**
   * 获取玩家邀请好友统计
   */
  public static async getReferralStats(): Promise<{
    totalInvitations: number;
    referralDistribution: { range: string; count: number }[];
  }> {
    // 总邀请数
    const totalResult = await sequelize.query(
      `SELECT SUM(referralCount) as total FROM user_wallets WHERE referralCount > 0`,
      { type: QueryTypes.SELECT }
    );

    // 邀请人数分布
    const distribution = await sequelize.query(
      `SELECT 
        CASE 
          WHEN referralCount = 1 THEN '1人'
          WHEN referralCount BETWEEN 2 AND 4 THEN '2-4人'
          WHEN referralCount = 5 THEN '5人'
          WHEN referralCount BETWEEN 6 AND 9 THEN '6-9人'
          WHEN referralCount = 10 THEN '10人'
          WHEN referralCount BETWEEN 11 AND 19 THEN '11-19人'
          WHEN referralCount = 20 THEN '20人'
          WHEN referralCount BETWEEN 21 AND 29 THEN '21-29人'
          WHEN referralCount = 30 THEN '30人'
          WHEN referralCount BETWEEN 31 AND 39 THEN '31-39人'
          WHEN referralCount = 40 THEN '40人'
          WHEN referralCount BETWEEN 41 AND 49 THEN '41-49人'
          WHEN referralCount = 50 THEN '50人'
          WHEN referralCount BETWEEN 51 AND 59 THEN '51-59人'
          WHEN referralCount = 60 THEN '60人'
          WHEN referralCount BETWEEN 61 AND 69 THEN '61-69人'
          WHEN referralCount = 70 THEN '70人'
          WHEN referralCount BETWEEN 71 AND 79 THEN '71-79人'
          WHEN referralCount = 80 THEN '80人'
          WHEN referralCount BETWEEN 81 AND 89 THEN '81-89人'
          WHEN referralCount = 90 THEN '90人'
          WHEN referralCount BETWEEN 91 AND 99 THEN '91-99人'
          WHEN referralCount = 100 THEN '100人'
          WHEN referralCount BETWEEN 101 AND 199 THEN '101-199人'
          WHEN referralCount = 200 THEN '200人'
          ELSE '200+人'
        END as \`range\`,
        COUNT(*) as count
       FROM user_wallets 
       WHERE referralCount > 0
       GROUP BY \`range\`
       ORDER BY MIN(referralCount)`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    return {
      totalInvitations: (totalResult[0] as any)?.total || 0,
      referralDistribution: distribution as { range: string; count: number }[]
    };
  }

  /**
   * 获取玩家宝箱统计
   */
  public static async getChestStats(): Promise<{
    totalChests: number;
    dailyChestClaimers: number;
    chestsByType: { type: string; count: number }[];
  }> {
    // 总宝箱数
    const totalResult = await sequelize.query(
      `SELECT COUNT(*) as count FROM chests`,
      { type: QueryTypes.SELECT }
    );

    // 每日宝箱领取人数（今天）
    const dailyResult = await sequelize.query(
      `SELECT COUNT(DISTINCT walletId) as count 
       FROM chests 
       WHERE type = 'daily' 
       AND DATE(createdAt) = CURDATE()`,
      { type: QueryTypes.SELECT }
    );

    // 按类型分组
    const typeStats = await sequelize.query(
      `SELECT type, COUNT(*) as count
       FROM chests 
       GROUP BY type`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    return {
      totalChests: (totalResult[0] as any)?.count || 0,
      dailyChestClaimers: (dailyResult[0] as any)?.count || 0,
      chestsByType: typeStats as { type: string; count: number }[]
    };
  }

  /**
   * 获取玩家数量和留存统计
   */
  public static async getPlayerGrowthAndRetention(): Promise<{
    dailyNewUsers: { date: string; count: number }[];
    totalUsers7Days: number;
    dayOneRetention: number;
    day7Retention: number;
  }> {
    // 每日新增用户（过去7天）
    const dailyNew = await sequelize.query(
      `SELECT 
        DATE(createdAt) as date,
        COUNT(*) as count
       FROM user_wallets 
       WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
       GROUP BY DATE(createdAt)
       ORDER BY date DESC`,
      {
        type: QueryTypes.SELECT,
      }
    );

    // 7日内总用户数
    const total7Days = await sequelize.query(
      `SELECT COUNT(*) as count 
       FROM user_wallets 
       WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)`,
      { type: QueryTypes.SELECT }
    );

    // 首日留存率计算
    const dayOneRetention = await sequelize.query(
      `SELECT 
        (COUNT(CASE WHEN DATEDIFF(lastActiveTime, createdAt) >= 1 THEN 1 END) * 100.0 / COUNT(*)) as retention
       FROM user_wallets
       WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      { type: QueryTypes.SELECT }
    );

    // 七日留存率计算
    const day7Retention = await sequelize.query(
      `SELECT 
        (COUNT(CASE WHEN DATEDIFF(lastActiveTime, createdAt) >= 7 THEN 1 END) * 100.0 / COUNT(*)) as retention
       FROM user_wallets
       WHERE createdAt <= DATE_SUB(NOW(), INTERVAL 7 DAY)
       AND createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)`,
      { type: QueryTypes.SELECT }
    );
    
    return {
      dailyNewUsers: dailyNew as { date: string; count: number }[],
      totalUsers7Days: (total7Days[0] as any)?.count || 0,
      dayOneRetention: parseFloat((dayOneRetention[0] as any)?.retention || 0),
      day7Retention: parseFloat((day7Retention[0] as any)?.retention || 0)
    };
  }

  /**
   * 获取玩家资源统计
   */
  public static async getPlayerResourceStats(): Promise<{
    totalGems: string;
    totalDiamonds: string;
    averageGems: string;
    averageDiamonds: string;
    topGemHolders: { walletId: number; username: string; gems: string }[];
  }> {
    // 总资源统计
    const totalStats = await sequelize.query(
      `SELECT 
        SUM(gem) as totalGems,
        SUM(diamond) as totalDiamonds,
        AVG(gem) as avgGems,
        AVG(diamond) as avgDiamonds
       FROM user_wallets`,
      { type: QueryTypes.SELECT }
    );

    // 宝石持有量排行前10
    const topHolders = await sequelize.query(
      `SELECT 
        uw.id as walletId,
        u.username,
        uw.gem as gems
       FROM user_wallets uw
       LEFT JOIN users u ON uw.userId = u.id
       ORDER BY uw.gem DESC
       LIMIT 10`,
      {
        type: QueryTypes.SELECT,
      }
    );
    
    const stats = totalStats[0] as any;
    return {
      totalGems: stats?.totalGems?.toString() || '0',
      totalDiamonds: stats?.totalDiamonds?.toString() || '0',
      averageGems: stats?.avgGems?.toString() || '0',
      averageDiamonds: stats?.avgDiamonds?.toString() || '0',
      topGemHolders: topHolders as { walletId: number; username: string; gems: string }[]
    };
  }

  /**
   * 获取玩家游玩时长统计
   * 注意：这个需要根据实际的活跃时间记录机制来计算
   */
  public static async getPlayTimeStats(): Promise<{
    averageDailyPlayTime: number; // 分钟
    totalActiveSessions: number;
    averageSessionLength: number; // 分钟
  }> {
    // 这里是一个简化的实现，实际可能需要更复杂的计算逻辑
    // 基于lastActiveTime和createdAt的差值来估算
    const stats = await sequelize.query(
      `SELECT 
        AVG(TIMESTAMPDIFF(MINUTE, createdAt, lastActiveTime)) as avgPlayTime,
        COUNT(*) as totalSessions
       FROM user_wallets 
       WHERE lastActiveTime IS NOT NULL 
       AND TIMESTAMPDIFF(MINUTE, createdAt, lastActiveTime) > 0`,
      { type: QueryTypes.SELECT }
    );
    
    const result = stats[0] as any;
    return {
      averageDailyPlayTime: parseFloat(result?.avgPlayTime || 0),
      totalActiveSessions: result?.totalSessions || 0,
      averageSessionLength: parseFloat(result?.avgPlayTime || 0) // 简化实现
    };
  }

  /**
   * 获取综合统计面板数据
   */
  public static async getDashboardStats(): Promise<{
    gameStats: any;
    playerProgress: any;
    revenue: any;
    retention: any;
    resources: any;
  }> {
    const [
      dailyStarts,
      unlockedAreas,
      deliveryUpgrades,
      boosterUsage,
      revenue,
      taskStats,
      referralStats,
      chestStats,
      growthRetention,
      resourceStats
    ] = await Promise.all([
      this.getDailyGameStarts(),
      this.getUnlockedAreasStats(),
      this.getDeliveryLineUpgradeStats(),
      this.getBoosterUsageStats(),
      this.getRevenueStats(),
      this.getTaskCompletionStats(),
      this.getReferralStats(),
      this.getChestStats(),
      this.getPlayerGrowthAndRetention(),
      this.getPlayerResourceStats()
    ]);

    return {
      gameStats: {
        dailyGameStarts: dailyStarts,
        unlockedAreas,
        deliveryUpgrades,
        boosterUsage,
        taskStats,
        chestStats
      },
      playerProgress: {
        referralStats,
        farmUpgrades: await this.getFarmPlotUpgradeStats()
      },
      revenue,
      retention: growthRetention,
      resources: resourceStats
    };
  }
}
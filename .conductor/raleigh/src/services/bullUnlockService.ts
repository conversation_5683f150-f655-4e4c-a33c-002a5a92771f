// src/services/bullUnlockService.ts
import { BullUnlockHistory } from "../models/BullUnlockHistory";
import { sequelize } from "../config/db";
import { Op } from "sequelize";
import { t } from "../i18n";

/**
 * 获取用户的 MOOF 解锁历史记录
 * @param userId 用户ID
 * @param page 页码
 * @param limit 每页记录数
 */
export async function getUserBullUnlockHistory(userId: number, page: number = 1, limit: number = 20) {
  try {
    const offset = (page - 1) * limit;
    
    // 查询用户的解锁历史记录
    const { rows, count } = await BullUnlockHistory.findAndCountAll({
      where: {
        userId
      },
      order: [['unlockDate', 'DESC']],
      limit,
      offset
    });

    // 计算总页数
    const totalPages = Math.ceil(count / limit);

    return {
      records: rows,
      pagination: {
        total: count,
        page,
        limit,
        totalPages
      }
    };
  } catch (error) {
    console.error(t("errors.getBullUnlockHistoryFailed"), error);
    throw error;
  }
}
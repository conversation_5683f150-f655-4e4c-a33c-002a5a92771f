// src/utils/logger.ts

/**
 * 日志工具，用于统一管理应用程序的日志记录
 * 提供不同级别的日志记录功能，并支持结构化日志
 */

interface LogData {
  [key: string]: any;
}

class Logger {
  /**
   * 记录信息级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  info(message: string, data?: LogData): void {
    this.log('INFO', message, data);
  }

  /**
   * 记录警告级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  warn(message: string, data?: LogData): void {
    this.log('WARN', message, data);
  }

  /**
   * 记录错误级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  error(message: string, data?: LogData): void {
    this.log('ERROR', message, data);
  }

  /**
   * 记录调试级别日志
   * @param message 日志消息
   * @param data 附加数据对象
   */
  debug(message: string, data?: LogData): void {
    // 在生产环境中可以禁用调试日志
    if (process.env.NODE_ENV !== 'production') {
      this.log('DEBUG', message, data);
    }
  }

  /**
   * 内部日志记录方法
   * @param level 日志级别
   * @param message 日志消息
   * @param data 附加数据对象
   */
  private log(level: string, message: string, data?: LogData): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      ...data
    };

    // 根据日志级别使用不同的控制台方法
    switch (level) {
      case 'ERROR':
        console.error(JSON.stringify(logEntry));
        break;
      case 'WARN':
        console.warn(JSON.stringify(logEntry));
        break;
      case 'DEBUG':
        console.debug(JSON.stringify(logEntry));
        break;
      case 'INFO':
      default:
        console.log(JSON.stringify(logEntry));
        break;
    }

    // 这里可以添加将日志写入文件或发送到日志服务的代码
    // 例如使用winston或其他日志库
  }
}

// 导出单例实例
export const logger = new Logger();
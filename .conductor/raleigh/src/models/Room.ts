import { DataTypes, Model } from "sequelize";
import { sequelize } from "../config/db";
// import Session from './Session';

export interface RoomAttributes {
  id?: string;
  sessionId: number;
  roundIndex: number; // 每个回合有不同的房间
  isFull: boolean;

  manualWinnerWalletId?: number;

  seqno?: number; 
  result_position?: number;
  lotteryProcessed?: boolean;
}

export class Room extends Model<RoomAttributes> implements RoomAttributes {
  public id!: string;
  public sessionId!: number;
  public roundIndex!: number;
  public isFull!: boolean;
  public lotteryProcessed?: boolean;
  public seqno?: number;
  public result_position?: number;
  public manualWinnerWalletId?: number;
  
}

Room.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    roundIndex: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    sessionId: { type: DataTypes.INTEGER, allowNull: false },
    isFull: { type: DataTypes.BOOLEAN, defaultValue: false },
    seqno: { type: DataTypes.INTEGER, allowNull: true }, // 新增字段
    result_position: { type: DataTypes.INTEGER, allowNull: true }, // 新增字段
    manualWinnerWalletId: { type: DataTypes.INTEGER, allowNull: true },
    lotteryProcessed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    tableName: "rooms",
    sequelize,
  }
);

// 建立关联关系：一个 Session 可对应多个 Room
// Session.hasMany(Room, { foreignKey: 'sessionId' });
// Room.belongsTo(Session, { foreignKey: 'sessionId' });

export default Room;

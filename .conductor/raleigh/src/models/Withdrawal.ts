// src/models/Withdrawal.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface WithdrawalAttributes {
  id: number;
  userId: number;
  walletId: number;
  currency: string; // "usd", "moof", "ton"等
  amount: number; // 提现金额
  fee: number; // 手续费
  status: string; // "pending", "approved", "rejected", "completed"等
  withdrawalAddress: string; // 提现地址
  remark: string; // 备注
  processedAt?: Date; // 处理时间
  processedBy?: string; // 处理人
  txHash?: string; // 区块链交易哈希
  blockNumber?: number; // 区块号
  confirmations?: number; // 确认数
  txStatus?: string; // 交易状态：pending, confirmed, failed
  txTimestamp?: Date; // 交易时间戳
  networkFee?: number; // 网络手续费
  createdAt?: Date;
  updatedAt?: Date;
}

type WithdrawalCreationAttributes = Optional<WithdrawalAttributes, "id" | "processedAt" | "processedBy" | "txHash" | "blockNumber" | "confirmations" | "txStatus" | "txTimestamp" | "networkFee">;

export class Withdrawal
  extends Model<WithdrawalAttributes, WithdrawalCreationAttributes>
  implements WithdrawalAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public currency!: string;
  public amount!: number;
  public fee!: number;
  public status!: string;
  public withdrawalAddress!: string;
  public remark!: string;
  public processedAt?: Date;
  public processedBy?: string;
  public txHash?: string;
  public blockNumber?: number;
  public confirmations?: number;
  public txStatus?: string;
  public txTimestamp?: Date;
  public networkFee?: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Withdrawal.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    fee: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    withdrawalAddress: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    remark: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    processedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    processedBy: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    txHash: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    blockNumber: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    confirmations: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    txStatus: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    txTimestamp: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    networkFee: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: "withdrawals",
    timestamps: true,
  }
);
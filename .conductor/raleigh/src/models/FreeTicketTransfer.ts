// src/models/FreeTicketTransfer.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface FreeTicketTransferAttributes {
  id: number;
  fromUserId: number; // 发送方用户ID
  fromWalletId: number; // 发送方钱包ID
  toUserId: number; // 接收方用户ID
  toWalletId: number; // 接收方钱包ID
  amount: number; // 转账数量
  createdAt?: Date;
  updatedAt?: Date;
}

type FreeTicketTransferCreationAttributes = Optional<FreeTicketTransferAttributes, "id">;

export class FreeTicketTransfer
  extends Model<FreeTicketTransferAttributes, FreeTicketTransferCreationAttributes>
  implements FreeTicketTransferAttributes
{
  public id!: number;
  public fromUserId!: number;
  public fromWalletId!: number;
  public toUserId!: number;
  public toWalletId!: number;
  public amount!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

FreeTicketTransfer.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    fromUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    fromWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    toUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    toWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    amount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
  },
  {
    tableName: "free_ticket_transfers",
    sequelize,
    timestamps: true,
  }
);
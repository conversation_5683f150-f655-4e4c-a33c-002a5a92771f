import { DataTypes, Model } from 'sequelize';
import { sequelize } from "../config/db";

export type SessionStatus = 'pending' | 'active' | 'completed';

export interface SessionAttributes {
  id?: number;
  session_category: string;  // 例如 "04:00:00" 或 "12:00:00"
  session_dt: Date;          // 对应 JSON 中的 session_dt
  reservedCount: number;     // 当前场次预定总人数
  status: SessionStatus;     // 场次状态
}

export class Session extends Model<SessionAttributes> implements SessionAttributes {
  public id!: number;
  public session_category!: string;
  public session_dt!: Date;
  public reservedCount!: number;
  public status!: SessionStatus;
}

Session.init(
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    session_category: { type: DataTypes.STRING, allowNull: false },
    session_dt: { type: DataTypes.DATE, allowNull: false },
    reservedCount: { type: DataTypes.INTEGER, defaultValue: 0 },
    status: { type: DataTypes.ENUM('pending', 'active', 'completed'), defaultValue: 'pending' },
  },
  {
    tableName: 'sessions',
    sequelize,
  }
);

export default Session;
import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';

interface FarmConfigVersionAttributes {
  id: number;
  version: string;
  name: string;
  description?: string;
  isActive: boolean;
  configCount: number;
  createdBy?: string;
  activatedAt?: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

interface FarmConfigVersionCreationAttributes extends Optional<FarmConfigVersionAttributes, 'id' | 'createdAt' | 'updatedAt' | 'description' | 'createdBy' | 'activatedAt'> {}

class FarmConfigVersion extends Model<FarmConfigVersionAttributes, FarmConfigVersionCreationAttributes> implements FarmConfigVersionAttributes {
  public id!: number;
  public version!: string;
  public name!: string;
  public description?: string;
  public isActive!: boolean;
  public configCount!: number;
  public createdBy?: string;
  public activatedAt?: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  /**
   * 创建新版本记录
   */
  public static async createVersion(
    version: string,
    name: string,
    configCount: number,
    description?: string,
    createdBy?: string
  ): Promise<FarmConfigVersion> {
    return await FarmConfigVersion.create({
      version,
      name,
      description,
      isActive: false,
      configCount,
      createdBy
    });
  }

  /**
   * 获取当前激活的版本
   */
  public static async getActiveVersion(): Promise<FarmConfigVersion | null> {
    return await FarmConfigVersion.findOne({
      where: { isActive: true }
    });
  }

  /**
   * 获取所有版本列表
   */
  public static async getAllVersions(): Promise<FarmConfigVersion[]> {
    return await FarmConfigVersion.findAll({
      order: [['createdAt', 'DESC']]
    });
  }

  /**
   * 激活指定版本
   */
  public static async activateVersion(version: string): Promise<boolean> {
    const transaction = await sequelize.transaction();
    
    try {
      // 先将所有版本设为非激活状态
      await FarmConfigVersion.update(
        {
          isActive: false,
          activatedAt: undefined
        },
        {
          where: {},
          transaction
        }
      );

      // 激活指定版本
      const [affectedCount] = await FarmConfigVersion.update(
        { 
          isActive: true,
          activatedAt: new Date()
        },
        { 
          where: { version },
          transaction 
        }
      );

      await transaction.commit();
      return affectedCount > 0;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 删除版本记录
   */
  public static async deleteVersion(version: string): Promise<boolean> {
    // 检查是否为激活版本
    const activeVersion = await FarmConfigVersion.findOne({
      where: { version, isActive: true }
    });

    if (activeVersion) {
      throw new Error('无法删除当前激活的配置版本');
    }

    const deletedCount = await FarmConfigVersion.destroy({
      where: { version }
    });

    return deletedCount > 0;
  }

  /**
   * 获取版本统计信息
   */
  public static async getVersionStats(): Promise<{
    totalVersions: number;
    activeVersion: string | null;
    latestVersion: string | null;
  }> {
    const totalVersions = await FarmConfigVersion.count();
    
    const activeVersion = await FarmConfigVersion.findOne({
      where: { isActive: true },
      attributes: ['version']
    });

    const latestVersion = await FarmConfigVersion.findOne({
      order: [['createdAt', 'DESC']],
      attributes: ['version']
    });

    return {
      totalVersions,
      activeVersion: activeVersion?.version || null,
      latestVersion: latestVersion?.version || null
    };
  }

  /**
   * 检查版本是否存在
   */
  public static async versionExists(version: string): Promise<boolean> {
    const count = await FarmConfigVersion.count({
      where: { version }
    });
    return count > 0;
  }

  /**
   * 获取版本详情
   */
  public static async getVersionDetail(version: string): Promise<FarmConfigVersion | null> {
    return await FarmConfigVersion.findOne({
      where: { version }
    });
  }
}

FarmConfigVersion.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    version: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      comment: '版本号',
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '版本名称',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '版本描述',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否激活',
    },
    configCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
      comment: '配置条数',
    },
    createdBy: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '创建者',
    },
    activatedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '激活时间',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'farm_config_versions',
    timestamps: true,
    indexes: [
      {
        fields: ['isActive']
      },
      {
        fields: ['createdAt']
      },
      {
        unique: true,
        fields: ['version']
      }
    ]
  }
);

export { FarmConfigVersion };

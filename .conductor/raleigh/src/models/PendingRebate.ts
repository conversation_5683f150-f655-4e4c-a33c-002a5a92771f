// src/models/PendingRebate.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface PendingRebateAttributes {
  id: number;
  userId: number;
  walletId: number;
  amount: number;
  level: number;
  sourceUserId: number; // 返利来源用户
}

type PendingRebateCreationAttributes = Optional<PendingRebateAttributes, "id">;

export class PendingRebate
  extends Model<PendingRebateAttributes, PendingRebateCreationAttributes>
  implements PendingRebateAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public amount!: number;
  public level!: number;
  public sourceUserId!: number;
}

PendingRebate.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(18, 6),
      allowNull: false,
    },
    level: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    sourceUserId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    }
  },
  {
    tableName: "pending_rebates",
    sequelize,
    timestamps: true,
  }
);
// src/models/Tasks.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface TasksAttributes {
  id: number;
  name: string; // 任务名称 (例如 "每日签到" / "加入Telegram频道")
  type: string; // 任务类型 (例如 "DAILY", "ONE_TIME")
  repeatInterval: string; // "day", "once", "week"等, 用来判断如何重复
  createdAt?: Date;
  updatedAt?: Date;
}

// 创建时可选的字段
type TasksCreationAttributes = Optional<TasksAttributes, "id">;

export class Tasks
  extends Model<TasksAttributes, TasksCreationAttributes>
  implements TasksAttributes
{
  public id!: number;
  public name!: string;
  public type!: string;
  public repeatInterval!: string;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Tasks.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    repeatInterval: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: "once", // 默认只能完成一次
    },
  },
  {
    tableName: "tasks",
    sequelize,
    timestamps: true,
  }
);

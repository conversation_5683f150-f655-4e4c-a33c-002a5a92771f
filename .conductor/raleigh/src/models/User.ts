// src/models/User.ts
import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";

interface UserAttributes {
  id: number;
  telegramId: string;
  hasFollowedChannel: boolean;
  username?: string;
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  authDate: number;
  hash: string;
  telegram_premium?: boolean;
  referralCount?: number;
  referrerId?: number;
  refWalletAddress?: string;
  firstWalletId?: number;
  email?: string; // 新增邮箱字段
 
}

type UserCreationAttributes = Optional<UserAttributes, "id">;

export class User
  extends Model<UserAttributes, UserCreationAttributes>
  implements UserAttributes
{
  public id!: number;
  public telegramId!: string;
  public hasFollowedChannel!: boolean;
  public username?: string;
  public firstName?: string;
  public lastName?: string;
  public photoUrl?: string;
  public authDate!: number;
  public hash!: string;
  public telegram_premium?: boolean;
  public referralCount?: number;
  public referrerId?: number;
  public firstWalletId?: number;
  public refWalletAddress?: string;
  public email?: string; // 新增邮箱字段
  
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

User.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    telegramId: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    hasFollowedChannel: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    referralCount: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
    referrerId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },
    firstWalletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
    },

    username: DataTypes.STRING,

    refWalletAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },

    firstName: DataTypes.STRING,
    lastName: DataTypes.STRING,
    photoUrl: DataTypes.STRING,
    authDate: DataTypes.INTEGER,
    hash: DataTypes.STRING,
    telegram_premium: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true, // 添加邮箱格式验证
      },
    },
    
  },
  {
    tableName: "users",
    sequelize,
    timestamps: true,
  }
);

import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { User } from "./User";
import { UserWallet } from "./UserWallet";

interface UserChestAccelerationAttributes {
  id: number;
  userId: number;
  walletId: number;
  accelerationDate: Date; // 加速使用的日期
  totalAccelerationSeconds: number; // 当天已使用的总加速秒数
  createdAt?: Date;
  updatedAt?: Date;
}

type UserChestAccelerationCreationAttributes = Optional<UserChestAccelerationAttributes, "id">;

export class UserChestAcceleration
  extends Model<UserChestAccelerationAttributes, UserChestAccelerationCreationAttributes>
  implements UserChestAccelerationAttributes
{
  public id!: number;
  public userId!: number;
  public walletId!: number;
  public accelerationDate!: Date;
  public totalAccelerationSeconds!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

UserChestAcceleration.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    accelerationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    totalAccelerationSeconds: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      defaultValue: 0,
    },
  },
  {
    tableName: "user_chest_accelerations",
    sequelize,
    timestamps: true,
  }
);

// 建立与User和UserWallet的关联
UserChestAcceleration.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

UserChestAcceleration.belongsTo(UserWallet, {
  foreignKey: 'walletId',
  as: 'userWallet'
});
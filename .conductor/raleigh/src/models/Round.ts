import { DataTypes, Model } from 'sequelize';
import { sequelize } from "../config/db";
import Session from './Session';

export type RoundStatus = 'pending' | 'revealed' | 'skipped';

export interface RoundAttributes {
  id?: number;
  sessionId: number;
  roundIndex: number;   // 例如 1～6
  result: string;       // 可存储 "Win"/"Lose" 或其它结果
  status: RoundStatus;
  result_time: Date;    // 开奖时间
  round_id: string;     // 回合标识，如 "ROUND 1 - 1"
  room_status: string;  // 房间状态，如 "open" 或 "done"
  room_count: number;   // 房间统计人数（例如 32 或 -1）
}

export class Round extends Model<RoundAttributes> implements RoundAttributes {
  public id!: number;
  public sessionId!: number;
  public roundIndex!: number;
  public result!: string;
  public status!: RoundStatus;
  public result_time!: Date;
  public round_id!: string;
  public room_status!: string;
  public room_count!: number;
}

Round.init(
  {
    id: { type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
    sessionId: { type: DataTypes.INTEGER, allowNull: false },
    roundIndex: { type: DataTypes.INTEGER, allowNull: false },
    result: { type: DataTypes.STRING, allowNull: true },
    status: { type: DataTypes.ENUM('pending', 'revealed', 'skipped'), defaultValue: 'pending' },
    result_time: { type: DataTypes.DATE, allowNull: true },
    round_id: { type: DataTypes.STRING, allowNull: true },
    room_status: { type: DataTypes.STRING, allowNull: true },
    room_count: { type: DataTypes.INTEGER, defaultValue: 0 },
  },
  {
    tableName: 'rounds',
    sequelize,
  }
);

// 建立关联关系：一个 Session 可对应多个 Round
// Session.hasMany(Round, { foreignKey: 'sessionId' });
// Round.belongsTo(Session, { foreignKey: 'sessionId' });

export default Round;
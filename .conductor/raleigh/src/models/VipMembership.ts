import { Model, DataTypes, Optional } from "sequelize";
import { sequelize } from "../config/db";
import { UserWallet } from './UserWallet';

interface VipMembershipAttributes {
  id: number;
  walletId: number;
  startTime: Date;
  endTime: Date;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

interface VipMembershipCreationAttributes extends Optional<VipMembershipAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class VipMembership extends Model<VipMembershipAttributes, VipMembershipCreationAttributes> implements VipMembershipAttributes {
  public id!: number;
  public walletId!: number;
  public startTime!: Date;
  public endTime!: Date;
  public isActive!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 检查VIP是否仍然有效
  public checkAndUpdateStatus(): boolean {
    const now = new Date();
    const isCurrentlyActive = now >= this.startTime && now <= this.endTime;
    
    if (this.isActive !== isCurrentlyActive) {
      this.isActive = isCurrentlyActive;
      this.save();
    }
    
    return isCurrentlyActive;
  }
}

VipMembership.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      unique: true, // 每个用户只能有一个VIP记录
      references: {
        model: UserWallet,
        key: 'id',
      },
    },
    startTime: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    endTime: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  {
    sequelize,
    modelName: "VipMembership",
    tableName: "vip_memberships",
  }
);


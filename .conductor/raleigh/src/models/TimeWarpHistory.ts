import { Model, DataTypes, Optional } from 'sequelize';
import { sequelize } from '../config/db';
import { UserWallet } from './UserWallet';
import { IapProduct } from './IapProduct';
import { IapPurchase } from './IapPurchase';

interface TimeWarpHistoryAttributes {
  id: number;
  walletId: number;
  productId?: number;
  purchaseId?: number;
  hours: number;
  gemsEarned: number;
  milkProduced: number;
  milkProcessed: number;
  farmProductionPerSecond: number;
  deliveryProcessingPerSecond: number;
  hasVip: boolean;
  hasSpeedBoost: boolean;
  speedBoostMultiplier: number;
  usedAt: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

interface TimeWarpHistoryCreationAttributes extends Optional<TimeWarpHistoryAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

export class TimeWarpHistory extends Model<TimeWarpHistoryAttributes, TimeWarpHistoryCreationAttributes> implements TimeWarpHistoryAttributes {
  public id!: number;
  public walletId!: number;
  public productId?: number;
  public purchaseId?: number;
  public hours!: number;
  public gemsEarned!: number;
  public milkProduced!: number;
  public milkProcessed!: number;
  public farmProductionPerSecond!: number;
  public deliveryProcessingPerSecond!: number;
  public hasVip!: boolean;
  public hasSpeedBoost!: boolean;
  public speedBoostMultiplier!: number;
  public usedAt!: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

TimeWarpHistory.init(
  {
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    walletId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: UserWallet,
        key: 'id',
      },
    },
    productId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: IapProduct,
        key: 'id',
      },
    },
    purchaseId: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: IapPurchase,
        key: 'id',
      },
    },
    hours: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
    },
    gemsEarned: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 0,
    },
    milkProduced: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 0,
    },
    milkProcessed: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 0,
    },
    farmProductionPerSecond: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 0,
    },
    deliveryProcessingPerSecond: {
      type: DataTypes.DECIMAL(15, 3),
      allowNull: false,
      defaultValue: 0,
    },
    hasVip: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    hasSpeedBoost: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    speedBoostMultiplier: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 1.00,
    },
    usedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    tableName: 'time_warp_histories',
    modelName: 'TimeWarpHistory',
    timestamps: true,
  }
);

// 定义关联关系
TimeWarpHistory.belongsTo(UserWallet, { foreignKey: 'walletId', as: 'UserWallet' });
TimeWarpHistory.belongsTo(IapProduct, { foreignKey: 'productId', as: 'IapProduct' });
TimeWarpHistory.belongsTo(IapPurchase, { foreignKey: 'purchaseId', as: 'IapPurchase' });

export default TimeWarpHistory;

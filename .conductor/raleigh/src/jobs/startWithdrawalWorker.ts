// src/jobs/startWithdrawalWorker.ts
import { Worker } from "bullmq";
import { redis } from "../config/redis";
import dotenv from "dotenv";
import * as path from 'path';

dotenv.config();

// 导入工作器文件的路径
const workerPath = path.join(__dirname, './withdrawalWorker.ts');
console.log(`TON提现工作器路径: ${workerPath}`);

/**
 * 独立启动TON提现工作器
 * 该文件用于单独启动TON提现处理工作器进程
 */
async function main() {
  console.log('TON提现工作器启动中...');
  
  try {
    // 动态导入工作器模块
    await import('./withdrawalWorker');
    console.log('TON提现工作器已成功启动');
    
    // 阻止进程退出
    process.stdin.resume();
  } catch (error) {
    console.error('启动TON提现工作器失败:', error);
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error('运行TON提现工作器主函数失败:', error);
  process.exit(1);
});

// 处理退出信号
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，TON提现工作器准备退出');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，TON提现工作器准备退出');
  process.exit(0);
});
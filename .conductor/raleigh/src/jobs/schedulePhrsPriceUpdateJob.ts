// src/jobs/schedulePhrsPriceUpdateJob.ts
import { phrsPriceUpdateQueue } from "./bullmqConfig";
import dotenv from "dotenv";

dotenv.config();

/**
 * 调度PHRS价格更新任务
 * 
 * 该函数负责创建一个定时任务，定期更新IapProduct表中所有产品的pricePhrs字段
 * 基于固定汇率：1 PHRS = 0.0001 USD
 * 默认每小时执行一次，可通过环境变量配置
 */
export async function schedulePhrsPriceUpdateJob() {
  console.log('[SchedulePhrsPriceUpdate] 开始设置PHRS价格更新任务定时调度...');
  
  // 默认每小时执行一次，可通过环境变量配置
  // CRON表达式格式：秒 分 时 日 月 星期
  // "0 0 * * * *" 表示每小时执行一次
  // "0 */10 * * * *" 表示每10分钟执行一次（用于测试）
  const cronPattern = process.env.PHRS_PRICE_UPDATE_SCHEDULE || "0 0 * * * *";
  const schedulerId = `scheduler-phrs-price-update`;

  console.log(`[SchedulePhrsPriceUpdate] 正在设置任务: ${schedulerId}, CRON: ${cronPattern}`);
  
  try {
    await phrsPriceUpdateQueue.upsertJobScheduler(
      schedulerId,
      {
        pattern: cronPattern,
        immediately: true, // 启动时立即执行一次
      },
      {
        name: `phrs-price-update-job`,
        data: {
          timestamp: new Date().toISOString(),
          type: 'phrs-price-update',
          description: 'Update PHRS prices for all IapProducts based on fixed exchange rate',
          exchangeRate: parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001')
        },
        opts: {
          backoff: {
            type: 'exponential',
            delay: 5000, // 5秒后重试
          },
          attempts: 3, // 最多重试3次
          removeOnFail: 100, // 保留最近100个失败的任务
          removeOnComplete: 100, // 保留最近100个完成的任务
        },
      }
    );
    console.log(`[SchedulePhrsPriceUpdate] PHRS价格更新任务设置成功: ${schedulerId}`);
    console.log(`[SchedulePhrsPriceUpdate] 任务将按照以下计划执行: ${cronPattern}`);
    console.log(`[SchedulePhrsPriceUpdate] 使用汇率: 1 PHRS = ${process.env.PHRS_TO_USD_RATE || '0.0001'} USD`);
  } catch (error) {
    console.error(`[SchedulePhrsPriceUpdate] 设置PHRS价格更新任务失败: ${schedulerId}`, error);
    throw error;
  }
}

/**
 * 移除PHRS价格更新任务调度
 */
export async function removePhrsPriceUpdateJob() {
  console.log('[RemovePhrsPriceUpdate] 开始移除PHRS价格更新任务调度...');
  
  const schedulerId = `scheduler-phrs-price-update`;
  
  try {
    const result = await phrsPriceUpdateQueue.removeJobScheduler(schedulerId);
    console.log(`[RemovePhrsPriceUpdate] 移除任务结果: ${result ? "成功" : "任务不存在"}`);
    return result;
  } catch (error) {
    console.error(`[RemovePhrsPriceUpdate] 移除PHRS价格更新任务失败: ${schedulerId}`, error);
    throw error;
  }
}

/**
 * 手动触发PHRS价格更新任务
 * 用于测试或立即更新价格
 */
export async function triggerPhrsPriceUpdateJob() {
  console.log('[TriggerPhrsPriceUpdate] 手动触发PHRS价格更新任务...');
  
  try {
    const job = await phrsPriceUpdateQueue.add(
      'phrs-price-update-job',
      {
        timestamp: new Date().toISOString(),
        type: 'manual-trigger',
        description: 'Manual trigger for PHRS price update',
        exchangeRate: parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001')
      },
      {
        removeOnComplete: 10,
        removeOnFail: 10,
      }
    );
    
    console.log(`[TriggerPhrsPriceUpdate] 手动任务已添加到队列: ${job.id}`);
    return job;
  } catch (error) {
    console.error('[TriggerPhrsPriceUpdate] 手动触发PHRS价格更新任务失败:', error);
    throw error;
  }
}

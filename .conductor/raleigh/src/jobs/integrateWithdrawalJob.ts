// src/jobs/integrateWithdrawalJob.ts
import { Queue } from "bullmq";
import { scheduleTonWithdrawalJob } from "./scheduleTonWithdrawalJob";
import { tonWithdrawalQueue } from "./tonWithdrawalQueue";
import dotenv from "dotenv";
import path from "path";
import { ChildProcess } from "child_process";

dotenv.config();

/**
 * 集成TON提现任务和工作器到主应用程序
 * 该函数用于在主应用程序启动时调用
 * 
 * @param startWorkerProcess 启动工作进程的函数
 * @param setupWorkerEvents 设置工作进程事件的函数
 * @param workers 工作进程集合
 * @param queues 队列集合
 */
export async function integrateWithdrawalJob(
  startWorkerProcess: (workerPath: string, execArgv?: string[]) => ChildProcess,
  setupWorkerEvents: (process: ChildProcess, workerPath: string, workerName: string) => ChildProcess,
  workers: { [key: string]: ChildProcess },
  queues: { [key: string]: Queue }
) {
  console.log('[集成TON提现任务] 开始集成TON提现任务和工作器...');
  
  try {
    // 1. 调度TON提现任务
    await scheduleTonWithdrawalJob();
    console.log('[集成TON提现任务] TON提现任务调度完成');
    
    // 2. 添加TON提现队列到队列集合
    queues['tonWithdrawal'] = tonWithdrawalQueue;
    console.log('[集成TON提现任务] TON提现队列已添加到队列集合');
    
    // 3. 启动TON提现工作器
    const withdrawalWorkerPath = process.env.NODE_ENV === 'production'
      ? path.join(__dirname, 'startWithdrawalWorker.js')
      : path.join(__dirname, 'startWithdrawalWorker.ts');
    
    console.log(`[集成TON提现任务] 正在启动TON提现工作器，路径: ${withdrawalWorkerPath}`);
    
    const execArgv = process.env.NODE_ENV === 'production' ? [] : ['-r', 'ts-node/register'];
    const withdrawalWorker = startWorkerProcess(withdrawalWorkerPath, execArgv);
    workers['tonWithdrawal'] = setupWorkerEvents(withdrawalWorker, withdrawalWorkerPath, 'TON提现工作器');
    
    console.log('[集成TON提现任务] TON提现工作器已启动并集成到系统');
    
    return {
      worker: workers['tonWithdrawal'],
      queue: queues['tonWithdrawal']
    };
  } catch (error) {
    console.error('[集成TON提现任务] 集成TON提现任务和工作器失败:', error);
    throw error;
  }
}
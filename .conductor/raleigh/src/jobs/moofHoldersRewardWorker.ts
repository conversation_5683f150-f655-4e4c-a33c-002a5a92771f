import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { sequelize } from "../config/db";
import { UserWallet, PrizePool, RewardClaim } from "../models";
import dayjs from "dayjs";
import { Op } from "sequelize";
import fs from 'fs';
import path from 'path';
import BigNumber from 'bignumber.js';

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[MoofHoldersRewardWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[MoofHoldersRewardWorker] ${message}`, ...args);
  },
};

console.log('moofHoldersRewardWorker.ts loaded');


async function backupRewardClaims() {
  const currentDate = dayjs().format('YYYY-MM-DD');
  const backupDir = path.join(__dirname, '../../backups/reward_claims');
  
  // 确保备份目录存在
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // 获取所有奖励记录
  const claims = await RewardClaim.findAll({
    where: {
      subPool: 'moof_holders_weekly'
    }
  });

  // 写入备份文件
  const backupPath = path.join(backupDir, `moof_holders_${currentDate}.json`);
  await fs.promises.writeFile(
    backupPath,
    JSON.stringify(claims, null, 2)
  );

  logger.info(`奖励记录已备份到: ${backupPath}`);
}

async function distributeMoofHoldersReward() {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 获取 MOOF 持有者奖池
    const moofPool = await PrizePool.findOne({
      where: { type: 'moof_holders' },
      transaction,
      lock: true
    });

    if (!moofPool || moofPool.amount <= 0) {
      logger.info('奖池为空或金额为0，跳过本次分发');
      await transaction.commit();
      return;
    }

    // 2. 计算本次要分发的奖励总额 (70%)
    const totalRewardAmount = moofPool.amount * 0.7;

    // 3. 获取所有持有 MOOF 的用户
    const moofHolders = await UserWallet.findAll({
      where: {
        moof: {
          [Op.gt]: 0
        }
      },
      transaction
    });

    if (moofHolders.length === 0) {
      logger.info('没有 MOOF 持有者，跳过本次分发');
      await transaction.commit();
      return;
    }

    // 4. 计算所有持有者的 MOOF 总量
    const totalMoof = moofHolders.reduce((sum, holder) => new BigNumber(sum).plus(holder.moof || 0).toNumber(), 0);

    // 5. 按持有比例更新或创建奖励记录
    for (const holder of moofHolders) {
      if (!holder.moof) continue;
      
      // 计算该持有者应得的奖励
      const holderShare = holder.moof / totalMoof;
      //保留5位小数
      let rewardAmount = totalRewardAmount * holderShare;
      // 保留5位小数
      rewardAmount = parseFloat(rewardAmount.toFixed(5));

      // 查找是否存在未领取的奖励记录
      const existingClaim = await RewardClaim.findOne({
        where: {
          userId: holder.userId,
          walletId: holder.id,
          prizePoolId: moofPool.id,
          subPool: 'moof_holders_weekly',
          claimed: false
        },
        transaction
      });

      if (existingClaim) {
        // 更新现有记录
        await existingClaim.update({
          amount: new BigNumber(existingClaim.amount).plus(rewardAmount).toNumber()
        }, { transaction });
        
        logger.info(`更新用户 ${holder.userId} 的奖励记录，新增金额: ${rewardAmount} USD`);
      } else {
        // 创建新记录
        await RewardClaim.create({
          userId: holder.userId,
          walletId: holder.id,
          prizePoolId: moofPool.id,
          subPool: 'moof_holders_weekly',
          amount: rewardAmount,
          claimed: false
        }, { transaction });

        logger.info(`为用户 ${holder.userId} 创建新的奖励记录，金额: ${rewardAmount} USD`);
      }
    }

    // 6. 更新奖池余额
    await moofPool.decrement('amount', {
      by: totalRewardAmount,
      transaction
    });

    // 7. 备份本周奖励记录
    await backupRewardClaims();

    await transaction.commit();
    logger.info(`本周 MOOF 持有者奖励记录已更新，总金额: ${totalRewardAmount} USD`);

  } catch (error) {
    await transaction.rollback();
    logger.error('奖励记录更新失败:', error);
    throw error;
  }
}

// Worker 配置
// 使用不同的变量名以避免重复声明
const moofHoldersRewardWorkerInstance = new Worker(
  "moof-holders-reward-job",
  async (job: Job) => {
    logger.info(`开始处理任务 ID: ${job.id}`);
    await distributeMoofHoldersReward();
    logger.info(`任务 ID: ${job.id} 已完成`);
  },
  {
    connection: redis,
    concurrency: 1,
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 },
  }
);

// 错误处理
moofHoldersRewardWorkerInstance.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

//error
moofHoldersRewardWorkerInstance.on("error", (error: Error) => {
  logger.error('Worker 错误:', error);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('MOOF持有者奖励工作进程收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await moofHoldersRewardWorkerInstance.close();
      
      logger.info('MOOF持有者奖励工作进程资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('MOOF持有者奖励工作进程清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on("SIGTERM", async () => {
  logger.info("收到 SIGTERM 信号，关闭 Worker...");
  try {
    await moofHoldersRewardWorkerInstance.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 监听 SIGINT 信号
process.on("SIGINT", async () => {
  logger.info("收到 SIGINT 信号，关闭 Worker...");
  try {
    await moofHoldersRewardWorkerInstance.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 处理未捕获的异常
process.on("uncaughtException", async (error) => {
  logger.error("未捕获的异常:", error);
  try {
    await moofHoldersRewardWorkerInstance.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 处理未处理的 Promise 拒绝
process.on("unhandledRejection", async (reason) => {
  logger.error("未处理的 Promise 拒绝:", reason);
  try {
    await moofHoldersRewardWorkerInstance.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化 MOOF 持有者奖励处理器...');
  // 这里不需要做任何事情，因为 Worker 已经在模块加载时创建
  return moofHoldersRewardWorkerInstance;
}

// 导出 worker 实例
export default moofHoldersRewardWorkerInstance;


// src/jobs/kaiapriceUpdateWorker.ts
import { Worker, Job } from "bullmq";
import { redis } from "../config/redis";
import { IapProduct } from "../models";
import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[KaiaPriceUpdateWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[KaiaPriceUpdateWorker] ${message}`, ...args);
  },
};

console.log('kaiapriceUpdateWorker.ts loaded');

// Kaiascan API配置
const KAIASCAN_API_KEY = process.env.KAIASCAN_API_KEY || '625b664e-c431-442c-9648-b4a9bb5d3a3c';
const KAIASCAN_API_URL = `https://mainnet-oapi.kaiascan.io/api?module=stats&action=coinprice&apikey=${KAIASCAN_API_KEY}`;

// 获取Kaia价格的函数
async function getKaiaPrice(): Promise<number> {
  try {
    logger.info('正在从Kaiascan API获取Kaia价格...');
    const response = await axios.get(KAIASCAN_API_URL, {
      timeout: 10000, // 10秒超时
    });
    
    if (response.data && response.data.status === '1' && response.data.result) {
      const coinUsd = parseFloat(response.data.result.coin_usd);
      if (isNaN(coinUsd) || coinUsd <= 0) {
        throw new Error(`无效的价格数据: ${response.data.result.coin_usd}`);
      }
      logger.info(`获取到Kaia价格: 1 KAIA = ${coinUsd} USD`);
      return coinUsd;
    } else {
      throw new Error(`API返回错误状态: ${response.data?.status || 'unknown'}`);
    }
  } catch (error) {
    logger.error('获取Kaia价格失败:', error);
    throw error;
  }
}

// 更新所有IapProduct的priceKaia字段
async function updateKaiaPrices(kaiaUsdPrice: number): Promise<void> {
  try {
    logger.info('开始更新所有IapProduct的priceKaia字段...');
    
    // 获取所有有priceUsd的产品
    const products = await IapProduct.findAll({
      where: {
        priceUsd: {
          [require('sequelize').Op.gt]: 0
        }
      }
    });
    
    logger.info(`找到 ${products.length} 个需要更新的产品`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      try {
        // 计算priceKaia = priceUsd / coin_usd
        const priceKaia = parseFloat((product.priceUsd / kaiaUsdPrice).toFixed(4));
        
        await product.update({
          priceKaia: priceKaia
        });
        
        logger.info(`产品 ${product.name} (ID: ${product.id}) 价格已更新: ${product.priceUsd} USD = ${priceKaia} KAIA`);
        updatedCount++;
      } catch (error) {
        logger.error(`更新产品 ${product.id} 失败:`, error);
      }
    }
    
    logger.info(`价格更新完成，共更新了 ${updatedCount} 个产品`);
  } catch (error) {
    logger.error('更新Kaia价格失败:', error);
    throw error;
  }
}

// Worker 配置
export const kaiaPriceUpdateWorker = new Worker(
  "kaia-price-update-job",
  async (job: Job) => {
    logger.info(`开始处理Kaia价格更新任务 ID: ${job.id}`);
    
    try {
      // 获取当前Kaia价格
      const kaiaUsdPrice = await getKaiaPrice();
      
      // 更新所有产品的priceKaia
      await updateKaiaPrices(kaiaUsdPrice);
      
      logger.info(`Kaia价格更新任务 ID: ${job.id} 已完成`);
    } catch (error) {
      logger.error(`Kaia价格更新任务 ID: ${job.id} 失败:`, error);
      throw error; // 重新抛出错误以便BullMQ处理重试
    }
  },
  {
    connection: redis,
    concurrency: 1, // 同时只处理一个任务
    removeOnComplete: { count: 100 }, // 保留最近100个完成的任务
    removeOnFail: { count: 100 }, // 保留最近100个失败的任务
  }
);

// 错误处理
kaiaPriceUpdateWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`Kaia价格更新任务 ${job?.id} 失败:`, error);
});

kaiaPriceUpdateWorker.on("error", (error: Error) => {
  logger.error("KaiaPriceUpdateWorker 出错:", error);
});

kaiaPriceUpdateWorker.on("completed", (job: Job) => {
  logger.info(`Kaia价格更新任务 ${job.id} 成功完成`);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any;
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('收到关闭信号，正在清理资源...');
    try {
      await kaiaPriceUpdateWorker.close();
      logger.info('KaiaPriceUpdateWorker 已关闭');
      process.exit(0);
    } catch (error) {
      logger.error('关闭 KaiaPriceUpdateWorker 时出错:', error);
      process.exit(1);
    }
  }
});

// 处理进程退出信号
process.on('SIGTERM', async () => {
  logger.info('收到 SIGTERM 信号，正在关闭 KaiaPriceUpdateWorker...');
  try {
    await kaiaPriceUpdateWorker.close();
    process.exit(0);
  } catch (error) {
    logger.error('关闭 KaiaPriceUpdateWorker 时出错:', error);
    process.exit(1);
  }
});

process.on('SIGINT', async () => {
  logger.info('收到 SIGINT 信号，正在关闭 KaiaPriceUpdateWorker...');
  try {
    await kaiaPriceUpdateWorker.close();
    process.exit(0);
  } catch (error) {
    logger.error('关闭 KaiaPriceUpdateWorker 时出错:', error);
    process.exit(1);
  }
});


// 添加初始化函数，与其他worker保持一致的接口
export async function initializeWorker(queue: any) {
  console.log('初始化KAIA价格更新处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return kaiaPriceUpdateWorker;
}
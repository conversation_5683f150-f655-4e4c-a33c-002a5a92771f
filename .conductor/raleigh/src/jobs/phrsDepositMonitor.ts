// src/jobs/phrsDepositMonitor.ts
import cron from 'node-cron';
import { phrsDepositService } from '../services/phrsDepositService';
import { PhrsDeposit } from '../models';
import { Op } from 'sequelize';

/**
 * PHRS充值监控任务
 * 定期检查充值状态和处理失败的充值
 */
export class PhrsDepositMonitor {
  private isRunning: boolean = false;
  private cronJob: cron.ScheduledTask | null = null;

  /**
   * 启动监控任务
   */
  public start(): void {
    if (this.isRunning) {
      console.log('PHRS充值监控任务已在运行');
      return;
    }

    try {
      // 启动区块链事件监听服务
      phrsDepositService.startListening();

      // 设置定时任务，每5分钟执行一次
      this.cronJob = cron.schedule('*/5 * * * *', async () => {
        await this.runMonitorTasks();
      }, {
        scheduled: false,
        timezone: 'Asia/Shanghai'
      });

      this.cronJob.start();
      this.isRunning = true;

      console.log('✅ PHRS充值监控任务启动成功');
      console.log('📅 定时任务: 每5分钟执行一次');
      
      // 立即执行一次监控任务
      this.runMonitorTasks();

    } catch (error) {
      console.error('❌ PHRS充值监控任务启动失败:', error);
      throw error;
    }
  }

  /**
   * 停止监控任务
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    try {
      // 停止定时任务
      if (this.cronJob) {
        this.cronJob.stop();
        this.cronJob = null;
      }

      // 停止区块链事件监听
      phrsDepositService.stopListening();

      this.isRunning = false;
      console.log('PHRS充值监控任务已停止');

    } catch (error) {
      console.error('停止PHRS充值监控任务时出错:', error);
    }
  }

  /**
   * 执行监控任务
   */
  private async runMonitorTasks(): Promise<void> {
    try {
      console.log('🔍 开始执行PHRS充值监控任务...');

      // 任务1: 检查待处理的充值
      await this.checkPendingDeposits();

      // 任务2: 重试失败的充值
      await this.retryFailedDeposits();

      // 任务3: 清理过期的失败记录
      await this.cleanupExpiredRecords();

      // 任务4: 输出统计信息
      await this.printStatistics();

      console.log('✅ PHRS充值监控任务执行完成');

    } catch (error) {
      console.error('❌ PHRS充值监控任务执行失败:', error);
    }
  }

  /**
   * 检查待处理的充值
   */
  private async checkPendingDeposits(): Promise<void> {
    try {
      const pendingDeposits = await PhrsDeposit.findAll({
        where: {
          status: 'PENDING',
          createdAt: {
            [Op.lt]: new Date(Date.now() - 10 * 60 * 1000) // 10分钟前的记录
          }
        },
        limit: 50
      });

      if (pendingDeposits.length === 0) {
        return;
      }

      console.log(`发现 ${pendingDeposits.length} 个待处理的PHRS充值记录`);

      for (const deposit of pendingDeposits) {
        try {
          // 检查交易状态
          const receipt = await phrsDepositService.provider.getTransactionReceipt(deposit.transactionHash);
          
          if (receipt) {
            if (receipt.status === 1) {
              // 交易成功，更新状态
              await deposit.update({
                status: 'CONFIRMED',
                confirmations: (await receipt.confirmations()) || 1,
                processedAt: new Date()
              });
              console.log(`✅ 充值 ${deposit.transactionHash} 已确认`);
            } else {
              // 交易失败
              await deposit.update({
                status: 'FAILED',
                errorMessage: 'Transaction failed on blockchain',
                processedAt: new Date()
              });
              console.log(`❌ 充值 ${deposit.transactionHash} 交易失败`);
            }
          }
        } catch (error) {
          console.error(`检查充值 ${deposit.transactionHash} 状态失败:`, error);
        }
      }

    } catch (error) {
      console.error('检查待处理充值失败:', error);
    }
  }

  /**
   * 重试失败的充值
   */
  private async retryFailedDeposits(): Promise<void> {
    try {
      const failedDeposits = await PhrsDeposit.findAll({
        where: {
          status: 'FAILED',
          errorMessage: 'User wallet not found',
          createdAt: {
            [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24小时内的记录
          }
        },
        limit: 20
      });

      if (failedDeposits.length === 0) {
        return;
      }

      console.log(`尝试重新处理 ${failedDeposits.length} 个失败的PHRS充值记录`);

      for (const deposit of failedDeposits) {
        try {
          // 重新查找用户钱包
          const { UserWallet } = await import('../models');
          const userWallet = await UserWallet.findOne({
            where: { phrsWalletAddress: deposit.userAddress }
          });

          if (userWallet) {
            // 找到用户钱包，重新处理充值
            const { BigNumber } = await import('bignumber.js');
            
            const depositAmount = new BigNumber(deposit.amount.toString());
            const currentBalance = new BigNumber(userWallet.phrsBalance?.toString() || '0');
            const newBalance = currentBalance.plus(depositAmount);

            // 更新用户余额和充值记录
            await userWallet.update({
              phrsBalance: newBalance.toFixed(3),
              phrsWalletAddress: deposit.userAddress,
              lastPhrsUpdateTime: new Date()
            });

            await deposit.update({
              walletId: userWallet.id,
              status: 'CONFIRMED',
              processedAt: new Date(),
              errorMessage: undefined
            });

            console.log(`✅ 重新处理充值成功: ${deposit.transactionHash}`);
          }
        } catch (error) {
          console.error(`重新处理充值 ${deposit.transactionHash} 失败:`, error);
        }
      }

    } catch (error) {
      console.error('重试失败充值时出错:', error);
    }
  }

  /**
   * 清理过期的失败记录
   */
  private async cleanupExpiredRecords(): Promise<void> {
    try {
      const expiredDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7天前
      
      const deletedCount = await PhrsDeposit.destroy({
        where: {
          status: 'FAILED',
          createdAt: {
            [Op.lt]: expiredDate
          }
        }
      });

      if (deletedCount > 0) {
        console.log(`🗑️  清理了 ${deletedCount} 个过期的失败充值记录`);
      }

    } catch (error) {
      console.error('清理过期记录失败:', error);
    }
  }

  /**
   * 输出统计信息
   */
  private async printStatistics(): Promise<void> {
    try {
      const stats = await PhrsDeposit.findAll({
        attributes: [
          'status',
          [PhrsDeposit.sequelize!.fn('COUNT', PhrsDeposit.sequelize!.col('id')), 'count'],
          [PhrsDeposit.sequelize!.fn('SUM', PhrsDeposit.sequelize!.col('amount')), 'totalAmount']
        ],
        group: ['status'],
        raw: true
      });

      console.log('📊 PHRS充值统计信息:');
      for (const stat of stats as any[]) {
        console.log(`   ${stat.status}: ${stat.count} 笔, 总额: ${stat.totalAmount || 0} PHRS`);
      }

      // 输出服务状态
      const serviceStatus = phrsDepositService.getStatus();
      console.log('🔧 服务状态:', serviceStatus);

    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  }

  /**
   * 获取监控状态
   */
  public getStatus(): object {
    return {
      isRunning: this.isRunning,
      cronSchedule: '*/5 * * * *',
      serviceStatus: phrsDepositService.getStatus()
    };
  }
}

// 导出单例实例
export const phrsDepositMonitor = new PhrsDepositMonitor();

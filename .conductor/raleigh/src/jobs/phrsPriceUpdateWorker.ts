// src/jobs/phrsPriceUpdateWorker.ts
import { Worker, Job } from "bullmq";
import { redis } from "../config/redis";
import { IapProduct } from "../models";
import dotenv from "dotenv";

dotenv.config();

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[PhrsPriceUpdateWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[PhrsPriceUpdateWorker] ${message}`, ...args);
  },
};

console.log('phrsPriceUpdateWorker.ts loaded');

// PHRS 兑换 USD 汇率配置
// 目前固定汇率：1 PHRS = 0.0001 USD
const PHRS_TO_USD_RATE = parseFloat(process.env.PHRS_TO_USD_RATE || '0.0001');

/**
 * 获取 PHRS 兑换 USD 汇率
 * 目前使用固定汇率，未来可以扩展为从API获取实时汇率
 */
async function getPhrsToUsdRate(): Promise<number> {
  try {
    logger.info(`使用固定汇率: 1 PHRS = ${PHRS_TO_USD_RATE} USD`);
    
    // 未来可以在这里添加从外部API获取实时汇率的逻辑
    // 例如：
    // const response = await axios.get('https://api.example.com/phrs-rate');
    // return response.data.rate;
    
    return PHRS_TO_USD_RATE;
  } catch (error) {
    logger.error('获取PHRS汇率失败，使用默认汇率:', error);
    return PHRS_TO_USD_RATE;
  }
}

/**
 * 更新所有IapProduct的pricePhrs字段
 */
async function updatePhrsPrices(phrsToUsdRate: number): Promise<void> {
  try {
    logger.info('开始更新所有IapProduct的pricePhrs字段...');
    
    // 获取所有有priceUsd的产品
    const products = await IapProduct.findAll({
      where: {
        priceUsd: {
          [require('sequelize').Op.gt]: 0
        }
      }
    });
    
    logger.info(`找到 ${products.length} 个需要更新的产品`);
    
    let updatedCount = 0;
    
    for (const product of products) {
      try {
        // 计算pricePhrs = priceUsd / phrsToUsdRate
        // 例如：产品价格 1 USD，汇率 0.0001，则需要 10000 PHRS
        const pricePhrs = parseFloat((product.priceUsd / phrsToUsdRate).toFixed(4));
        
        await product.update({
          pricePhrs: pricePhrs
        });
        
        logger.info(`产品 ${product.name} (ID: ${product.id}) PHRS价格已更新: ${product.priceUsd} USD = ${pricePhrs} PHRS (汇率: ${phrsToUsdRate})`);
        updatedCount++;
      } catch (error) {
        logger.error(`更新产品 ${product.id} 失败:`, error);
      }
    }
    
    logger.info(`PHRS价格更新完成，共更新了 ${updatedCount} 个产品`);
  } catch (error) {
    logger.error('更新PHRS价格失败:', error);
    throw error;
  }
}

// Worker 配置
export const phrsPriceUpdateWorker = new Worker(
  "phrs-price-update-job",
  async (job: Job) => {
    logger.info(`开始处理PHRS价格更新任务 ID: ${job.id}`);
    
    try {
      // 获取当前PHRS汇率
      const phrsToUsdRate = await getPhrsToUsdRate();
      
      // 更新所有产品的pricePhrs
      await updatePhrsPrices(phrsToUsdRate);
      
      logger.info(`PHRS价格更新任务 ID: ${job.id} 已完成`);
    } catch (error) {
      logger.error(`PHRS价格更新任务 ID: ${job.id} 失败:`, error);
      throw error; // 重新抛出错误以便BullMQ处理重试
    }
  },
  {
    connection: redis,
    concurrency: 1, // 同时只处理一个任务
    removeOnComplete: { count: 100 }, // 保留最近100个完成的任务
    removeOnFail: { count: 100 }, // 保留最近100个失败的任务
  }
);

logger.info('PHRS价格更新Worker已初始化');

// 处理Worker事件
phrsPriceUpdateWorker.on('completed', (job) => {
  logger.info(`PHRS价格更新任务完成: ${job.id}`);
});

phrsPriceUpdateWorker.on('failed', (job, err) => {
  logger.error(`PHRS价格更新任务失败: ${job?.id}`, err);
});

phrsPriceUpdateWorker.on('error', (err) => {
  logger.error('PHRS价格更新Worker错误:', err);
});

// 添加初始化函数，与其他worker保持一致的接口
export async function initializeWorker(queue: any) {
  console.log('初始化PHRS价格更新处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return phrsPriceUpdateWorker;
}

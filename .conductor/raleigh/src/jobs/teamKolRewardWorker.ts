import { Job, Worker } from "bullmq";
import { redis } from "../config/redis";
import { sequelize } from "../config/db";
import { User, UserWallet, PrizePool, RewardClaim, GameHistory } from "../models";
import dayjs from "dayjs";
import { Op } from "sequelize";
import fs from 'fs';
import path from 'path';
import BigNumber from 'bignumber.js';

const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[TeamKolRewardWorker] ${message}`, ...args);
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[TeamKolRewardWorker] ${message}`, ...args);
  },
};

console.log('teamKolRewardWorker.ts loaded');

// 子奖池配置
export const TEAM_KOL_POOLS = {
  silver: {
    minAmount: 100000,
    maxAmount: 200000,
    sharePercentage: 0.5
  },
  gold: {
    minAmount: 200000,
    maxAmount: Infinity,
    sharePercentage: 0.5
  }
};

async function backupRewardClaims(subPool: string) {
  const currentDate = dayjs().format('YYYY-MM-DD');
  const backupDir = path.join(__dirname, '../../backups/reward_claims');
  
  // 确保备份目录存在
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }

  // 获取所有奖励记录
  const claims = await RewardClaim.findAll({
    where: {
      subPool: `team_kol_${subPool}`
    }
  });

  // 写入备份文件
  const backupPath = path.join(backupDir, `team_kol_${subPool}_${currentDate}.json`);
  await fs.promises.writeFile(
    backupPath,
    JSON.stringify(claims, null, 2)
  );

  logger.info(`${subPool} 奖励记录已备份到: ${backupPath}`);
}

// 递归获取用户的所有下级用户（最多5级）
async function getAllDownlineUsers(userId: number, maxLevel: number = 5, currentLevel: number = 1): Promise<{userId: number, level: number}[]> {
  if (currentLevel > maxLevel) {
    return [];
  }
  
  // 获取当前用户的直接下级
  const directDownlines = await User.findAll({
    where: {
      referrerId: userId
    }
  });
  
  if (directDownlines.length === 0) {
    return [];
  }
  
  // 记录当前级别的下级用户
  const result: {userId: number, level: number}[] = directDownlines.map(user => ({
    userId: user.id,
    level: currentLevel
  }));
  
  // 递归获取下一级的下级用户
  for (const downline of directDownlines) {
    const nextLevelDownlines = await getAllDownlineUsers(downline.id, maxLevel, currentLevel + 1);
    result.push(...nextLevelDownlines);
  }
  
  return result;
}

// 获取用户团队的上周游戏量
export async function getTeamGameAmount(userId: number): Promise<number> {
  // 获取上周的开始和结束时间
  const lastWeekStart = dayjs().subtract(1, 'week').startOf('week').toDate();
  const lastWeekEnd = dayjs().subtract(1, 'week').endOf('week').toDate();
  
  // 获取用户的所有下级（最多5级）
  const downlineUsers = await getAllDownlineUsers(userId, 5);
  
  if (downlineUsers.length === 0) {
    return 0;
  }
  
  // 获取所有下级用户的ID
  const downlineUserIds = downlineUsers.map(user => user.userId);
  
  // 计算这些用户上周的游戏总量
  const gameHistories = await GameHistory.findAll({
    where: {
      userId: {
        [Op.in]: downlineUserIds
      },
      createdAt: {
        [Op.between]: [lastWeekStart, lastWeekEnd]
      }
    }
  });
  
  // 计算总游戏金额
  const totalGameAmount = gameHistories.reduce((sum, history) => {
    // 假设每场游戏消耗100 USD
    return sum + 100;
  }, 0);
  
  return totalGameAmount;
}

// 分配团队KOL奖励
async function distributeTeamKolReward() {
  const transaction = await sequelize.transaction();
  
  try {
    // 1. 获取团队KOL奖池
    const teamKolPool = await PrizePool.findOne({
      where: { type: 'team_kol' },
      transaction,
      lock: true
    });

    if (!teamKolPool || teamKolPool.amount <= 0) {
      logger.info('奖池为空或金额为0，跳过本次分发');
      await transaction.commit();
      return;
    }

    // 2. 计算每个子奖池的金额
    const totalPoolAmount = teamKolPool.amount;
    const silverPoolAmount = totalPoolAmount * TEAM_KOL_POOLS.silver.sharePercentage;
    const goldPoolAmount = totalPoolAmount * TEAM_KOL_POOLS.gold.sharePercentage;

    // 3. 获取所有用户
    const users = await User.findAll({
      transaction
    });

    // 4. 计算每个用户的团队游戏量并分类
    const silverTeams: Array<{userId: number, gameAmount: number}> = [];
    const goldTeams: Array<{userId: number, gameAmount: number}> = [];

    for (const user of users) {
      const gameAmount = await getTeamGameAmount(user.id);
      
      // 根据游戏量分配到不同的子奖池
      if (gameAmount >= TEAM_KOL_POOLS.gold.minAmount) {
        goldTeams.push({ userId: user.id, gameAmount });
      } else if (gameAmount >= TEAM_KOL_POOLS.silver.minAmount) {
        silverTeams.push({ userId: user.id, gameAmount });
      }
    }

    // 5. 分配银牌KOL奖池
    await distributeSubPoolRewards(silverTeams, silverPoolAmount, teamKolPool.id, 'silver', transaction);
    
    // 6. 分配金牌KOL奖池
    await distributeSubPoolRewards(goldTeams, goldPoolAmount, teamKolPool.id, 'gold', transaction);

    // 7. 更新奖池余额
    await teamKolPool.update({ amount: 0 }, { transaction });

    // 8. 备份奖励记录
    await backupRewardClaims('silver');
    await backupRewardClaims('gold');

    await transaction.commit();
    logger.info(`本周团队KOL奖励记录已更新，总金额: ${totalPoolAmount} USD`);

  } catch (error) {
    await transaction.rollback();
    logger.error('奖励记录更新失败:', error);
    throw error;
  }
}

// 分配子奖池奖励
async function distributeSubPoolRewards(
  teams: Array<{userId: number, gameAmount: number}>,
  poolAmount: number,
  prizePoolId: number,
  subPoolName: string,
  transaction: any
) {
  if (teams.length === 0 || poolAmount <= 0) {
    logger.info(`${subPoolName} 奖池没有符合条件的团队或奖池金额为0，跳过分配`);
    return;
  }

  // 计算总游戏量
  const totalGameAmount = teams.reduce((sum, team) => new BigNumber(sum).plus(team.gameAmount).toNumber(), 0);

  // 按比例分配奖励
  for (const team of teams) {
    const wallet = await UserWallet.findOne({
      where: { userId: team.userId },
      transaction
    });

    if (!wallet) {
      logger.info(`未找到用户 ${team.userId} 的钱包，跳过奖励分配`);
      continue;
    }

    // 计算该团队应得的奖励
    const teamShare = team.gameAmount / totalGameAmount;
    const rewardAmount = poolAmount * teamShare;

    // 查找是否存在未领取的奖励记录
    const existingClaim = await RewardClaim.findOne({
      where: {
        userId: team.userId,
        walletId: wallet.id,
        prizePoolId: prizePoolId,
        subPool: `team_kol_${subPoolName}`,
        claimed: false
      },
      transaction
    });

    if (existingClaim) {
      // 更新现有记录
      await existingClaim.update({
        amount: new BigNumber(existingClaim.amount).plus(rewardAmount).toNumber()
      }, { transaction });
      
      logger.info(`更新用户 ${team.userId} 的 ${subPoolName} 奖励记录，新增金额: ${rewardAmount} USD`);
    } else {
      // 创建新记录
      await RewardClaim.create({
        userId: team.userId,
        walletId: wallet.id,
        prizePoolId: prizePoolId,
        subPool: `team_kol_${subPoolName}`,
        amount: rewardAmount,
        claimed: false
      }, { transaction });

      logger.info(`为用户 ${team.userId} 创建新的 ${subPoolName} 奖励记录，金额: ${rewardAmount} USD`);
    }
  }
}

// Worker 配置
export const teamKolRewardWorker = new Worker(
  "team-kol-reward-job",
  async (job: Job) => {
    logger.info(`开始处理任务 ID: ${job.id}`);
    await distributeTeamKolReward();
    logger.info(`任务 ID: ${job.id} 已完成`);
  },
  {
    connection: redis,
    concurrency: 1,
    removeOnComplete: { count: 1000 },
    removeOnFail: { count: 1000 },
  }
);

// 错误处理
teamKolRewardWorker.on("failed", (job: Job | undefined, error: Error) => {
  logger.error(`任务 ${job?.id} 失败:`, error);
});

teamKolRewardWorker.on("error", (error: Error) => {
  logger.error("Worker 出错:", error);
});

// 定义消息类型接口
interface WorkerMessage {
  type: string;
  [key: string]: any; // 允许其他可能的属性
}

// 监听主进程发送的消息
process.on('message', async (message: WorkerMessage) => {
  if (message && message.type === 'shutdown') {
    logger.info('团队KOL奖励工作进程收到关闭信号，正在清理资源...');
    
    try {
      // 关闭 worker 连接
      await teamKolRewardWorker.close();
      
      logger.info('团队KOL奖励工作进程资源清理完毕，准备退出');
      
      // 通知主进程已准备好退出
      process.send?.({ type: 'ready_to_exit' });
      
      // 给主进程一些时间来接收消息
      setTimeout(() => {
        process.exit(0);
      }, 500);
    } catch (err) {
      logger.error('团队KOL奖励工作进程清理资源失败:', err);
      process.exit(1);
    }
  }
});

// 监听 SIGTERM 信号
process.on("SIGTERM", async () => {
  logger.info("收到 SIGTERM 信号，关闭 Worker...");
  try {
    await teamKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 监听 SIGINT 信号
process.on("SIGINT", async () => {
  logger.info("收到 SIGINT 信号，关闭 Worker...");
  try {
    await teamKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
    process.exit(0);
  } catch (error) {
    logger.error("关闭 Worker 失败:", error);
    process.exit(1);
  }
});

// 处理未捕获的异常
process.on("uncaughtException", async (error) => {
  logger.error("未捕获的异常:", error);
  try {
    await teamKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 处理未处理的 Promise 拒绝
process.on("unhandledRejection", async (reason) => {
  logger.error("未处理的 Promise 拒绝:", reason);
  try {
    await teamKolRewardWorker.close();
    logger.info("Worker 已关闭，准备退出");
  } catch (closeError) {
    logger.error("关闭 Worker 失败:", closeError);
  }
  process.exit(1);
});

// 添加导出的初始化函数
export async function initializeWorker(queue: any) {
  console.log('初始化团队 KOL 奖励处理器...');
  // Worker 已经在模块加载时创建，不需要额外操作
  return teamKolRewardWorker;
}

// 导出 worker 实例
export default teamKolRewardWorker;
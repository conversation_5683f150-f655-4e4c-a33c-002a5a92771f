// bot.ts
import { Bo<PERSON> } from "grammy";
import { randomUUID } from "crypto";

import { PurchaseRecord } from "./models";
import { User, UserWallet } from "./models";
import { PaymentRequest } from "./models";
import { logger } from "./utils/logger"; // 假设项目中有logger工具
import dotenv from "dotenv";
dotenv.config();
const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN as string);

// 处理 pre_checkout_query 更新
bot.on("pre_checkout_query", async (ctx) => {
  const preCheckoutQuery = ctx.update.pre_checkout_query;

  logger.info("收到支付预检查请求", { preCheckoutQuery: JSON.stringify(preCheckoutQuery) });

  try {
    // 验证支付负载
    const { isValid, errorMessage } = await validatePayload(preCheckoutQuery.invoice_payload);

    if (isValid) {
      logger.info("支付预检查通过", { payload: preCheckoutQuery.invoice_payload });
      await ctx.answerPreCheckoutQuery(true);
    } else {
      logger.warn("支付预检查失败", {
        payload: preCheckoutQuery.invoice_payload,
        reason: errorMessage
      });
      await ctx.answerPreCheckoutQuery(false, {
        error_message: errorMessage || "支付验证失败，请稍后再试",
      });
    }
  } catch (error) {
    logger.error("处理支付预检查时出错", {
      error: error instanceof Error ? error.message : '未知错误',
      payload: preCheckoutQuery.invoice_payload
    });
    await ctx.answerPreCheckoutQuery(false, {
      error_message: "系统处理错误，请稍后再试",
    });
  }
});

/**
 * 验证支付负载
 * @param payload 支付负载JSON字符串
 * @returns 验证结果对象，包含是否有效和错误消息
 */
async function validatePayload(payload: string): Promise<{ isValid: boolean; errorMessage?: string }> {
  try {
    // 解析负载数据
    const data = JSON.parse(payload);
    logger.info("验证支付负载", { data });

    // 基本数据验证
    if (!data.userId || !data.walletId || !data.paymentId || !data.productType) {
      return { isValid: false, errorMessage: "支付信息不完整" };
    }

    // 验证用户是否存在
    const user = await User.findByPk(data.userId);
    if (!user) {
      return { isValid: false, errorMessage: "用户不存在" };
    }

    // 验证钱包是否存在
    const wallet = await UserWallet.findByPk(data.walletId);
    if (!wallet || wallet.userId !== data.userId) {
      return { isValid: false, errorMessage: "钱包信息无效" };
    }

    // 验证支付请求是否有效
    const paymentRequest = await PaymentRequest.findOne({
      where: { paymentId: data.paymentId }
    });
    if (!paymentRequest) {
      return { isValid: false, errorMessage: "支付请求不存在" };
    }

    // 验证是否已经购买过相同产品
    const existingPurchase = await PurchaseRecord.findOne({
      where: {
        user_id: data.userId,
        product_type: data.productType,
        status: 'completed'
      }
    });

    if (existingPurchase) {
      // 根据产品类型决定是否允许重复购买
      if (data.productType === 'star_membership') {
        return { isValid: false, errorMessage: "您已购买过此产品" };
      }

      // 检查是否在允许的购买时间间隔内
      if (data.productType === 'daily_purchase') {
        const lastPurchaseDate = new Date(existingPurchase.purchase_date);
        const currentDate = new Date();
        const hoursSinceLastPurchase = (currentDate.getTime() - lastPurchaseDate.getTime()) / (1000 * 60 * 60);

        if (hoursSinceLastPurchase < 24) {
          return { isValid: false, errorMessage: "已达到每日购买限制" };
        }
      }
    }

    return { isValid: true };
  } catch (error) {
    logger.error("验证支付负载时出错", { error: error instanceof Error ? error.message : '未知错误' });
    return { isValid: false, errorMessage: "支付验证处理错误" };
  }
}

// 处理成功的支付
bot.on("message", async (ctx) => {
  if (ctx.message.successful_payment) {
    const paymentInfo = ctx.message.successful_payment;
    logger.info("收到支付成功消息", {
      telegramId: ctx.from?.id,
      paymentInfo: JSON.stringify(paymentInfo)
    });

    try {
      // 解析支付负载
      const data = JSON.parse(paymentInfo.invoice_payload);

      // 创建购买记录
      const purchaseRecord = await PurchaseRecord.create({
        user_id: data.userId,
        amount: paymentInfo.total_amount,
        currency: paymentInfo.currency,
        payment_id: paymentInfo.provider_payment_charge_id,
        telegram_payment_charge_id: paymentInfo.telegram_payment_charge_id,
        invoice_payload: paymentInfo.invoice_payload,
        purchase_date: Date.now().toString(),
        status: 'completed',
        product_type: data.productType || 'unknown',
        benefits_delivered: false
      });

      logger.info("购买记录已创建", { purchaseId: purchaseRecord.id });

      // 更新支付请求状态
      if (data.paymentId) {
        await PaymentRequest.update(
          { status: 'completed', completedAt: new Date() },
          { where: { paymentId: data.paymentId } }
        );
        logger.info("支付请求状态已更新", { paymentId: data.paymentId });
      }

      // 发放用户权益
      await deliverUserBenefits(data, purchaseRecord.id);

      // 发送成功消息给用户
      await ctx.reply(`🎉 支付成功！感谢您的购买。\n商品类型: ${data.productType || '未知产品'}\n金额: ${paymentInfo.total_amount} ${paymentInfo.currency}`);

      logger.info("支付流程处理完成", { purchaseId: purchaseRecord.id });
    } catch (error) {
      logger.error("处理支付成功消息时出错", {
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
        paymentInfo: JSON.stringify(paymentInfo)
      });

      // 通知用户支付已收到但处理出错
      try {
        await ctx.reply("✅ 您的支付已收到，但在处理过程中遇到了一些问题。我们的团队将尽快解决，请稍后查看您的权益状态。");
      } catch (replyError) {
        logger.error("发送支付错误回复失败", { error: replyError });
      }
    }
  }
});

/**
 * 发放用户购买权益
 * @param data 支付负载数据
 * @param purchaseId 购买记录ID
 */
async function deliverUserBenefits(data: any, purchaseId: number): Promise<void> {
  try {
    logger.info("开始发放用户权益", { userId: data.userId, productType: data.productType });

    // 根据产品类型发放不同权益
    switch (data.productType) {
      case 'star_membership':
        // 处理星级会员权益
        await activateStarMembership(data.userId, data.walletId);
        break;
      default:
        logger.warn("未知产品类型，无法发放具体权益", { productType: data.productType });
        break;
    }

    // 更新购买记录，标记权益已发放
    await PurchaseRecord.update(
      { benefits_delivered: true },
      { where: { id: purchaseId } }
    );

    logger.info("用户权益发放完成", { purchaseId });
  } catch (error) {
    logger.error("发放用户权益时出错", {
      error: error instanceof Error ? error.message : '未知错误',
      userId: data.userId,
      productType: data.productType
    });
    throw error; // 重新抛出错误，让上层处理
  }
}

/**
 * 激活星级会员
 */
async function activateStarMembership(userId: number, walletId: number): Promise<void> {
  // 实现星级会员激活逻辑
  logger.info("激活星级会员", { userId, walletId });
  // 更新userwallet表中的isStar字段为true 
  await UserWallet.update(
    { isStar: true },
    { where: { userId: userId, id: walletId } }
  );

}
export default bot;

// src/types.ts

/**
 * 定义描述部分的接口
 */
interface StoragePhase {
  storage_fees_collected: string;
  status_change: string;
}

interface ComputePhase {
  skipped: boolean;
  success: boolean;
  msg_state_used: boolean;
  account_activated: boolean;
  gas_fees: string;
  gas_used: string;
  gas_limit: string;
  mode: number;
  exit_code: number;
  vm_steps: number;
  vm_init_state_hash: string;
  vm_final_state_hash: string;
}

interface Action {
  success: boolean;
  valid: boolean;
  no_funds: boolean;
  status_change: string;
  total_fwd_fees: string;
  total_action_fees: string;
  result_code: number;
  tot_actions: number;
  spec_actions: number;
  skipped_actions: number;
  msgs_created: number;
  action_list_hash: string;
  tot_msg_size: Record<string, any>; // 具体结构根据实际情况调整
}

interface Description {
  type: string;
  aborted: boolean;
  destroyed: boolean;
  credit_first: boolean;
  storage_ph: StoragePhase;
  compute_ph: ComputePhase;
  action: Action;
}

/**
 * 定义 block_ref 部分的接口
 */
interface BlockRef {
  workchain: number;
  shard: string;
  seqno: number;
}

/**
 * 定义消息内容的接口
 */
interface MessageContent {
  hash: string;
  body: string;
  decoded: any | null; // 根据实际情况调整
}

/**
 * 定义 in_msg 和 out_msgs 的接口
 */
interface InMsg {
  hash: string;
  source: string;
  destination: string;
  value: string;
  fwd_fee: string;
  ihr_fee: string;
  created_lt: string;
  created_at: string;
  opcode: string;
  ihr_disabled: boolean;
  bounce: boolean;
  bounced: boolean;
  import_fee: any | null;
  message_content: MessageContent;
  init_state: any | null; // 根据实际情况调整
}

interface OutMsg extends InMsg {}

/**
 * 定义 account_state 部分的接口
 */
interface AccountState {
  hash: string;
  balance: string;
  account_status: string;
  frozen_hash: string | null;
  data_hash: string;
  code_hash: string;
}

/**
 * 定义 Transaction 接口
 */
export interface Transaction {
  account: string;
  hash: string;
  lt: string;
  now: number;
  mc_block_seqno: number;
  trace_id: string;
  prev_trans_hash: string;
  prev_trans_lt: string;
  orig_status: string;
  end_status: string;
  total_fees: string;
  description: Description;
  block_ref: BlockRef;
  in_msg: InMsg;
  out_msgs: OutMsg[];
  account_state_before: AccountState;
  account_state_after: AccountState;
}

/**
 * 定义 AdjacentTransactions API 的响应结构
 */
export interface AdjacentTransactionsResponse {
  transactions: Transaction[];
  // 如果有其他属性，可以在这里添加
}

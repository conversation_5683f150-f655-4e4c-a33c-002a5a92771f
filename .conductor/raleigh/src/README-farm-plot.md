# 牧场区（Farm Plot）系统实现文档

## 系统概述

牧场区系统是Moofun奶牛农场游戏的核心生产设施。玩家通过升级牧场区来增加牛舍数量，提高牛奶产量和产出速度。系统包括牧场区的基础属性、升级和解锁机制、生产循环系统以及相关API接口。

## 数据模型

### FarmPlot 模型

牧场区模型包含以下主要属性：

- `id`: 唯一标识符
- `walletId`: 关联的钱包ID
- `plotNumber`: 牧场区编号（1-20）
- `level`: 牧场区等级（1-20）
- `barnCount`: 牛舍数量
- `milkProduction`: 每次产出获得的牛奶量
- `productionSpeed`: 每次产出所需时间（秒）
- `unlockCost`: 解锁费用
- `upgradeCost`: 升级费用
- `lastProductionTime`: 上次产出时间
- `isUnlocked`: 是否已解锁
- `accumulatedMilk`: 累积的牛奶量

### User 模型扩展

为支持牧场区系统，User模型增加了以下字段：

- `gems`: 游戏内货币，用于升级和解锁牧场区
- `milk`: 牛奶，牧场区产出的资源

## 核心功能

### 1. 初始化用户牧场区

当用户首次访问牧场区系统时，系统会自动初始化20个牧场区，其中只有第1个牧场区默认解锁。

```typescript
public async initializeUserFarmPlots(walletId: number): Promise<void>
```

### 2. 升级牧场区

玩家可以使用宝石（GEM）升级已解锁的牧场区，每次升级会：
- 提升牧场区等级+1
- 增加牛舍数量+1
- 提升产出速度5%（秒数 / 1.05）
- 提升产量1.5倍
- 提升下次升级费用1.5倍

```typescript
public async upgradeFarmPlot(walletId: number, plotNumber: number): Promise<FarmPlot>
```

### 3. 解锁牧场区

玩家可以使用宝石解锁新的牧场区，解锁后：
- 牧场区等级设为1
- 牛舍数量设为1
- 基础产量为前一个牧场区的2.0倍
- 下一个牧场区的解锁费用提升2.0倍

```typescript
public async unlockFarmPlot(walletId: number, plotNumber: number): Promise<FarmPlot>
```

### 4. 牛奶生产循环

牧场区会根据产出速度自动生产牛奶，玩家可以随时收集：

```typescript
public async collectMilk(walletId: number, plotNumber: number): Promise<number>
public async collectAllMilk(walletId: number): Promise<number>
```

### 5. 离线收益计算

系统会计算玩家离线期间的牛奶产出：

```typescript
public async calculateOfflineEarnings(walletId: number, offlineTime: number): Promise<number>
```

## API接口

系统提供以下API接口：

- `GET /api/farm/farm-plots`: 获取用户的所有牧场区
- `POST /api/farm/farm-plots/upgrade`: 升级牧场区
- `POST /api/farm/farm-plots/unlock`: 解锁牧场区
- `POST /api/farm/farm-plots/collect`: 收集指定牧场区的牛奶
- `POST /api/farm/farm-plots/collect-all`: 收集所有牧场区的牛奶
- `POST /api/farm/farm-plots/offline-earnings`: 计算离线收益
- `POST /api/farm/farm-plots/add-milk`: 添加牛奶到生产线

## 数据库迁移

系统包含两个数据库迁移文件：

1. `20250602000000-create-farm-plots.js`: 创建farm_plots表
2. `20250602000001-add-gems-milk-to-users.js`: 为Users表添加gems和milk字段

## 使用示例

### 获取用户的所有牧场区

```typescript
const farmPlots = await farmPlotService.getUserFarmPlots(walletId);
```

### 升级牧场区

```typescript
const upgradedPlot = await farmPlotService.upgradeFarmPlot(walletId, plotNumber);
```

### 解锁新牧场区

```typescript
const unlockedPlot = await farmPlotService.unlockFarmPlot(walletId, plotNumber);
```

### 收集牛奶

```typescript
const collectedMilk = await farmPlotService.collectMilk(walletId, plotNumber);
// 或收集所有牧场区的牛奶
const totalCollectedMilk = await farmPlotService.collectAllMilk(walletId);
```

## 注意事项

1. 牧场区升级和解锁操作会消耗用户的宝石（GEM）
2. 牧场区等级上限为20级
3. 系统会自动处理离线时间的产量计算
4. 所有涉及资源变动的操作都使用事务确保数据一致性
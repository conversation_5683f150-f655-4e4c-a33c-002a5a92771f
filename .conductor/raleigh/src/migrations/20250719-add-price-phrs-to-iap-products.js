'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加 pricePhrs 字段到 iap_products 表
    await queryInterface.addColumn('iap_products', 'pricePhrs', {
      type: Sequelize.DECIMAL(20, 4), // 支持大数值，4位小数
      allowNull: true,
      comment: 'PHRS价格，基于USD价格和PHRS汇率计算'
    });
    
    console.log('✅ 已添加 iap_products.pricePhrs 字段');
  },

  async down(queryInterface, Sequelize) {
    // 回滚：删除 pricePhrs 字段
    await queryInterface.removeColumn('iap_products', 'pricePhrs');
    
    console.log('✅ 已删除 iap_products.pricePhrs 字段');
  }
};

// src/routes/userRoutes.ts
import { Router } from "express";
import { transferUSD, userInfo } from "../services/userService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { redis } from "../config/redis"; // 导入redis用于存储验证码
import { sendVerificationEmail, sendTransferVerificationEmail } from "../services/emailService";
import { bindUserEmail } from "../services/userService";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

import jwt from "jsonwebtoken";
const router = Router();
import dotenv from "dotenv";
import { User, UserWallet } from "../models";
dotenv.config();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

router.get("/me", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    const { userId, walletId } = myReq.user!;

    const user = await userInfo(userId, walletId!);

    res.json(successResponse(user));
  } catch (err: any) {
    res.status(400).json(errorResponse(
      tFromRequest(req, "errors.userInfoFailed"),
      err.message
    ));
    return;
  }
});

// 定义发送邮箱验证码请求体验证模式
const sendEmailCodeSchema = {
  type: "object",
  properties: {
    email: { type: "string", format: "email" }
  },
  required: ["email"]
};

const validateSendEmailCode = ajv.compile(sendEmailCodeSchema);

// 添加发送邮箱验证码的路由
//@ts-ignore
router.post("/send-email-code", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const { email } = req.body;

    // 验证请求体
    const valid = validateSendEmailCode(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateSendEmailCode.errors || [], req.language)
      ));
    }

    // 生成6位随机验证码
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 将验证码存储到Redis，设置5分钟过期
    const key = `email_verification:${userId}:${email}`;
    await redis.set(key, verificationCode, "EX", 300);
    
    // 发送验证码到用户邮箱
    await sendVerificationEmail(email, verificationCode);
    
    return res.json(successResponse(
      null,
      tFromRequest(req, "success.emailCodeSent")
    ));
  } catch (error: any) {
    console.error("发送邮箱验证码失败:", error);
    return res.status(400).json(errorResponse(
      tFromRequest(req, "errors.sendEmailCodeFailed"),
      error.message
    ));
  }
});

// 定义绑定邮箱请求体验证模式
const bindEmailSchema = {
  type: "object",
  properties: {
    email: { type: "string", format: "email" },
    verificationCode: { type: "string", minLength: 6, maxLength: 6 }
  },
  required: ["email", "verificationCode"]
};

const validateBindEmail = ajv.compile(bindEmailSchema);

// 添加绑定邮箱的路由
//@ts-ignore
router.post("/bind-email", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId; // 来自JWT
    const { email, verificationCode } = req.body;

    // 验证请求体
    const valid = validateBindEmail(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateBindEmail.errors || [], req.language)
      ));
    }
    
    // 验证验证码
    const key = `email_verification:${userId}:${email}`;
    const storedCode = Number(await redis.get(key));
    
    if (!storedCode) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.verificationCodeExpired")
      ));
    }
    
    if (storedCode !== Number(verificationCode)) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.incorrectVerificationCode")
      ));
    }
    
    // 验证通过，删除验证码
    await redis.del(key);

    const result = await bindUserEmail(userId, email);
    return res.json(successResponse(
      result,
      tFromRequest(req, "success.emailBound")
    ));
  } catch (error: any) {
    console.error("绑定邮箱失败:", error);
    return res.status(400).json(errorResponse(
      tFromRequest(req, "errors.bindEmailFailed"),
      error.message
    ));
  }
});

// 定义发送转账验证码请求体验证模式
const sendTransferCodeSchema = {
  type: "object",
  properties: {
    toWalletAddress: { type: "string", minLength: 1 },
    amount: { type: "number", minimum: 0.000001 }
  },
  required: ["toWalletAddress", "amount"]
};

const validateSendTransferCode = ajv.compile(sendTransferCodeSchema);

// 发送转账验证码
//@ts-ignore
router.post("/send-transfer-code", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const walletId = myReq.user!.walletId;
    const { toWalletAddress, amount } = req.body;

    // 验证请求体
    const valid = validateSendTransferCode(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateSendTransferCode.errors || [], req.language)
      ));
    }

    // 查找用户
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json(errorResponse(
        tFromRequest(req, "errors.userNotExist")
      ));
    }

    // 检查用户是否绑定了邮箱
    if (!user.email) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.emailNotBound")
      ));
    }

    // 查找钱包
    const wallet = await UserWallet.findByPk(walletId);
    if (!wallet) {
      return res.status(404).json(errorResponse(
        tFromRequest(req, "errors.walletNotFound")
      ));
    }

    // 检查余额是否足够
    if ((wallet.usd || 0) < amount) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.insufficientBalance")
      ));
    }

    // 生成6位随机验证码
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // 将验证码和转账信息存储到Redis，设置5分钟过期
    const key = `transfer_verification:${userId}:${walletId}`;
    await redis.hmset(key, {
      code: verificationCode,
      toWalletAddress,
      amount,
      timestamp: Date.now()
    });
    await redis.expire(key, 300); // 5分钟过期
    
    // 发送验证码到用户邮箱
    await sendTransferVerificationEmail(user.email, verificationCode, amount, toWalletAddress);
    
    return res.json(successResponse(
      null,
      tFromRequest(req, "success.transferCodeSent")
    ));
  } catch (error: any) {
    console.error("发送转账验证码失败:", error);
    return res.status(400).json(errorResponse(
      tFromRequest(req, "errors.sendTransferCodeFailed"),
      error.message
    ));
  }
});

// 定义执行转账请求体验证模式
const transferUSDSchema = {
  type: "object",
  properties: {
    verificationCode: { type: "string", minLength: 6, maxLength: 6 }
  },
  required: ["verificationCode"]
};

const validateTransferUSD = ajv.compile(transferUSDSchema);

// 执行转账
//@ts-ignore
router.post("/transfer-usd", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const walletId = myReq.user!.walletId;
    const { verificationCode } = req.body;

    // 验证请求体
    const valid = validateTransferUSD(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateTransferUSD.errors || [], req.language)
      ));
    }
    
    // 验证验证码
    const key = `transfer_verification:${userId}:${walletId}`;
    const transferData = await redis.hgetall(key);
    
    if (!transferData || Object.keys(transferData).length === 0) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.verificationCodeExpired")
      ));
    }
    
    if (transferData.code !== String(verificationCode)) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.incorrectVerificationCode")
      ));
    }
    
    // 检查验证码是否在5分钟内
    const timestamp = parseInt(transferData.timestamp);
    const now = Date.now();
    if (now - timestamp > 5 * 60 * 1000) {
      await redis.del(key);
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.verificationCodeExpired")
      ));
    }
    
    // 执行转账
    const result = await transferUSD(
      userId,
      walletId!,
      transferData.toWalletAddress,
      parseFloat(transferData.amount)
    );
    
    // 验证通过，删除验证码
    await redis.del(key);

    return res.json(successResponse(
      result,
      tFromRequest(req, "success.transferSuccess")
    ));
  } catch (error: any) {
    console.error("转账失败:", error);
    return res.status(400).json(errorResponse(
      tFromRequest(req, "errors.transferFailed"),
      error.message
    ));
  }
});

export default router;

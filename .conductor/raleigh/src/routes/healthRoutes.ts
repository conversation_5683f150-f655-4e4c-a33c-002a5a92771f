// src/routes/healthRoutes.ts
import { Router, Request, Response } from 'express';
import { sequelize } from '../config/db';
import { redis } from '../config/redis';
import { DeliveryLineConfig } from '../models/DeliveryLineConfig';

const router = Router();

// 健康检查端点
router.get('/health', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // 检查数据库连接
    await sequelize.authenticate();
    const dbStatus = 'ok';
    
    // 检查Redis连接
    await redis.ping();
    const redisStatus = 'ok';
    
    // 检查响应时间
    const responseTime = Date.now() - startTime;
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbStatus,
        redis: redisStatus
      },
      responseTime: `${responseTime}ms`,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
      }
    });
  } catch (error) {
    console.error('健康检查失败:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 简单的存活检查
router.get('/ping', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString()
  });
});

// 测试流水线配置接口（仅开发环境）
router.get('/test-delivery-configs', async (req: Request, res: Response): Promise<void> => {
  // 仅在开发环境下可用
  if (process.env.NODE_ENV !== 'development') {
    res.status(404).json({ message: 'Not found' });
    return;
  }

  try {
    const configs = await DeliveryLineConfig.findAll({
      order: [['grade', 'ASC']]
    });

    res.json({
      ok: true,
      message: '流水线配置测试成功',
      data: {
        configs: configs.map(config => ({
          level: config.grade,
          profit: config.profit,
          capacity: config.capacity,
          productionInterval: config.production_interval,
          deliverySpeedDisplay: config.delivery_speed_display,
          upgradeCost: config.upgrade_cost
        })),
        totalLevels: configs.length,
        testTime: new Date().toISOString()
      }
    });
  } catch (error: any) {
    res.status(500).json({
      ok: false,
      message: '流水线配置测试失败: ' + error.message,
      testTime: new Date().toISOString()
    });
  }
});

export default router;

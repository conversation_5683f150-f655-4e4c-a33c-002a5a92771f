import express, { Request, Response } from "express";
import crypto from "crypto";
import jwt from "jsonwebtoken";
import rateLimit from "express-rate-limit";
import { Address, Cell, contractAddress, loadStateInit } from "@ton/core";
import { CHAIN, toUserFriendlyAddress } from "@tonconnect/sdk";
import { User } from "../models/User";
import { Chest } from "../models/Chest";
import { UserWallet } from "../models/UserWallet";
import { generateUniqueCode } from "../utils/random";
import { registerUser } from "../services/userService";
import { tryParsePublicKey } from "../wrappers/wallets-data";
import { TonApiService } from "../ton-api-service";
import { CheckProofPayload, CheckTonProof, GenerateTonProofPayload } from "../dto";
import { ajv, t, formatValidationErrors, SupportedLanguage } from "../i18n";

// 定义验证模式
const checkProofSchema = {
  type: "object",
  properties: {
    proof: { type: "object" },
    address: { type: "string" },
    network: { type: "string" },
    public_key: { type: "string" },
    code: { type: "string", nullable: true },
    initData: { type: "string", nullable: false }
  },
  required: ["proof", "address", "network", "public_key"]
};

const validateCheckProof = ajv.compile(checkProofSchema);
import { PAYLOAD_TTL, PROOF_TTL } from "../constants";
import { CheckProofRequest } from "../dto/check-proof-request-dto";
import { TonProofService } from "../services/ton-proof-service";
import { handleNewUserContribution } from "../services/jackpotChestService";
import { sequelize } from "../config/db";

// JWT 密钥
const SHARED_SECRET = process.env.JWT_SECRET_Wallet!;
// 验证有效期：15分钟
const validAuthTime = 15 * 60;

const router = express.Router();

// 速率限制中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP限制100次请求
  message: { ok: false, message: "请求过于频繁，请稍后再试" }
});

// 判断是否是测试网
function isTestnet(chain: CHAIN) {
  return chain === CHAIN.TESTNET;
}

/** 辅助函数：生成 JWT token */
const generateToken = (userId: number, walletId: number, walletAddress: string, network: any = undefined): string => {
  return jwt.sign({ userId, walletId, walletAddress, network }, SHARED_SECRET, { expiresIn: "60d" });
};

/** 辅助函数：生成唯一的邀请码 */
const getUniqueInviteCode = async (): Promise<string> => {
  const MAX_TRIES = 100;
  for (let i = 0; i < MAX_TRIES; i++) {
    const candidate = generateUniqueCode(6);
    const existing = await UserWallet.findOne({ where: { code: candidate } });
    if (!existing) return candidate;
  }
  throw new Error("Failed to generate a unique invite code, please retry");
};

/** 验证 payload */
const validatePayload = (payloadBuffer: Buffer, address: string): void => {
  if (payloadBuffer.length !== 32) {
    throw new Error(`invalid payload length, got ${payloadBuffer.length}, expected 32`);
  }

  const mac = crypto.createHmac("sha256", SHARED_SECRET);
  mac.update(payloadBuffer.subarray(0, 16));
  const expectedSignature = mac.digest();

  if (!payloadBuffer.subarray(16).equals(expectedSignature.subarray(0, 16))) {
    throw new Error("invalid payload signature");
  }
};

/** 验证过期时间 */
const validateExpiration = (payloadBuffer: Buffer, proof: any, address: string): void => {
  const now = Math.floor(Date.now() / 1000);
  const expireTime = payloadBuffer.subarray(8, 16).readBigUint64BE();

  if (BigInt(now) > expireTime) {
    throw new Error("payload has expired");
  }

  if (now > proof.timestamp + PROOF_TTL) {
    // throw new Error("ton proof has expired");
  }

  if (now - validAuthTime > proof.timestamp) {
    // throw new Error("proof timestamp is too old");
  }
};

/** 验证过期时间 */
const validateExpiration2 = (payloadBuffer: Buffer, proof: any, address: string): void => {
  const now = Math.floor(Date.now() / 1000);
  const expireTime = payloadBuffer.subarray(8, 16).readBigUint64BE();

  if (BigInt(now) > expireTime) {
    throw new Error("payload has expired");
  }

  if (now > proof.timestamp + PROOF_TTL) {
    throw new Error("ton proof has expired");
  }

  if (now - validAuthTime > proof.timestamp) {
    throw new Error("proof timestamp is too old");
  }
};

/** 验证地址和公钥 */
const validateAddressAndPublicKey = async (address: string, proof: any, public_key: string, network: CHAIN): Promise<void> => {
  const stateInit = loadStateInit(Cell.fromBase64(proof.state_init).beginParse());
  const client = TonApiService.create(network);

  let pubKey = tryParsePublicKey(stateInit) ?? (await client.getWalletPublicKey(address));
  if (!pubKey) {
    throw new Error("checkProof error: public key not found");
  }

  const wantedPublicKey = Buffer.from(public_key, "hex");
  if (!pubKey.equals(wantedPublicKey)) {
    throw new Error("checkProof error: public key mismatch");
  }

  const expectedAddress = Address.parse(address);
  const contractAddr = contractAddress(expectedAddress.workChain, stateInit);
  if (!contractAddr.equals(expectedAddress)) {
    throw new Error("checkProof error: address mismatch");
  }
};

/** 生成 payload 接口 */
router.post(
  "/generate-payload",
  limiter,
  (_req: Request, res: Response<GenerateTonProofPayload>) => {
    try {
      console.log("[generate-payload] 开始生成 payload");

      const randomBits = crypto.randomBytes(8);
      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = Buffer.alloc(8);
      expirationTime.writeBigUint64BE(BigInt(currentTime + PAYLOAD_TTL));

      const payload = Buffer.concat([randomBits, expirationTime]);
      const hmac = crypto.createHmac("sha256", SHARED_SECRET);
      hmac.update(payload);
      const signature = hmac.digest();

      const finalPayload = Buffer.concat([payload, signature]);
      const payloadHex = finalPayload.subarray(0, 32).toString("hex");

      console.log(`[generate-payload] 成功生成 payload: ${payloadHex.substring(0, 10)}...`);
      res.json({ ok: true, payload: payloadHex });
    } catch (error) {
      console.error("[generate-payload] 生成失败:", error);
      res.status(500).json({ ok: false, message: "生成 payload 失败" });
    }
  }
);

/** 验证 proof 接口 */
router.post(
  "/check-proof",
  limiter,
  //@ts-ignore
  async (req: Request<{}, {}, CheckProofPayload>, res: Response<CheckTonProof>) => {
    const transaction = await sequelize.transaction();

    try {
      console.log("[check-proof] 开始验证 proof");
      const valid = validateCheckProof(req.body);
      if (!valid) {
        // 从请求头获取语言
        const acceptLanguage = req.headers["accept-language"];
        const lang = acceptLanguage?.split(",")[0].split("-")[0] as SupportedLanguage || "en";

        return res.status(400).json({
          ok: false,
          message: t("errors.paramValidation"),
          error: formatValidationErrors(validateCheckProof.errors || [], lang)
        });
      }
      const { proof, address, network, public_key, code, initData } = req.body;
      console.log(`[check-proof] 收到请求 - address: ${address}, network: ${network}`);

      const payloadBuffer = Buffer.from(proof.payload, "hex");

      // 验证 payload
      await validatePayload(payloadBuffer, address);
      console.log(`[check-proof] payload 签名验证成功 - address: ${address}`);

      // 验证过期时间
      validateExpiration(payloadBuffer, proof, address);

      // 验证 domain 长度
      if (proof.domain.lengthBytes !== proof.domain.value.length) {
        throw new Error(`domain length mismatched against provided length bytes of ${proof.domain.lengthBytes}`);
      }

      // 验证地址和公钥
      await validateAddressAndPublicKey(address, proof, public_key, network);
      console.log(`[check-proof] 地址和公钥验证成功 - address: ${address}`);

      const userFriendlyAddress = toUserFriendlyAddress(address, isTestnet(network));
      console.log(`[check-proof] 用户友好地址 - ${userFriendlyAddress}`);

      const claims = { address, userFriendlyAddress, network };
      const existingWallet = await UserWallet.findOne({
        where: { walletAddress: userFriendlyAddress },
        transaction
      });

      console.log(`[check-proof] 开始注册用户 - address: ${userFriendlyAddress}`);
      const user = await registerUser(initData, code, network, transaction);
      const userId = user.id;
      console.log(`[check-proof] 用户注册成功 - userId: ${userId}`);

      // 检查用户是否已经绑定了钱包
      const userWallets = await UserWallet.findAll({
        where: { userId },
        transaction
      });

      if (userWallets.length > 0) {
        if (existingWallet && existingWallet.userId === userId) {
          const token = generateToken(userId, existingWallet.id, userFriendlyAddress, network);
          await transaction.commit();
          return res.json({ data: claims, ok: true, token });
        } else {
          throw new Error("Each TG account can only bind one wallet");
        }
      }

      if (existingWallet) {
        throw new Error("Wallet address already bind with other account");
      }

      const inviteCode = await getUniqueInviteCode();

      let referrerWalletId;
      if (code) {
        const referrerWallet = await UserWallet.findOne({
          where: { code },
          transaction
        });
        if (referrerWallet) {
          referrerWalletId = referrerWallet.id;
          await UserWallet.increment('referralCount', {
            by: 1,
            where: { id: referrerWalletId },
            transaction
          });
          console.log(`[check-proof] 找到推荐人钱包 - referrerWalletId: ${referrerWalletId}`);
        }
      }

      const userWallet = await UserWallet.create({
        userId,
        walletAddress: userFriendlyAddress,
        code: inviteCode,
        referrerWalletId,
        referralCount: 0,
        network: network,
        milk:0,
        parsedWalletAddress: Address.parse(userFriendlyAddress).toString(),
      }, { transaction });

      const walletId = userWallet.id;
      console.log(`[check-proof] 新钱包创建成功 - walletId: ${walletId}, code: ${inviteCode}`);

      user.firstWalletId = walletId;
      await user.save({ transaction });

      try {

        // 处理新用户的 注册池和 Jackpot 贡献
        await handleNewUserContribution(userId, walletId, transaction);
      } catch (error) {
        console.error("处理新用户 Jackpot 贡献失败:", error);
      }

      await transaction.commit();

      const token = generateToken(userId, walletId, userFriendlyAddress);
      console.log(`[check-proof] 认证完成 - userId: ${userId}, walletId: ${walletId}`);
      return res.json({ data: claims, ok: true, token });

    } catch (error) {
      await transaction.rollback();
      console.error("[check-proof] 处理失败:", error);
      const message = error instanceof Error ? error.message : t("errors.unknown");
      return res.status(400).json({ ok: false, message });
    }
  }
);

/** 定义登录验证模式 */
const loginProofSchema = {
  type: "object",
  properties: {
    proof: {
      type: "object",
      properties: {
        payload: { type: "string" },
        timestamp: { type: "number" },
        state_init: { type: "string" },
        signature: { type: "string" },
        domain: {
          type: "object",
          properties: {
            lengthBytes: { type: "number" },
            value: { type: "string" }
          },
        }
      },
    },
    address: { type: "string" },
    network: { type: "string" },
    public_key: { type: "string" }
  },
  required: ["proof", "address", "network", "public_key"]
};

const validateLoginProof = ajv.compile(loginProofSchema);

/** 登录接口 - 只验证钱包，不注册新用户 */
router.post(
  "/login",
  limiter,
  //@ts-ignore
  async (req: Request<{}, {}, CheckProofPayload>, res: Response<CheckTonProof>) => {
    try {
      console.log("[login] 开始验证钱包登录");
      const valid = validateLoginProof(req.body);
      if (!valid) {
        // 从请求头获取语言
        const acceptLanguage = req.headers["accept-language"];
        const lang = acceptLanguage?.split(",")[0].split("-")[0] as SupportedLanguage || "en";

        return res.status(400).json({
          ok: false,
          message: t("errors.paramValidation"),
          error: formatValidationErrors(validateLoginProof.errors || [], lang)
        });
      }
      const { proof, address, network, public_key } = req.body;

      // 检查环境变量
      const skipProofCheck = process.env.SKIP_PROOF_CHECK === 'true';
      
      if (!skipProofCheck) {
        const body = CheckProofRequest.parse(req.body);
        const client = TonApiService.create(body.network);
        const service = new TonProofService();

        const isValid = await service.checkProof(body, (address) => client.getWalletPublicKey(address));
        if (!isValid) {
          throw new Error("Invalid proof");
        }
      } else {
        console.log('[login] 跳过 proof 验证 (非生产环境)');
      }

      console.log(`[login] 收到登录请求 - address: ${address}, network: ${network}`);

      const payloadBuffer = Buffer.from(proof.payload, "hex");

      // 验证 payload
      await validatePayload(payloadBuffer, address);
      console.log(`[login] payload 签名验证成功 - address: ${address}`);

      // 验证 domain 长度
      if (proof.domain.lengthBytes !== proof.domain.value.length) {
        throw new Error(`domain length mismatched against provided length bytes of ${proof.domain.lengthBytes}`);
      }

      const userFriendlyAddress = toUserFriendlyAddress(address, isTestnet(network));
      console.log(`[login] 用户友好地址 - ${userFriendlyAddress}`);

      // 查找钱包是否存在
      const existingWallet = await UserWallet.findOne({ where: { walletAddress: userFriendlyAddress } });
      if (!existingWallet) {
        // 钱包不存在，返回错误
        console.log(`[login] 钱包地址未注册 - address: ${userFriendlyAddress}`);
        return res.status(404).json({ ok: false, message: "Wallet not registered" });
      }

      // 获取用户信息
      const user = await User.findByPk(existingWallet.userId);
      if (!user) {
        console.log(`[login] 用户不存在 - userId: ${existingWallet.userId}`);
        return res.status(404).json({ ok: false, message: "User not found" });
      }

      // 生成JWT令牌
      const token = generateToken(user.id, existingWallet.id, userFriendlyAddress, network);
      console.log(`[login] 登录成功 - userId: ${user.id}, walletId: ${existingWallet.id}`);

      const claims = { address, userFriendlyAddress, network };
      return res.json({ data: claims, ok: true, token });
    } catch (error) {
      console.error("[login] 处理失败:", error);
      const message = error instanceof Error ? error.message : t("errors.unknown");
      return res.status(400).json({ ok: false, message });
    }
  }
);

export { router };
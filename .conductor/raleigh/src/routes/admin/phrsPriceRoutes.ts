// src/routes/admin/phrsPriceRoutes.ts
import { Router, Request, Response } from 'express';
import { PhrsPriceService } from '../../services/phrsPriceService';
import { triggerPhrsPriceUpdateJob } from '../../jobs/schedulePhrsPriceUpdateJob';
import { IapProduct } from '../../models';

const router = Router();

/**
 * 获取当前PHRS汇率信息
 * GET /admin/phrs-price/rate
 */
router.get('/rate', async (req: Request, res: Response) => {
  try {
    const rateInfo = PhrsPriceService.getCurrentRateInfo();
    
    res.json({
      success: true,
      data: rateInfo,
      message: '获取PHRS汇率信息成功'
    });
  } catch (error) {
    console.error('获取PHRS汇率信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取PHRS汇率信息失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 手动触发PHRS价格更新
 * POST /admin/phrs-price/update
 */
router.post('/update', async (req: Request, res: Response) => {
  try {
    // 触发异步任务
    const job = await triggerPhrsPriceUpdateJob();
    
    res.json({
      success: true,
      data: {
        jobId: job.id,
        message: 'PHRS价格更新任务已添加到队列'
      },
      message: '手动触发PHRS价格更新成功'
    });
  } catch (error) {
    console.error('手动触发PHRS价格更新失败:', error);
    res.status(500).json({
      success: false,
      message: '手动触发PHRS价格更新失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 同步更新所有产品PHRS价格（立即执行）
 * POST /admin/phrs-price/sync-update
 */
router.post('/sync-update', async (req: Request, res: Response) => {
  try {
    const updatedCount = await PhrsPriceService.updateAllProductsPhrsPrices();
    
    res.json({
      success: true,
      data: {
        updatedCount,
        rate: PhrsPriceService.getPhrsToUsdRate()
      },
      message: `成功更新 ${updatedCount} 个产品的PHRS价格`
    });
  } catch (error) {
    console.error('同步更新PHRS价格失败:', error);
    res.status(500).json({
      success: false,
      message: '同步更新PHRS价格失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 获取单个产品的价格信息
 * GET /admin/phrs-price/product/:productId
 */
router.get('/product/:productId', async (req: Request, res: Response): Promise<void> => {
  try {
    const productId = parseInt(req.params.productId);
    if (isNaN(productId)) {
      res.status(400).json({
        success: false,
        message: '无效的产品ID'
      });
      return;
    }

    const priceInfo = await PhrsPriceService.getProductPrices(productId);
    if (!priceInfo) {
      res.status(404).json({
        success: false,
        message: '产品不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: priceInfo,
      message: '获取产品价格信息成功'
    });
  } catch (error) {
    console.error('获取产品价格信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取产品价格信息失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 更新单个产品的PHRS价格
 * POST /admin/phrs-price/product/:productId/update
 */
router.post('/product/:productId/update', async (req: Request, res: Response): Promise<void> => {
  try {
    const productId = parseInt(req.params.productId);
    if (isNaN(productId)) {
      res.status(400).json({
        success: false,
        message: '无效的产品ID'
      });
      return;
    }

    const product = await PhrsPriceService.updateProductPhrsPrice(productId);
    if (!product) {
      res.status(404).json({
        success: false,
        message: '产品不存在'
      });
      return;
    }

    res.json({
      success: true,
      data: {
        productId: product.id,
        productName: product.name,
        usdPrice: product.priceUsd,
        phrsPrice: product.pricePhrs,
        rate: PhrsPriceService.getPhrsToUsdRate()
      },
      message: '产品PHRS价格更新成功'
    });
  } catch (error) {
    console.error('更新产品PHRS价格失败:', error);
    res.status(500).json({
      success: false,
      message: '更新产品PHRS价格失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 验证PHRS支付金额
 * POST /admin/phrs-price/validate-payment
 */
router.post('/validate-payment', async (req: Request, res: Response): Promise<void> => {
  try {
    const { productId, phrsAmount } = req.body;

    if (!productId || !phrsAmount) {
      res.status(400).json({
        success: false,
        message: '缺少必要参数: productId, phrsAmount'
      });
      return;
    }

    const validation = await PhrsPriceService.validatePhrsPayment(productId, phrsAmount);
    
    res.json({
      success: true,
      data: validation,
      message: validation.isValid ? 'PHRS支付金额验证通过' : 'PHRS支付金额验证失败'
    });
  } catch (error) {
    console.error('验证PHRS支付金额失败:', error);
    res.status(500).json({
      success: false,
      message: '验证PHRS支付金额失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 获取所有产品的价格概览
 * GET /admin/phrs-price/products
 */
router.get('/products', async (req: Request, res: Response) => {
  try {
    const products = await IapProduct.findAll({
      attributes: ['id', 'productId', 'name', 'priceUsd', 'priceKaia', 'pricePhrs', 'isActive'],
      where: {
        isActive: true
      },
      order: [['id', 'ASC']]
    });

    const rateInfo = PhrsPriceService.getCurrentRateInfo();
    
    res.json({
      success: true,
      data: {
        products: products.map(product => ({
          id: product.id,
          productId: product.productId,
          name: product.name,
          priceUsd: product.priceUsd,
          priceKaia: product.priceKaia,
          pricePhrs: product.pricePhrs,
          calculatedPhrsPrice: PhrsPriceService.calculatePhrsPrice(product.priceUsd),
          isActive: product.isActive
        })),
        rateInfo
      },
      message: '获取产品价格概览成功'
    });
  } catch (error) {
    console.error('获取产品价格概览失败:', error);
    res.status(500).json({
      success: false,
      message: '获取产品价格概览失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;

// src/routes/adminAnalyticsRoutes.ts

import { Router } from "express";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { adminAuthMiddleware } from "../middlewares/adminAuth";
import * as adminAnalyticsController from "../controllers/adminAnalyticsController";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 应用管理员认证中间件到所有路由
router.use(adminAuthMiddleware);

/**
 * 游戏基础统计API
 */

// 获取每日游戏启动次数
// GET /api/admin/analytics/game-starts?date=2024-01-01
router.get("/game-starts", adminAnalyticsController.getGameStartStats);

// 获取解锁区域统计
// GET /api/admin/analytics/unlocked-areas
router.get("/unlocked-areas", adminAnalyticsController.getUnlockedAreasStats);

// 获取配送线升级统计
// GET /api/admin/analytics/delivery-line-upgrades?levels=10,20,25,30,35,40,45,50
router.get("/delivery-line-upgrades", adminAnalyticsController.getDeliveryLineUpgradeStats);

// 获取道具使用统计
// GET /api/admin/analytics/booster-usage
router.get("/booster-usage", adminAnalyticsController.getBoosterUsageStats);

// 获取充值数据统计
// GET /api/admin/analytics/revenue
router.get("/revenue", adminAnalyticsController.getRevenueStats);

// 获取每日APP开启统计
// GET /api/admin/analytics/daily-app-opens?days=7
router.get("/daily-app-opens", adminAnalyticsController.getDailyAppOpensStats);

/**
 * 玩家进度统计API
 */

// 获取牧场区升级统计
// GET /api/admin/analytics/farm-plot-upgrades?levels=10,20,25,30,35,40,45,50
router.get("/farm-plot-upgrades", adminAnalyticsController.getFarmPlotUpgradeStats);

// 获取任务完成统计
// GET /api/admin/analytics/task-completion
router.get("/task-completion", adminAnalyticsController.getTaskCompletionStats);

// 获取邀请好友统计
// GET /api/admin/analytics/referral
router.get("/referral", adminAnalyticsController.getReferralStats);

// 获取宝箱统计
// GET /api/admin/analytics/chest
router.get("/chest", adminAnalyticsController.getChestStats);

/**
 * 玩家留存和增长统计API
 */

// 获取玩家增长和留存统计
// GET /api/admin/analytics/player-growth-retention
router.get("/player-growth-retention", adminAnalyticsController.getPlayerGrowthRetentionStats);

// 获取玩家资源统计
// GET /api/admin/analytics/player-resources
router.get("/player-resources", adminAnalyticsController.getPlayerResourceStats);

// 获取玩家游玩时长统计
// GET /api/admin/analytics/play-time
router.get("/play-time", adminAnalyticsController.getPlayTimeStats);

/**
 * 综合统计API
 */

// 获取综合统计面板数据
// GET /api/admin/analytics/dashboard
router.get("/dashboard", adminAnalyticsController.getDashboardStats);

// 自定义查询统计
// POST /api/admin/analytics/custom-query
router.post("/custom-query", adminAnalyticsController.getCustomQueryStats);

export default router;
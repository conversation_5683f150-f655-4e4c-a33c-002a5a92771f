// src/routes/chestRoutes.ts
import { Router } from "express";
import { chestCount, processOpenChest, getUserChestRecords } from "../services/chestService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors, t } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { manualReplaceVariables, tWithVariables, processErrorMessage } from "../utils/translationUtil";
import { sequelize } from "../config/db";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义开箱请求体验证模式
const openChestSchema = {
  type: "object",
  properties: {
    amount: { type: "integer", enum: [1, 10] }
  },
  required: ["amount"]
};

const validateOpenChest = ajv.compile(openChestSchema);

//@ts-ignore
router.post("/open", walletAuthMiddleware, async (req, res) => {



  try {
    const myReq = req as MyRequest;

    const { walletId, userId } = myReq.user!;
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }

    // 验证请求体
    const valid = validateOpenChest(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateOpenChest.errors || [], req.language)
      ));
    }

    const { amount } = req.body;
    console.log(`用户 ${userId} 请求开启 ${amount} 个宝箱，钱包ID: ${walletId}`);

    const transaction = await sequelize.transaction();

    try {
      const result = await processOpenChest({
        count: amount,
        walletId,
        userId,
        transaction
      });
      await transaction.commit();
      console.log(`宝箱开启成功，返回结果:`, {
        openedCount: result.openedCount,
        levelSummary: result.levelSummary
      });

      res.json(successResponse(result, tFromRequest(req, "success.chestOpened")));
    } catch (openError: any) {
      console.error(`宝箱开启失败:`, openError);

      await transaction.rollback();

      // 调试信息
      console.log(`请求语言: ${req.language || '未设置'}`);

      // 处理错误消息
      let errorMessage = processErrorMessage(openError.message, req.language);

      // 如果errorMessage没有变化(可能是未知错误)，使用默认错误信息
      if (errorMessage === openError.message) {
        errorMessage = tFromRequest(req, "errors.openChestsFailed");
      }

      console.log(`最终错误消息: ${errorMessage}`);
      res.status(400).json(errorResponse(errorMessage));
    }
  } catch (err: any) {
    console.error(`处理开启宝箱请求时出错:`, err);
    res.status(400).json(errorResponse(err.message));
  }
});

//@ts-ignore
router.get("/count", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    const { walletId } = myReq.user!;
    if (!walletId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }
    const result = await chestCount(walletId);
    res.json(successResponse(result, tFromRequest(req, "success.chestCount")));
  } catch (err: any) {
    res.status(400).json(errorResponse(err.message));
  }
});


/**
 * GET /api/chest/my-chest-records?page=1&limit=20
 * 查询用户的宝箱记录，包括开启时间和获得的奖励信息
 */
//@ts-ignore
router.get("/my-chest-records", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;

    const { walletId, userId } = myReq.user!;
    if (!walletId || !userId) {
      return res.status(400).json(errorResponse(tFromRequest(req, "errors.missingWalletId")));
    }

    // 解析分页参数
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    const result = await getUserChestRecords(userId, walletId, page, limit);
    res.json(successResponse(result, tFromRequest(req, "success.getChestRecords")));
  } catch (err: any) {
    console.error(`获取宝箱记录失败:`, err);
    res.status(400).json(errorResponse(err.message));
  }
});

export default router;

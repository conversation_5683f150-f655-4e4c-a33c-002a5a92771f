// src/routes/taskRoutes.ts
import { Router } from "express";
import { completeTask, getTaskListWithStatus } from "../services/taskService";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

/**
 * GET /api/tasks
 * 查询用户的任务列表 & 完成状态
 */
router.get("/", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId; // 来自JWT
    const tasks = await getTaskListWithStatus(userId);
    
    // 对任务名称进行翻译
    const translatedTasks = tasks.map(task => {
      // 根据任务名称映射到正确的翻译键
      let taskKey;
      
      // 映射数据库中的任务名称到预定义的翻译键
      if (task.type.toLowerCase().includes('daily') || task.type.toLowerCase().includes('signin')) {
        taskKey = 'tasks.dailySignin';
      } else if (task.type.toLowerCase().includes('telegram') || task.type.toLowerCase().includes('join')) {
        taskKey = 'tasks.joinTelegram';
      } else if (task.type.toLowerCase().includes('twitter') || task.type.toLowerCase().includes('follow') || task.type.toLowerCase().includes('x')) {
        taskKey = 'tasks.followTwitter';
      } else {
        // 如果没有匹配到预定义的键，使用原来的逻辑作为后备
        taskKey = task.type.startsWith('tasks.') 
          ? task.type 
          : `tasks.${task.type.replace(/\s+/g, "")}`;
      }
      
      return {
        ...task,
        name: tFromRequest(req, taskKey)
      };
    });
    
    res.json(successResponse(translatedTasks));
    return;
  } catch (err: any) {
    res.status(400).json(errorResponse(
      tFromRequest(req, "errors.getTaskListFailed"),
      err.message
    ));
    return;
  }
});

// 定义完成任务请求体验证模式
const completeTaskSchema = {
  type: "object",
  properties: {
    taskId: { type: "integer", minimum: 1 }
  },
  required: ["taskId"]
};

const validateCompleteTask = ajv.compile(completeTaskSchema);

/**
 * body: { taskId: number }
 * POST /api/tasks/complete
 */
//@ts-ignore
router.post("/complete", walletAuthMiddleware, async (req, res) => {
  try {
    // 验证请求体
    const valid = validateCompleteTask(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateCompleteTask.errors || [], req.language)
      ));
    }

    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const walletId = myReq.user!.walletId;
    const { taskId } = req.body;

    const result = await completeTask(userId, taskId, walletId!);
    res.json(successResponse(
      result,
      tFromRequest(req, "success.taskCompleted")
    ));
  } catch (err: any) {
    res.status(400).json(errorResponse(
      tFromRequest(req, "errors.completeTaskFailed"),
      err.message
    ));
  }
});

export default router;

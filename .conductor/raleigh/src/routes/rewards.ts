import { Chest, <PERSON><PERSON><PERSON><PERSON>m, UserWallet, WalletHistory } from "../models";
import { Router } from "express";
import { walletAuthMiddleware } from "../middlewares/walletAuth";
import { MyRequest } from "../types/customRequest";
import { Op } from "sequelize";
import { sequelize } from "../config/db";
import { getUserDirectReferralGameAmount } from "../jobs/personalKolRewardWorker";
import { getTeamGameAmount } from "../jobs/teamKolRewardWorker";
import { KOL_POOLS } from "../jobs/personalKolRewardWorker";
import { TEAM_KOL_POOLS } from "../jobs/teamKolRewardWorker";
import { ajv, tFromRequest, formatValidationErrors } from "../i18n";
import { languageMiddleware } from "../middlewares/languageMiddleware";
import { successResponse, errorResponse } from "../utils/responseUtil";
import { chestCount } from "../services/chestService";

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义参数验证模式
const rewardIdParamSchema = {
  type: "object",
  properties: {
    id: { type: "string", pattern: "^[0-9]+$" }
  },
  required: ["id"]
};

const validateRewardIdParam = ajv.compile(rewardIdParamSchema);

/**
 * 获取用户的KOL奖励
 */
//@ts-ignore
router.get("/kol-rewards", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const walletId = myReq.user!.walletId; // 来自JWT
    
    if (!walletId) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.walletNotFound")
      ));
    }
    
    const rewards = await RewardClaim.findAll({
      where: {
        walletId,
        subPool: {
          [Op.like]: 'personal_kol_%'
        },
        claimed: false
      }
    });
    
    return res.json(successResponse(
      rewards,
      tFromRequest(req, "success.getKolRewards")
    ));
  } catch (error) {
    console.error("获取KOL奖励失败:", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getKolRewardsFailed"),
      (error as Error).message
    ));
  }
});

/**
 * 领取KOL奖励
 */
//@ts-ignore
router.post("/claim-kol-reward/:id", walletAuthMiddleware, async (req, res) => {
  try {
    // 验证参数
    const params = { id: req.params.id };
    const valid = validateRewardIdParam(params);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.paramValidation"),
        formatValidationErrors(validateRewardIdParam.errors || [], req.language)
      ));
    }
    
    const transaction = await sequelize.transaction();
    
    try {
      const rewardId = req.params.id;
      const myReq = req as MyRequest;
      const userId = myReq.user!.userId; // 来自JWT
      const walletId = myReq.user!.walletId; // 来自JWT
      
      if (!walletId) {
        await transaction.rollback();
        return res.status(400).json(errorResponse(
          tFromRequest(req, "errors.walletNotFound")
        ));
      }
      
      const reward = await RewardClaim.findOne({
        where: {
          id: rewardId,
          walletId,
          claimed: false
        },
        transaction
      });
      
      if (!reward) {
        await transaction.rollback();
        return res.status(404).json(errorResponse(
          tFromRequest(req, "errors.rewardNotFoundOrClaimed")
        ));
      }
      
      // 更新奖励状态
      await reward.update({
        claimed: true,
        claimTime: new Date()
      }, { transaction });
      
      // 增加用户钱包余额
      await UserWallet.increment("usd", {
        by: reward.amount,
        where: { id: reward.walletId },
        transaction
      });
      
      // 创建钱包历史记录
      await WalletHistory.create({
        userId,
        walletId: reward.walletId,
        amount: reward.amount,
        currency: "usd",
        reference: "KOL Reward",
        action: "in",
        category: "usd",
        credit_type: "usd",
        fe_display_remark: `KOL奖励 - ${reward.subPool!.replace('personal_kol_', '')}`,
        developer_remark: `KOL奖励领取 - ${reward.subPool}`
      }, { transaction });
      
      await transaction.commit();
      
      return res.json(successResponse(
        reward,
        tFromRequest(req, "success.claimKolReward")
      ));
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error("领取KOL奖励失败:", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.claimKOLRewardFailed"),
      (error as Error).message
    ));
  }
});

/**
 * 获取用户的 KOL 等级和游戏量信息
 */
//@ts-ignore
router.get("/kol-status", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;

    if (!userId) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.missingUserId")
      ));
    }

    // 获取一级直推游戏量
    const directReferralGameAmount = await getUserDirectReferralGameAmount(userId);
    
    // 获取团队游戏量
    const teamGameAmount = await getTeamGameAmount(userId);

    // 判断个人 KOL 等级
    let personalKolLevel = tFromRequest(req, "labels.notQualified");
    if (directReferralGameAmount >= KOL_POOLS.three_star.minAmount) {
      personalKolLevel = tFromRequest(req, "labels.threeStarKol");
    } else if (directReferralGameAmount >= KOL_POOLS.two_star.minAmount) {
      personalKolLevel = tFromRequest(req, "labels.twoStarKol");
    } else if (directReferralGameAmount >= KOL_POOLS.one_star.minAmount) {
      personalKolLevel = tFromRequest(req, "labels.oneStarKol");
    }

    // 判断团队 KOL 等级
    let teamKolLevel = tFromRequest(req, "labels.notQualified");
    if (teamGameAmount >= TEAM_KOL_POOLS.gold.minAmount) {
      teamKolLevel = tFromRequest(req, "labels.goldKol");
    } else if (teamGameAmount >= TEAM_KOL_POOLS.silver.minAmount) {
      teamKolLevel = tFromRequest(req, "labels.silverKol");
    }

    return res.json(successResponse(
      {
        directReferral: {
          gameAmount: directReferralGameAmount,
          level: personalKolLevel,
          requirements: {
            one_star: KOL_POOLS.one_star.minAmount,
            two_star: KOL_POOLS.two_star.minAmount,
            three_star: KOL_POOLS.three_star.minAmount
          }
        },
        team: {
          gameAmount: teamGameAmount,
          level: teamKolLevel,
          requirements: {
            silver: TEAM_KOL_POOLS.silver.minAmount,
            gold: TEAM_KOL_POOLS.gold.minAmount
          }
        }
      },
      tFromRequest(req, "success.getKolStatus")
    ));

  } catch (error) {
    console.error("获取 KOL 状态失败:", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getKolStatusFailed"),
      (error as Error).message
    ));
  }
});

/**
 * 获取用户的宝箱奖励信息
 */
//@ts-ignore
router.get("/chest-rewards", walletAuthMiddleware, async (req, res) => {
  try {
    const myReq = req as MyRequest;
    const userId = myReq.user!.userId;
    const walletId = myReq.user!.walletId;
    
    if (!walletId) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, "errors.walletNotFound")
      ));
    }
    
    // 获取用户未打开的宝箱数量
    const availableChests = await chestCount(walletId);
    
    // 获取用户所有宝箱记录
    const chests = await Chest.findAll({
      where: { walletId },
      order: [['createdAt', 'DESC']],
      limit: 100 // 限制返回最近的100条记录
    });
    
    // 获取已打开的宝箱数量
    const openedChests = chests.filter(chest => chest.isOpened).length;
    
    // 获取已开启宝箱的奖励详情
    const openedChestIds = chests.filter(chest => chest.isOpened).map(chest => chest.id);
    
    // 从钱包历史记录中获取宝箱奖励信息
    const chestRewards = await WalletHistory.findAll({
      where: {
        walletId,
        reference: 'Chest Reward',
        category: {
          [Op.in]: ['ticket', 'ton', 'gem', 'diamond', 'fragment_green','fragment_blue','fragment_purple','fragment_gold'] // 查询所有奖励类型
        }
      },
      order: [['createdAt', 'DESC']],
      limit: 100 // 限制查询数量
    });
    
    // 按照奖励类型统计已开启宝箱的奖励
    const rewardSummary = {
      ticket: 0,
      ton: 0,
      gem: 0,
      diamond: 0,
      fragment_green: 0,
      fragment_blue: 0,
      fragment_purple: 0,
      fragment_gold: 0
    };

    chestRewards.forEach(reward => {
      if (rewardSummary.hasOwnProperty(reward.currency)) {
        // @ts-ignore
        rewardSummary[reward.currency] += Number(reward.amount);
      }
    });
    
    // 获取最近10个已开启宝箱的详细信息
    const recentOpenedChests = [];
    
    for (const chest of chests.filter(c => c.isOpened).slice(0, 10)) {
      // 查找该宝箱对应的奖励记录
      const rewards = chestRewards.filter(reward => 
        reward.developer_remark && reward.developer_remark.includes(`宝箱ID: ${chest.id}`)
      );
      
      if (rewards.length > 0) {
        recentOpenedChests.push({
          id: chest.id,
          openedAt: chest.updatedAt,
          rewards: rewards.map(reward => ({
            type: reward.currency,
            amount: reward.amount
          }))
        });
      }
    }
    
    return res.json(successResponse(
      {
        availableChests,
        openedChests,
        totalChests: chests.length,
        rewardSummary,
        recentOpenedChests
      },
      tFromRequest(req, "success.getChestRewards")
    ));
  } catch (error) {
    console.error("获取宝箱奖励失败:", error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, "errors.getChestRewardsFailed"),
      (error as Error).message
    ));
  }
});

export default router;
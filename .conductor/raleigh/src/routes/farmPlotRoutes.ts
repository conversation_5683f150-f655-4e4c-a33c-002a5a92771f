import express from 'express';
import farmPlotController from '../controllers/farmPlotController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';

const router = express.Router();

// 所有路由都需要用户认证
router.use(walletAuthMiddleware);

// 获取用户的所有牧场区
router.get('/farm-plots', farmPlotController.getUserFarmPlots);

// 升级牧场区
router.post('/farm-plots/upgrade', farmPlotController.upgradeFarmPlot);

// 解锁牧场区
router.post('/farm-plots/unlock', farmPlotController.unlockFarmPlot);

// 收集牧场区的牛奶
router.post('/farm-plots/collect', farmPlotController.collectMilk);

// 收集所有牧场区的牛奶
router.post('/farm-plots/collect-all', farmPlotController.collectAllMilk);

// 计算离线收益
router.post('/farm-plots/offline-earnings', farmPlotController.calculateOfflineEarnings);

// 添加牛奶到生产线
router.post('/farm-plots/add-milk', farmPlotController.addMilkToProductionLine);

// 触发牛舍产生牛奶
router.post('/farm-plots/trigger-production', farmPlotController.triggerMilkProduction);

export default router;
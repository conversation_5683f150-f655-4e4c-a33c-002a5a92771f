import { Router, Request, Response } from 'express';
import { walletAuthMiddleware } from '../middlewares/walletAuth'; // 导入 walletAuth 中间件
import { ajv, tFromRequest, formatValidationErrors } from '../i18n';
import { languageMiddleware } from '../middlewares/languageMiddleware';
import { successResponse, errorResponse } from '../utils/responseUtil';
import { web3Login, generateSignMessage, updateUsername } from '../services/web3AuthService';
import { v4 as uuidv4 } from 'uuid';
import { redis } from '../config/redis';

const router = Router();

// 应用语言中间件到所有路由
router.use(languageMiddleware);

// 定义获取nonce请求体验证模式
const getNonceSchema = {
  type: 'object',
  properties: {
    walletAddress: { type: 'string' }
  },
  required: ['walletAddress']
};

const validateGetNonce = ajv.compile(getNonceSchema);

// 定义登录请求体验证模式
const loginSchema = {
  type: 'object',
  properties: {
    walletAddress: { type: 'string' },
    signature: { type: 'string' },
    message: { type: 'string' },
    referralCode: { type: 'string' }
  },
  required: ['walletAddress', 'signature', 'message']
};

const validateLogin = ajv.compile(loginSchema);

// 定义更新用户名请求体验证模式
const updateUsernameSchema = {
  type: 'object',
  properties: {
    newUsername: { type: 'string', minLength: 3, maxLength: 30 } // 添加校验规则
  },
  required: ['newUsername']
};

const validateUpdateUsername = ajv.compile(updateUsernameSchema);

/**
 * 获取用于签名的nonce
 * 路径: /api/web3-auth/nonce
 * 方法: POST
 */
//@ts-ignore
router.post('/nonce', async (req: Request, res: Response) => {
  try {
    // 验证请求体
    const valid = validateGetNonce(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.paramValidation'),
        formatValidationErrors(validateGetNonce.errors || [], req.language)
      ));
    }

    const { walletAddress } = req.body;
    
    // 生成随机nonce
    const nonce = uuidv4();
    
    // 将nonce存储到Redis，设置5分钟过期
    const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;
    await redis.set(key, nonce, 'EX', 300);
    
    // 生成签名消息
    const message = generateSignMessage(nonce);
    
    return res.json(successResponse({
      nonce,
      message
    }));
  } catch (error: any) {
    console.error('获取nonce失败:', error);
    return res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError'),
      error.message
    ));
  }
});

/**
 * Web3钱包登录
 * 路径: /api/web3-auth/login
 * 方法: POST
 */

//@ts-ignore
router.post('/login', async (req: Request, res: Response) => {
  try {
    // 验证请求体
    const valid = validateLogin(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.paramValidation'),
        formatValidationErrors(validateLogin.errors || [], req.language)
      ));
    }

    const { walletAddress, signature, message, referralCode } = req.body;
    
    // 验证nonce是否有效
    const key = `web3_auth_nonce:${walletAddress.toLowerCase()}`;
    const storedNonce = await redis.get(key);
    
    if (!storedNonce || !message.includes(storedNonce)) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.invalidNonce')
      ));
    }
    
    // 登录处理
    const result = await web3Login(walletAddress, signature, message, referralCode);
    
    // 登录成功后删除nonce
    await redis.del(key);
    
    return res.json(successResponse({
      token: result.token,
      user: {
        id: result.user.id,
        username: result.user.username,
        walletAddress: result.wallet.walletAddress
      }
    }));
  } catch (error: any) {
    console.error('Web3登录失败:', error);
    return res.status(400).json(errorResponse(
      tFromRequest(req, 'errors.loginFailed'),
      error.message
    ));
  }
});

/**
 * 更新用户名
 * 路径: /api/web3-auth/update-username
 * 方法: POST
 * 需要认证: 是 (walletAuth)
 */
//@ts-ignore
router.post('/update-username', walletAuthMiddleware, async (req: Request, res: Response) => {
  try {
    // @ts-ignore
    const userId = req.user.userId; // 从认证中间件获取用户ID

    console.log('userId',userId);
    

    // 验证请求体
    const valid = validateUpdateUsername(req.body);
    if (!valid) {
      return res.status(400).json(errorResponse(
        tFromRequest(req, 'errors.paramValidation'),
        formatValidationErrors(validateUpdateUsername.errors || [], req.language)
      ));
    }

    const { newUsername } = req.body;

    // 更新用户名
    const updatedUser = await updateUsername(userId, newUsername);

    return res.json(successResponse({
      id: updatedUser.id,
      username: updatedUser.username
    }));
  } catch (error: any) {
    console.error('更新用户名失败:', error);
    // 根据错误类型返回不同的错误信息
    if (error.message === tFromRequest(req, 'errors.usernameAlreadyExists') || error.message === tFromRequest(req, 'errors.userNotFound')) {
      return res.status(400).json(errorResponse(error.message));
    }
    return res.status(500).json(errorResponse(
      tFromRequest(req, 'errors.serverError'),
      error.message
    ));
  }
});

export default router;
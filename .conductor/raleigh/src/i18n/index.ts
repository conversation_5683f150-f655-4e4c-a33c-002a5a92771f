// src/i18n/index.ts
import Polyglot from 'node-polyglot';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import localize from 'ajv-i18n';
import en from './locales/en';
import zh from './locales/zh';
import ja from './locales/ja';

// 创建 AJV 实例
export const ajv = new Ajv({ allErrors: true });
// 添加格式验证支持
addFormats(ajv);

// 添加自定义关键字，用于获取当前请求的语言
import { getCurrentRequestLanguage } from './requestContext';

// 添加自定义关键字 i18n，可以在验证函数中获取当前请求的语言
ajv.addKeyword({
  keyword: 'i18n',
  validate: function i18nValidate(schema: boolean, data: any) {
    // 从请求上下文中获取当前语言
    const language = getCurrentRequestLanguage();
    // 可以在这里根据语言进行自定义验证逻辑
    // schema 参数可以包含特定于语言的验证规则
    return true; // 默认返回true，不影响验证结果
  },
  // 确保这个关键字不会影响数据验证结果
  metaSchema: { type: 'boolean' }
});

// 创建 Polyglot 实例
const polyglot = new Polyglot();

// 支持的语言
export const SUPPORTED_LANGUAGES = ['en', 'zh', 'ja'] as const;
export type SupportedLanguage = typeof SUPPORTED_LANGUAGES[number];

// 当前语言（默认英语）
let currentLanguage: SupportedLanguage = 'en';

/**
 * 规范化语言包
 * 处理语言包的路径格式化和默认导出问题
 */
function normalize(locales: Record<string, any>) {
  const normalized = Object.entries(locales).map(([key, val]) => [
    key.split('/').pop()?.split('.').slice(0, -1).join('.') || key,
    val.default ? val.default : val,
  ]);
  return Object.fromEntries(normalized);
}

// 语言包
const phrases = normalize({
  en,
  zh,
  ja
});

/**
 * 查找最匹配的语言
 * 支持部分匹配如 zh 和 zh-CN
 */
export function findBestMatchingLanguage(lang: string): SupportedLanguage {
  if (!lang) return 'en';
  
  const langs = Object.keys(phrases);
  const exactMatch = langs.find(item => item === lang);
  if (exactMatch) return exactMatch as SupportedLanguage;
  
  const partialMatch = langs.find(item => item.startsWith(lang) || lang.startsWith(item));
  return (partialMatch || 'en') as SupportedLanguage;
}

// 获取当前语言
export function getCurrentLanguage(): SupportedLanguage {
  return getCurrentRequestLanguage();
}

// 设置语言
export function setLanguage(lang: string | SupportedLanguage = 'en') {
  const bestMatch = findBestMatchingLanguage(lang);
  polyglot.locale(bestMatch);
  polyglot.replace(phrases[bestMatch]);
  currentLanguage = bestMatch; // 更新当前语言变量
  return bestMatch;
}

// 获取翻译
export function t(key: string, options?: Polyglot.InterpolationOptions, lang?: SupportedLanguage) {
  // 确保options是一个对象
  const safeOptions = options || {};
  
  // 如果提供了特定语言，临时切换到该语言
  if (lang && SUPPORTED_LANGUAGES.includes(lang)) {
    const currentLocale = polyglot.locale();
    polyglot.locale(lang);
    polyglot.replace(phrases[lang]);
    
    // 使用安全的options对象
    const translation = polyglot.t(key, safeOptions);
    
    // 恢复原来的语言
    polyglot.locale(currentLocale);
    polyglot.replace(phrases[currentLocale as SupportedLanguage]);
    return translation;
  }
  
  // 如果没有提供语言参数，尝试从AsyncLocalStorage获取当前请求的语言
  const requestLanguage = getCurrentRequestLanguage();

  if (requestLanguage && requestLanguage !== currentLanguage) {
    // 如果请求上下文中的语言与当前语言不同，临时切换语言
    const currentLocale = polyglot.locale();
    polyglot.locale(requestLanguage);
    polyglot.replace(phrases[requestLanguage]);
    
    // 使用安全的options对象
    const translation = polyglot.t(key, safeOptions);
    
    // 恢复原来的语言
    polyglot.locale(currentLocale);
    polyglot.replace(phrases[currentLocale as SupportedLanguage]);
    return translation;
  }
  
  // 使用安全的options对象
  return polyglot.t(key, safeOptions);
}

// 从请求对象获取翻译
export function tFromRequest(req: any, key: string, options?: Polyglot.InterpolationOptions) {
  if (req && req.language) {
    return t(key, options, req.language);
  }  
  return t(key, options, getCurrentLanguage()); // 使用当前语言作为默认值
}

// 处理验证错误
export function formatValidationErrors(errors: any[], lang: string | SupportedLanguage = 'en') {
  if (!errors) return [];
  
  const bestMatch = findBestMatchingLanguage(lang || getCurrentLanguage());
  
  // 根据语言本地化错误信息
  localize[bestMatch](errors);
  
  return errors.map(error => ({
    field: error.instancePath,
    message: error.message
  }));
}

// 初始化默认语言
setLanguage('en');
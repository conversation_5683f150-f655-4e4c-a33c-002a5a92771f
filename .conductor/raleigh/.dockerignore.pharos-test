# Pharos Test 分支 Docker 构建忽略文件

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 环境文件（除了 .env.pharos-test）
.env
.env.local
.env.development
.env.test
.env.production
.env_prod

# 日志文件
logs/
*.log
pids/
*.pid
*.seed
*.pid.lock

# 运行时数据
lib-cov/
coverage/
.nyc_output/
.grunt/
.lock-wscript/

# 依赖目录
build/
dist/
.tmp/
.cache/

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore*

# 数据库文件
mysql-data/
mysql-data-pharos-test/
*.sql
*.db
*.sqlite

# 备份文件
*.bak
*.backup
*.old

# 测试文件
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档
docs/
README*.md
CHANGELOG.md
LICENSE

# 脚本文件
scripts/
*.sh

# 配置文件
.eslintrc*
.prettierrc*
tsconfig.json
jest.config.js

# 临时文件
tmp/
temp/
.tmp/
